// Fidget Spinner Game UI Controller
let game;

document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('game-canvas');
    game = new FidgetSpinnerGame(canvas);
    
    // Button event listeners
    document.getElementById('canvas-start-btn').addEventListener('click', startGame);
    document.getElementById('reset-btn').addEventListener('click', resetGame);
    document.getElementById('style-btn').addEventListener('click', changeStyle);
    document.getElementById('sound-btn').addEventListener('click', toggleSound);
    document.getElementById('home-btn').addEventListener('click', goHome);
    
    // Close game start modal when clicking outside
    document.getElementById('game-start').addEventListener('click', function(e) {
        if (e.target === this) {
            startGame();
        }
    });
});

function startGame() {
    // Hide start overlay
    document.getElementById('game-start').classList.add('hidden');
    // Enable game
    game.start();
}

function resetGame() {
    game.reset();
    // Add visual feedback
    const btn = document.getElementById('reset-btn');
    btn.style.transform = 'scale(0.95)';
    setTimeout(() => {
        btn.style.transform = 'scale(1)';
    }, 100);
}

function changeStyle() {
    const styleName = game.changeStyle();
    const btn = document.getElementById('style-btn');
    btn.textContent = styleName;
    
    // Add visual feedback
    btn.style.transform = 'scale(0.95)';
    setTimeout(() => {
        btn.style.transform = 'scale(1)';
    }, 100);
}

function toggleSound() {
    const soundEnabled = game.toggleSound();
    const btn = document.getElementById('sound-btn');
    btn.textContent = soundEnabled ? '🔊 Sound' : '🔇 Mute';
    
    // Add visual feedback
    btn.style.transform = 'scale(0.95)';
    setTimeout(() => {
        btn.style.transform = 'scale(1)';
    }, 100);
}

function goHome() {
    window.location.href = '/';
}

// Handle window resize
window.addEventListener('resize', function() {
    if (game) {
        // Redraw the game when window is resized
        game.draw();
    }
});

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (!game.gameStarted) return;
    
    switch(e.key) {
        case 'r':
        case 'R':
            resetGame();
            break;
        case 's':
        case 'S':
            toggleSound();
            break;
        case ' ':
            e.preventDefault();
            changeStyle();
            break;
    }
});
