
let game;

document.addEventListener('DOMContentLoaded', function() {
    game = new Game2048();
    

    game.updateDisplay();
    game.renderBoard();
    
    // Button event listeners
    document.getElementById('new-game-btn').addEventListener('click', newGame);
    document.getElementById('canvas-start-btn').addEventListener('click', startGame);
    document.getElementById('undo-btn').addEventListener('click', undoMove);
    document.getElementById('restart-btn').addEventListener('click', restartGame);
    document.getElementById('continue-btn').addEventListener('click', continueGame);
    document.getElementById('home-btn').addEventListener('click', goHome);
    
    // Close game over modal when clicking outside
    document.getElementById('game-over').addEventListener('click', function(e) {
        if (e.target === this) {
            closeGameOver();
        }
    });
});

function newGame() {
    game.newGame();
    showStartOverlay();
}

function startGame() {
    // Hide start overlay
    document.getElementById('game-start').classList.add('hidden');
    // Enable game controls
    game.gameStarted = true;
}

function showStartOverlay() {
    document.getElementById('game-start').classList.remove('hidden');
}

function undoMove() {
    if (game.canUndo()) {
        game.undo();
    }
}

function restartGame() {
    game.newGame();
    closeGameOver();
    showStartOverlay();
}

function continueGame() {
    game.continueGame();
}

function goHome() {
    window.location.href = '/';
}

function closeGameOver() {
    document.getElementById('game-over').classList.add('hidden');
}

// Handle window resize
window.addEventListener('resize', function() {
    if (game) {
        game.renderBoard();
    }
});

// Prevent default behavior for arrow keys
document.addEventListener('keydown', function(e) {
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
        e.preventDefault();
    }
});

// Add visual feedback for moves
document.addEventListener('keydown', function(e) {
    if (!game || game.gameOver) return;
    
    const gameBoard = document.getElementById('game-board');
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'w', 'a', 's', 'd'].includes(e.key.toLowerCase())) {
        gameBoard.style.transform = 'scale(0.98)';
        setTimeout(() => {
            gameBoard.style.transform = 'scale(1)';
        }, 100);
    }
});

// Update board rendering after moves
const originalMove = Game2048.prototype.move;
Game2048.prototype.move = function(direction) {
    const moved = originalMove.call(this, direction);
    if (moved) {
        setTimeout(() => {
            this.renderBoard();
        }, 50);
    }
    return moved;
};

// Add tile animations
const originalRenderBoard = Game2048.prototype.renderBoard;
Game2048.prototype.renderBoard = function() {
    originalRenderBoard.call(this);
    
    // Add animation classes to new tiles
    const tiles = document.querySelectorAll('.tile');
    tiles.forEach(tile => {
        if (!tile.classList.contains('tile-animated')) {
            tile.classList.add('tile-new');
            tile.classList.add('tile-animated');
            
            setTimeout(() => {
                tile.classList.remove('tile-new');
            }, 200);
        }
    });
};

// Add score animation
const originalUpdateDisplay = Game2048.prototype.updateDisplay;
Game2048.prototype.updateDisplay = function() {
    const oldScore = parseInt(document.getElementById('score').textContent) || 0;
    originalUpdateDisplay.call(this);
    
    const newScore = this.score;
    if (newScore > oldScore) {
        const scoreElement = document.getElementById('score');
        scoreElement.style.transform = 'scale(1.1)';
        scoreElement.style.color = '#f67c5f';
        
        setTimeout(() => {
            scoreElement.style.transform = 'scale(1)';
            scoreElement.style.color = '#776e65';
        }, 200);
    }
};
