/* Reaction Test Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
    min-height: 100vh;
    color: white;
    overflow-x: hidden;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
}

.header h1 {
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin: 0;
}

.home-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.home-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Main Game Area */
.main-game-area {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

/* Statistics Panel */
.stats-panel {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    margin-bottom: 25px;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
}

.stat-item {
    text-align: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    transition: transform 0.2s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-label {
    display: block;
    font-size: 0.85rem;
    opacity: 0.8;
    margin-bottom: 5px;
    font-weight: 500;
}

.stat-value {
    display: block;
    font-size: 1.4rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    color: #fff;
}

/* Start Test Button */
.start-test-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.start-test-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.start-test-btn:active {
    transform: translateY(-1px);
}

.start-test-btn.hidden {
    display: none;
}

/* Control Panel */
.control-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    gap: 15px;
}

.control-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.control-btn.secondary {
    background: linear-gradient(135deg, #FF6B6B, #FF5252);
    color: white;
}

.control-btn.tertiary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.test-area {
    width: 100%;
    height: 350px;
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.test-area.waiting {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-color: rgba(255, 255, 255, 0.3);
}

.test-area.ready {
    background: linear-gradient(135deg, #dc3545, #c82333);
    animation: pulse 1s ease-in-out infinite;
    border-color: #dc3545;
    box-shadow: 0 0 30px rgba(220, 53, 69, 0.4);
}

.test-area.go {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 0 50px rgba(40, 167, 69, 0.6);
    border-color: #28a745;
    transform: scale(1.02);
}

.test-area.too-early {
    background: linear-gradient(135deg, #fd7e14, #e55353);
    animation: shake 0.5s ease-in-out;
    border-color: #fd7e14;
}

.test-area.result {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border-color: #17a2b8;
    box-shadow: 0 0 30px rgba(23, 162, 184, 0.4);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

.test-content {
    text-align: center;
    z-index: 2;
    padding: 20px;
    max-width: 90%;
}

.test-message {
    font-size: 2.2rem;
    font-weight: bold;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    color: white;
}

.test-instruction {
    font-size: 1.1rem;
    opacity: 0.95;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    color: white;
    line-height: 1.4;
    margin-bottom: 10px;
}

/* Results Section */
.results-section {
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    overflow: hidden;
    max-height: 0;
    opacity: 0;
    margin-top: 0;
}

.results-section.hidden {
    max-height: 0;
    opacity: 0;
    padding: 0 25px;
    margin-bottom: 0;
}

.results-section:not(.hidden) {
    max-height: 500px;
    opacity: 1;
    margin-top: 20px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 10px;
}

.results-header h3 {
    font-size: 1.4rem;
    margin: 0;
    color: white;
}

.results-count {
    font-size: 0.9rem;
    opacity: 0.8;
    color: #4ECDC4;
    font-weight: 500;
}

.results-list {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 10px;
}

.results-list::-webkit-scrollbar {
    width: 6px;
}

.results-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.result-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    align-items: center;
    gap: 15px;
    padding: 12px 15px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.result-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
}

.result-time {
    font-weight: bold;
    font-size: 1.2rem;
    color: white;
}

.result-rating {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.result-timestamp {
    font-size: 0.85rem;
    opacity: 0.7;
    color: #ccc;
}

.rating-excellent {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.rating-good {
    background: linear-gradient(135deg, #ffc107, #ffb300);
    color: #212529;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.rating-average {
    background: linear-gradient(135deg, #fd7e14, #f57c00);
    color: white;
    box-shadow: 0 2px 8px rgba(253, 126, 20, 0.3);
}

.rating-poor {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.result-item:has(.rating-excellent) {
    border-left-color: #28a745;
}

.result-item:has(.rating-good) {
    border-left-color: #ffc107;
}

.result-item:has(.rating-average) {
    border-left-color: #fd7e14;
}

.result-item:has(.rating-poor) {
    border-left-color: #dc3545;
}

.no-results {
    text-align: center;
    opacity: 0.7;
    font-style: italic;
    padding: 40px 20px;
    color: #ccc;
    font-size: 1.1rem;
}

/* Instructions */
.instructions {
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 15px;
    
}

.instructions h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.instruction-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
}

.instruction-item .icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.instruction-item .desc {
    font-size: 0.9rem;
    line-height: 1.4;
}

.tips {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #4ECDC4;
}

.tips h4 {
    margin-bottom: 15px;
    color: #4ECDC4;
    font-size: 1.2rem;
}

.tips ul {
    list-style: none;
    padding: 0;
}

.tips li {
    padding: 5px 0;
    padding-left: 20px;
    position: relative;
    opacity: 0.9;
}

.tips li:before {
    content: "💡";
    position: absolute;
    left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header {
        flex-direction: column;
        gap: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .main-game-area {
        padding: 20px;
    }

    .stats-panel {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        padding: 15px;
    }

    .stat-item {
        padding: 8px;
    }

    .stat-value {
        font-size: 1.2rem;
    }

    .test-area {
        height: 280px;
    }

    .test-message {
        font-size: 1.6rem;
    }

    .test-instruction {
        font-size: 1rem;
    }

    .start-test-btn {
        padding: 12px 25px;
        font-size: 1rem;
    }

    .control-panel {
        flex-direction: column;
        gap: 10px;
    }

    .control-btn {
        width: 100%;
        padding: 12px;
    }

    .result-item {
        grid-template-columns: 1fr;
        gap: 8px;
        text-align: center;
    }

    .instruction-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .main-game-area {
        padding: 15px;
    }

    .stats-panel {
        grid-template-columns: 1fr;
        gap: 8px;
        padding: 12px;
    }

    .stat-item {
        padding: 10px;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .stat-value {
        font-size: 1.1rem;
    }

    .test-area {
        height: 250px;
    }

    .test-message {
        font-size: 1.4rem;
    }

    .test-instruction {
        font-size: 0.9rem;
    }

    .start-test-btn {
        padding: 10px 20px;
        font-size: 0.95rem;
    }

    .results-header {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .result-item {
        padding: 10px;
    }

    .result-time {
        font-size: 1.1rem;
    }
}
