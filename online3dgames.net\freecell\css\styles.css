* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.landscape-prompt {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #1a4d2e;
    z-index: 10000;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
}

.landscape-content {
    max-width: 400px;
}

.rotate-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: rotate 2s infinite ease-in-out;
}

@keyframes rotate {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(90deg); }
}

.landscape-content h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

.landscape-content p {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1a4d2e;
    min-height: 100vh;
    color: #fff;
    overflow-x: auto;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Game Container */
.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    min-height: 100dvh;
}

/* Header Styles */
.game-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.header-left h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.game-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.stat {
    background: rgba(255,255,255,0.1);
    padding: 8px 16px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.stat-label {
    font-weight: bold;
    margin-right: 5px;
}

.header-right {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    box-shadow: none;
    outline: none;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #ee5a24, #ff6b6b);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#fullscreenBtn {
    font-size: 18px;
    padding: 8px 12px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#fullscreenBtn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

:fullscreen {
    background: #1a4d2e;
}

:-webkit-full-screen {
    background: #1a4d2e;
}

:-moz-full-screen {
    background: #1a4d2e;
}

:-ms-fullscreen {
    background: #1a4d2e;
}

.game-board {
    display: grid;
    grid-template-areas:
        "foundation stock"
        "tableau tableau";
    grid-template-columns: 1fr auto;
    gap: 30px;
    margin-bottom: 30px;
}

.foundation-area {
    grid-area: foundation;
    display: flex;
    gap: 15px;
    justify-content: flex-start;
}

.foundation-pile {
    width: 132px;
    height: 185px;
    border: 2px dashed rgba(255,255,255,0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.1);
    position: relative;
}

.pile-placeholder {
    font-size: 24px;
    opacity: 0.5;
    font-weight: bold;
}

.stock-area {
    grid-area: stock;
    display: flex;
    gap: 8px;
    align-items: flex-start;
    position: relative;
    perspective: 1000px;
    padding: 10px;
}

.stock-pile, .waste-pile {
    width: 100px;
    height: 145px;
    border-radius: 8px;
    position: relative;
    cursor: pointer;
    transform-style: preserve-3d;
    transition: all 0.3s ease;
}

.stock-pile {
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(255,255,255,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        2px 0 4px rgba(0,0,0,0.3),
        4px 0 8px rgba(0,0,0,0.2),
        6px 0 12px rgba(0,0,0,0.1);
}

.stock-pile:hover {
    background: rgba(255,255,255,0.15);
    border-color: rgba(255,255,255,0.4);
    box-shadow:
        2px 0 6px rgba(0,0,0,0.4),
        4px 0 10px rgba(0,0,0,0.3),
        6px 0 14px rgba(0,0,0,0.2);
}

.card-back {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #8B0000, #DC143C);
    border-radius: 6px;
    border: 2px solid #fff;
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 0 10px rgba(0,0,0,0.3);
}

.card-back::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255,255,255,0.1) 2px,
        rgba(255,255,255,0.1) 4px
    );
    border-radius: 4px;
}

.waste-pile {
    background: rgba(255,255,255,0.05);
    border: 2px solid rgba(255,255,255,0.2);
    box-shadow:
        -2px 0 4px rgba(0,0,0,0.3),
        -4px 0 8px rgba(0,0,0,0.2),
        -6px 0 12px rgba(0,0,0,0.1);
}

.waste-pile:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
}

.tableau-area {
    grid-area: tableau;
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.tableau-pile {
    width: 132px;
    min-height: 185px;
    border: 2px dashed rgba(255,255,255,0.2);
    border-radius: 8px;
    background: rgba(255,255,255,0.05);
    position: relative;
    display: flex;
    justify-content: center;
}

/* Card Styles */
.card {
    width: 120px;
    height: 170px;
    background: white;
    border-radius: 8px;
    border: 1px solid #ccc;
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 6px;
    font-size: 18px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.card.face-down {
    background: linear-gradient(45deg, #8B0000, #DC143C);
    color: transparent;
}

.card.face-down::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255,255,255,0.1) 2px,
        rgba(255,255,255,0.1) 4px
    );
    border-radius: 4px;
}

.card.red {
    color: #dc143c;
}

.card.black {
    color: #000;
}

.card-top, .card-bottom {
    display: flex;
    align-items: center;
    gap: 2px;
}

.card-bottom {
    transform: rotate(180deg);
    align-self: flex-end;
}

.card-center {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
}

/* Dragging States */
.card.dragging {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 12px 24px rgba(0,0,0,0.6);
    transition: none !important;
}

.card.drag-placeholder {
    opacity: 0.3;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px dashed rgba(255, 255, 255, 0.5) !important;
}

.card.draggable-card {
    cursor: grab;
    transition: all 0.2s ease;
}



.card.draggable-card:active {
    cursor: grabbing;
}

.tableau-pile .card.dragging ~ .card.dragging {
    z-index: 9998 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 8px 16px rgba(0,0,0,0.5);
}





.card.book-flip {
    transform-style: preserve-3d;
    transition: transform 0.6s ease;
    transform-origin: right center;
}

.card.book-flip-start {
    transform: rotateY(0deg);
}

.card.book-flip-middle {
    transform: rotateY(90deg);
}

.card.book-flip-end {
    transform: rotateY(180deg);
}

.waste-pile .card {
    box-shadow:
        -2px 0 4px rgba(0,0,0,0.3),
        -1px 0 2px rgba(0,0,0,0.2);
}

.stock-pile .card-back {
    box-shadow:
        2px 0 4px rgba(0,0,0,0.3),
        1px 0 2px rgba(0,0,0,0.2),
        inset 0 0 10px rgba(0,0,0,0.3);
}

.stock-count {
    font-family: 'Arial', sans-serif;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    border: 1px solid rgba(255,255,255,0.3);
}

/* Flip animation */
.card.flipping {
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.card.flip-start {
    transform: rotateY(180deg);
}

.card.flip-middle {
    transform: rotateY(90deg);
}

.card.flip-end {
    transform: rotateY(0deg);
}

.card.flying {
    z-index: 10000 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
}

.card.flying-fast {
    z-index: 10000 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.08s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
}

.card.moving-to-foundation {
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
    transition: all 0.15s ease;
}

.card.moving-to-tableau {
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.6);
    transition: all 0.3s ease;
}

.card.card-returning {
    z-index: 8888 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.3s ease-out !important;
}



/* Game Messages */
.game-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.message-content {
    background: linear-gradient(135deg, #1a3d0a, #2d5016, #4a7c59);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 450px;
    border: 3px solid #7fb069;
    box-shadow:
        0 0 0 1px rgba(255,255,255,0.1),
        0 10px 30px rgba(0,0,0,0.7),
        inset 0 1px 0 rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
}

.message-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #7fb069, transparent);
}

.message-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #7fb069, transparent);
}

.message-content h2 {
    margin-bottom: 20px;
    font-size: 2rem;
    color: #fff;
}

.message-content p {
    margin-bottom: 20px;
    font-size: 1.1rem;
    color: rgba(255,255,255,0.9);
}

.message-stats {
    margin: 20px 0;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 10px;
}

.message-stats div {
    background: rgba(255,255,255,0.1);
    padding: 10px 15px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    line-height: 1.2;
}

.message-stats div:hover {
    background: rgba(255,255,255,0.15);
    transform: translateY(-1px);
}

.message-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.message-buttons .btn {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 10px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.message-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.4);
}

/* Help Panel */
.help-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.help-content {
    background: linear-gradient(135deg, #2d5016, #4a7c59);
    padding: 0;
    border-radius: 20px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    border: 2px solid rgba(255,255,255,0.2);
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.help-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    background: rgba(255,255,255,0.1);
}

.help-header h3 {
    margin: 0;
    font-size: 1.6rem;
    color: #fff;
}

.help-body {
    padding: 20px 30px;
    overflow-y: auto;
    flex: 1;
}

.help-footer {
    padding: 20px 30px;
    border-top: 1px solid rgba(255,255,255,0.2);
    background: rgba(255,255,255,0.1);
    text-align: center;
}

.help-section {
    margin-bottom: 25px;
}

.help-section h4 {
    margin-bottom: 10px;
    color: #fff;
    font-size: 1.2rem;
    border-bottom: 1px solid rgba(255,255,255,0.3);
    padding-bottom: 5px;
}

.help-section p {
    color: rgba(255,255,255,0.9);
    line-height: 1.5;
    margin-bottom: 10px;
}

.help-section ul {
    color: rgba(255,255,255,0.9);
    padding-left: 20px;
}

.help-section li {
    margin-bottom: 8px;
    line-height: 1.4;
}

.help-section strong {
    color: #fff;
}

.close-btn {
    background: none;
    border: none;
    color: rgba(255,255,255,0.7);
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
    color: #fff;
}

/* Hidden Class */
.hidden {
    display: none !important;
}

/* Touch and Mobile Optimizations */
* {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.card {
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

/* Mobile viewport optimizations */
@media (max-width: 768px) {
    html, body {
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
    }

    body {
        position: fixed;
        width: 100%;
        height: 100%;
    }

    .game-container {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: none;
    }
}

.card.draggable-card {
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

@media (max-width:1024px){
    .game-header {
        margin-bottom: 0;
        gap: 5px;
    }

    .game-container{
        padding: 10px;
    }

    .header-left h1{
        font-size: 1.5rem;
    }
    .btn,.stat{
        padding: 5px 10px;
        font-size: 12px;
    }

    #fullscreenBtn {
        font-size: 14px;
        padding: 5px 8px;
        min-width: 36px;
    }

    .game-board{
        gap: 10px;
    }

    .tableau-area{
        margin-top: 0;
    }

    .foundation-pile,.stock-pile, .waste-pile,.tableau-pile,.card{
        width: 66px;
        height: 93px;
        font-size: 14px;
        min-height: 93px;
    }
    .card{
        padding: 0 2px;
    }
}

/* Improve touch targets and interactions for mobile */
@media (max-width: 768px) {
    .card {
        min-height: 60px;
        min-width: 40px;
        cursor: pointer;
        -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
    }

    .card.draggable-card:hover:not(.dragging) {
        transform: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .card.dragging {
        box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        z-index: 1000;
        opacity: 0.9;
    }

    /* Better touch feedback */
    .card:active {
        transition: transform 0.1s ease;
    }

    .tableau-pile {
        min-height: 200px;
        position: relative;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .help-content {
        max-width: 95%;
        max-height: 95vh;
    }

    .help-header, .help-body, .help-footer {
        padding: 15px 20px;
    }

    .help-header h3 {
        font-size: 1.3rem;
    }

    .help-section h4 {
        font-size: 1.1rem;
    }

    .help-section ul {
        padding-left: 15px;
    }
}

/* Hint Styles */
.card-hint {
    animation: pulse 1s infinite;
    border: 2px solid #ffff00 !important;
    box-shadow: 0 0 15px rgba(255, 255, 0, 0.7) !important;
}



@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Auto-complete animation */
.auto-move {
    animation: autoMove 0.5s ease-in-out;
}

@keyframes autoMove {
    0% { transform: scale(1); }
    50% { transform: scale(1.1) rotate(5deg); }
    100% { transform: scale(1); }
}

@media screen and (max-width: 768px) and (orientation: portrait) {
    .landscape-prompt {
        display: flex;
    }

    .game-container {
        display: none;
    }
}

@media screen and (max-width: 768px) and (orientation: landscape) {
    .landscape-prompt {
        display: none;
    }

    .game-container {
        display: block;
        padding: 5px;
        height: 100vh;
        overflow: hidden;
    }

    .game-header {
        padding: 4px 8px;
        margin-bottom: 4px;
    }
}



body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1a4d2e;
    min-height: 100vh;
    color: #fff;
    overflow-x: auto;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Game Container */
.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    min-height: 100dvh;
}

/* Header Styles */
.game-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.header-left h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.game-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.stat {
    background: rgba(255,255,255,0.1);
    padding: 8px 16px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.stat-label {
    font-weight: bold;
    margin-right: 5px;
}

.header-right {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: center;
}

/* Button Styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    backdrop-filter: blur(10px);
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #ff5252, #e53e3e);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.btn-secondary {
    background: rgba(255,255,255,0.1);
    color: rgba(255,255,255,0.9);
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.2);
    color: white;
    border-color: rgba(255,255,255,0.4);
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Game Board */
.game-board {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin-bottom: 30px;
    position: relative;
    min-height: 70vh;
}

/* Card Styles - Desktop */
.card {
    width: 120px;
    height: 170px;
    background: white;
    border-radius: 8px;
    border: 1px solid #ccc;
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 6px;
    font-size: 18px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.card.face-down {
    background: linear-gradient(45deg, #8B0000, #DC143C);
    color: transparent;
}

.card.face-down::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255,255,255,0.1) 2px,
        rgba(255,255,255,0.1) 4px
    );
    border-radius: 4px;
}

.card.red {
    color: #dc143c;
}

.card.black {
    color: #000;
}

.card-top, .card-bottom {
    display: flex;
    align-items: center;
    gap: 2px;
}

.card-bottom {
    transform: rotate(180deg);
    align-self: flex-end;
}

.card-center {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
}

/* Dragging States */
.card.dragging {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 12px 24px rgba(0,0,0,0.6);
    transform: scale(1.05);
    transition: none !important;
}

.card.drag-placeholder {
    opacity: 0.3;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px dashed rgba(255, 255, 255, 0.5) !important;
}

.card.draggable-card {
    cursor: grab;
    transition: all 0.2s ease;
}

.card.draggable-card:hover:not(.dragging) {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.card.draggable-card:active {
    cursor: grabbing;
}

.card.flying {
    z-index: 10000 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
    transform: scale(1.05);
}

.card.moving-to-foundation {
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
    transition: all 0.15s ease;
}

.card.moving-to-tableau {
    transform: scale(1.02);
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.6);
    transition: all 0.3s ease;
}

/* Pile Styles */
.pile {
    width: 100px;
    height: 145px;
    border: 2px dashed rgba(255,255,255,0.3);
    border-radius: 8px;
    background: rgba(255,255,255,0.05);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.pile-placeholder {
    color: rgba(255,255,255,0.5);
    font-size: 14px;
    font-weight: bold;
    text-align: center;
}

/* Remove pile hover effects for cleaner drag experience */



/* Game Messages */
.game-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.message-content {
    background: linear-gradient(135deg, #1a3d0a, #2d5016, #4a7c59);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.5);
    border: 2px solid rgba(255,255,255,0.1);
}

.message-content h2 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.message-content p {
    font-size: 1.1rem;
    margin-bottom: 20px;
    color: rgba(255,255,255,0.9);
}

.message-stats {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    padding: 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.message-stats div {
    text-align: center;
    color: rgba(255,255,255,0.9);
}

.message-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.hidden {
    display: none !important;
}

/* Help Panel */
.help-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.help-content {
    background: linear-gradient(135deg, #1a3d0a, #2d5016, #4a7c59);
    border-radius: 20px;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0,0,0,0.5);
    border: 2px solid rgba(255,255,255,0.1);
}

.help-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.help-header h3 {
    font-size: 1.5rem;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.close-btn {
    background: none;
    border: none;
    font-size: 2rem;
    color: rgba(255,255,255,0.7);
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #fff;
}

.help-body {
    padding: 30px;
}

.help-section {
    margin-bottom: 25px;
}

.help-section h4 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.help-section p, .help-section li {
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
    margin-bottom: 8px;
}

.help-section ul {
    padding-left: 20px;
}

.help-footer {
    padding: 20px 30px;
    border-top: 1px solid rgba(255,255,255,0.1);
    text-align: center;
}

/* Touch and Mobile Optimizations */
body {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.card {
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .game-header {
        margin-bottom: 0;
        gap: 8px;
        padding: 5px 10px;
    }

    .game-container{
        padding: 0px 8px;
    }

    .header-left h2{
        font-size: 1.4rem;
        margin: 0;
    }

    .game-stats {
        gap: 8px;
        flex-wrap: wrap;
    }

    .header-right {
        gap: 6px;
        flex-wrap: wrap;
    }

    .btn{
        padding: 5px 8px;
        font-size: 12px;
        height: 30px;
    }

    .game-board{
        gap: 12px;
        min-height: 65vh;
    }
}

/* Mobile viewport optimizations */
@media (max-width: 768px) {
    html, body {
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
    }

    body {
        position: fixed;
        width: 100%;
        height: 100%;
    }

    .game-container {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: none;
        height: 100vh;
        height: 100dvh;
    }

    .game-header {
        flex-direction: row;
        gap: 4px;
        padding: 4px 6px;
        justify-content: space-between;
        align-items: center;
    }

    .header-left {
        flex: 1;
        min-width: 0;
    }

    .header-right {
        flex: 2;
        justify-content: flex-end;
        gap: 3px;
        flex-wrap: nowrap;
        overflow-x: auto;
    }

    .game-stats {
        justify-content: flex-start;
        margin-top: 0;
        gap: 4px;
        flex-wrap: nowrap;
        overflow-x: auto;
    }

    .card {
        width: calc((100vw - 32px) / 4 - 8px);
        height: calc((100vw - 32px) / 4 * 1.35 - 8px);
        max-width: 77px;
        max-height: 104px;
        min-width: 57px;
        min-height: 77px;
        cursor: pointer;
        -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
        touch-action: manipulation;
    }

    .card.draggable-card:hover:not(.dragging) {
        transform: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .card.dragging {
        transform: scale(1.08);
        box-shadow: 0 8px 16px rgba(0,0,0,0.4);
        z-index: 1000;
        opacity: 0.9;
    }

    /* Enhanced touch feedback */
    .card:active {
        transform: scale(0.95);
        transition: transform 0.15s ease;
    }

    .btn {
        min-width: 50px;
        font-weight: 500;
        padding: 4px 6px;
        font-size: 11px;
        min-height: 28px;
    }

    .stat {
        padding: 3px 6px;
        font-size: 10px;
        min-height: 24px;
        min-width: 45px;
    }

    .btn:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
}

/* Landscape mobile optimizations */
@media screen and (max-height: 500px) and (orientation: landscape) {
    .game-header {
        flex-direction: row;
        justify-content: space-between;
        padding: 4px 8px;
    }

    .header-left {
        flex: 1;
        min-height: 0;
    }

    .header-right {
        flex: 1;
        flex-wrap: nowrap;
        justify-content: flex-end;
    }

    .game-board {
        gap: 6px;
        flex: 1;
        min-height: 0;
    }

    .card {
        width: calc((100vw - 80px) / 8 - 6px);
        height: calc((100vw - 80px) / 8 * 1.35 - 6px);
        max-width: 62px;
        max-height: 84px;
        min-width: 42px;
        min-height: 57px;
        font-size: 14px !important;
        font-weight: 500 !important;
        padding: 3px;
    }

    .card-center {
        font-size: 20px;
    }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
    .game-header {
        padding: 3px 4px;
    }

    .header-left h2 {
        font-size: 1.1rem;
    }

    .game-stats {
        gap: 3px;
    }

    .stat {
        min-width: 50px;
    }

    #fullscreenBtn {
        font-size: 12px;
        padding: 3px 5px;
        min-width: 28px;
    }

    .game-container {
        padding: 2px;
    }

    .game-board {
        gap: 4px;
    }
}

@media screen and (max-width: 768px) and (orientation: landscape) {
    /* Ultra compact header for landscape mobile */
    .game-header {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: 6px;
        padding: 4px 8px;
        margin-bottom: 4px;
        background: rgba(0,0,0,0.1);
        border-radius: 4px;
    }

    .header-left {
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .header-left h2 {
        font-size: 14px;
        margin: 0;
        font-weight: 600;
        white-space: nowrap;
    }

    .game-stats {
        display: flex;
        gap: 6px;
        flex-wrap: nowrap;
    }

    .stat {
        background: rgba(255,255,255,0.1);
        border-radius: 3px;
        padding: 2px 6px;
        font-size: 11px;
        min-height: auto;
        line-height: 1.2;
        white-space: nowrap;
    }

    .header-right {
        flex: 1;
        justify-content: flex-end;
        gap: 4px;
        flex-wrap: nowrap;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .header-right::-webkit-scrollbar { display: none; }

    .btn {
        background: rgba(255,255,255,0.15);
        border: 1px solid rgba(255,255,255,0.2);
        color: white;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 11px;
        min-height: 28px;
        min-width: auto;
        white-space: nowrap;
        flex: 0 0 auto;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .btn:hover, .btn:active {
        background: rgba(255,255,255,0.25);
        transform: scale(0.98);
    }

    .btn-primary {
        background: rgba(76, 175, 80, 0.8);
        border-color: rgba(76, 175, 80, 1);
    }

    /* Optimized cards for landscape */
    .card {
        width: calc((100vw - 60px) / 8 - 8px);
        height: calc(((100vw - 60px) / 8 - 8px) * 1.35);
        max-width: 65px;
        max-height: 85px;
        min-width: 55px;
        min-height: 74px;
        padding: 3px;
        overflow: hidden;
        font-size: 11px;
    }

    .card-top span, .card-bottom span {
        font-size: clamp(9px, calc(((100vw - 60px) / 8 - 8px) * 0.2), 13px);
        line-height: 1;
    }

    .card-center {
        font-size: clamp(14px, calc(((100vw - 60px) / 8 - 8px) * 0.45), 24px);
        line-height: 1;
    }
}

/* Very small screens - compact mode */
@media (max-width: 360px) {
    .header-left h2 {
        font-size: 1.1rem;
    }

    .btn, .stat {
        font-size: 10px;
        padding: 3px 6px;
        min-height: 28px;
    }

    .game-stats .stat {
        min-width: 50px;
    }

    .header-right {
        gap: 3px;
    }
}

/* Mobile tips styling */
.mobile-tips {
    margin-top: 15px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 14px;
}

.mobile-tips ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.mobile-tips li {
    margin: 4px 0;
    color: #e0e0e0;
}
