/* Tetris Game Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
    overflow-x: hidden;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
}

.header h1 {
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin: 0;
}

.home-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.home-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* Game Layout */
.game-layout {
    display: grid;
    grid-template-columns: 1fr 300px 1fr;
    gap: 30px;
    align-items: start;
}

.left-panel, .right-panel {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
}

.score-panel {
    margin-bottom: 30px;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.score-item .label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.score-item .value {
    font-size: 1.5rem;
    font-weight: bold;
}

.controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.control-btn.primary {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.control-btn.secondary {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: white;
}

.control-btn.danger {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
}

.control-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

/* Game Area */
.game-area {
    position: relative;
    display: flex;
    justify-content: center;
}

#game-canvas {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background: #000;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Next Piece */
.next-piece {
    margin-bottom: 30px;
    text-align: center;
}

.next-piece h3 {
    margin-bottom: 15px;
    font-size: 1.2rem;
}

#next-canvas {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: #000;
}

/* Instructions */
.instructions h3 {
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.instruction-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.instruction-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.instruction-item .key {
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 10px;
    border-radius: 6px;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
    font-size: 0.8rem;
}

.instruction-item .desc {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Game Overlays */
.game-overlay, .game-over {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    
}

.game-over.hidden, .game-overlay.hidden {
    display: none;
}

.game-over-content, .game-start-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: scale(0.8);
    animation: modalAppear 0.3s ease forwards;
}

@keyframes modalAppear {
    to {
        transform: scale(1);
    }
}

.game-over-content h2, .game-start-content h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.game-over-content p, .game-start-content p {
    font-size: 1.2rem;
    margin-bottom: 20px;
}

#high-score-msg {
    color: #FFD700;
    font-weight: bold;
    animation: glow 1s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 5px #FFD700; }
    to { text-shadow: 0 0 20px #FFD700, 0 0 30px #FFD700; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .game-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .left-panel {
        order: 2;
    }
    
    .game-area {
        order: 1;
    }
    
    .right-panel {
        order: 3;
    }
    
    #game-canvas {
        width: 100%;
        max-width: 300px;
        height: auto;
    }
    
    .score-panel {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
    
    .score-item {
        margin-bottom: 0;
    }

    .mobile-controls {
        display: flex !important;
    }

    .container {
        padding-bottom: 120px;
    }
}

@media (max-width: 480px) {
    .score-panel {
        grid-template-columns: 1fr;
    }

    .controls {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .control-btn {
        flex: 1;
        min-width: 100px;
    }

    .mobile-controls {
        display: flex;
    }

    .game-area {
        margin-bottom: 10px;
    }

    #game-canvas {
        width: 220px;
        height: 440px;
    }

    .mobile-btn {
        width: 50px;
        height: 50px;
        font-size: 18px;
    }

    .control-row, .direction-row {
        gap: 10px;
    }
}

/* Mobile Controls */
.mobile-controls {
    display: none;
    flex-direction: column;
    gap: 15px;
    margin: 15px 0;
    align-items: center;
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
    padding: 15px;
    border-radius: 15px;
    
}

.control-row, .direction-row {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.mobile-btn {
    width: 60px;
    height: 60px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.mobile-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.mobile-btn:active {
    background: rgba(255, 255, 255, 0.4);
    transform: translateY(0px);
}

#btn-rotate {
    background: rgba(74, 144, 226, 0.3);
    border-color: rgba(74, 144, 226, 0.5);
}

#btn-drop {
    background: rgba(231, 76, 60, 0.3);
    border-color: rgba(231, 76, 60, 0.5);
}

#btn-pause {
    background: rgba(241, 196, 15, 0.3);
    border-color: rgba(241, 196, 15, 0.5);
}
