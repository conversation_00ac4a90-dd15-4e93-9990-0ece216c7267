
let game;

document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('game-canvas');
    game = new SnakeGame(canvas);
    
    // Initialize the game display
    game.draw();
    
    // Button event listeners
    document.getElementById('start-btn').addEventListener('click', startGame);
    document.getElementById('canvas-start-btn').addEventListener('click', startGame);
    document.getElementById('pause-btn').addEventListener('click', togglePause);
    document.getElementById('reset-btn').addEventListener('click', resetGame);
    document.getElementById('restart-btn').addEventListener('click', restartGame);
    document.getElementById('home-btn').addEventListener('click', goHome);

    // Mobile control buttons
    setupMobileControls();
    
    // Close game over modal when clicking outside
    document.getElementById('game-over').addEventListener('click', function(e) {
        if (e.target === this) {
            closeGameOver();
        }
    });
});

function startGame() {
    if (game.gameRunning) return;

    // Hide start overlay
    document.getElementById('game-start').classList.add('hidden');

    game.start();
    updateButtonStates();
}

function togglePause() {
    if (!game.gameRunning) return;
    
    game.togglePause();
    updateButtonStates();
}

function resetGame() {
    game.reset();
    closeGameOver();
    showStartOverlay();
    updateButtonStates();
}

function restartGame() {
    game.reset();
    closeGameOver();
    showStartOverlay();
}

function showStartOverlay() {
    document.getElementById('game-start').classList.remove('hidden');
}

function goHome() {
    window.location.href = '/';
}

function setupMobileControls() {
    // Direction buttons
    document.getElementById('btn-up').addEventListener('touchstart', (e) => {
        e.preventDefault();
        if (game.gameRunning && !game.gamePaused && game.dy !== 1) {
            game.dx = 0;
            game.dy = -1;
            addButtonFeedback(e.target);
        }
    });

    document.getElementById('btn-down').addEventListener('touchstart', (e) => {
        e.preventDefault();
        if (game.gameRunning && !game.gamePaused && game.dy !== -1) {
            game.dx = 0;
            game.dy = 1;
            addButtonFeedback(e.target);
        }
    });

    document.getElementById('btn-left').addEventListener('touchstart', (e) => {
        e.preventDefault();
        if (game.gameRunning && !game.gamePaused && game.dx !== 1) {
            game.dx = -1;
            game.dy = 0;
            addButtonFeedback(e.target);
        }
    });

    document.getElementById('btn-right').addEventListener('touchstart', (e) => {
        e.preventDefault();
        if (game.gameRunning && !game.gamePaused && game.dx !== -1) {
            game.dx = 1;
            game.dy = 0;
            addButtonFeedback(e.target);
        }
    });

    document.getElementById('btn-pause').addEventListener('touchstart', (e) => {
        e.preventDefault();
        if (game.gameRunning) {
            togglePause();
            addButtonFeedback(e.target);
        }
    });

    // Also add click events for desktop testing
    document.getElementById('btn-up').addEventListener('click', (e) => {
        if (game.gameRunning && !game.gamePaused && game.dy !== 1) {
            game.dx = 0;
            game.dy = -1;
        }
    });

    document.getElementById('btn-down').addEventListener('click', (e) => {
        if (game.gameRunning && !game.gamePaused && game.dy !== -1) {
            game.dx = 0;
            game.dy = 1;
        }
    });

    document.getElementById('btn-left').addEventListener('click', (e) => {
        if (game.gameRunning && !game.gamePaused && game.dx !== 1) {
            game.dx = -1;
            game.dy = 0;
        }
    });

    document.getElementById('btn-right').addEventListener('click', (e) => {
        if (game.gameRunning && !game.gamePaused && game.dx !== -1) {
            game.dx = 1;
            game.dy = 0;
        }
    });

    document.getElementById('btn-pause').addEventListener('click', (e) => {
        if (game.gameRunning) {
            togglePause();
        }
    });
}

function addButtonFeedback(button) {
    // Visual feedback
    button.style.transform = 'scale(0.9)';
    setTimeout(() => {
        button.style.transform = 'scale(1)';
    }, 100);

    // Haptic feedback
    if ('vibrate' in navigator) {
        navigator.vibrate(30);
    }
}

function closeGameOver() {
    document.getElementById('game-over').classList.add('hidden');
}

function updateButtonStates() {
    const startBtn = document.getElementById('start-btn');
    const pauseBtn = document.getElementById('pause-btn');
    const resetBtn = document.getElementById('reset-btn');
    
    if (game.gameRunning && !game.gamePaused) {
        startBtn.disabled = true;
        startBtn.textContent = 'Playing...';
        pauseBtn.disabled = false;
        pauseBtn.textContent = 'Pause';
        resetBtn.disabled = false;
    } else if (game.gameRunning && game.gamePaused) {
        startBtn.disabled = true;
        startBtn.textContent = 'Playing...';
        pauseBtn.disabled = false;
        pauseBtn.textContent = 'Resume';
        resetBtn.disabled = false;
    } else {
        startBtn.disabled = false;
        startBtn.textContent = 'Start Game';
        pauseBtn.disabled = true;
        pauseBtn.textContent = 'Pause';
        resetBtn.disabled = false;
    }
}

// Handle window resize
window.addEventListener('resize', function() {
    // Redraw the game when window is resized
    if (game) {
        game.draw();
    }
});

// Prevent default behavior for arrow keys
document.addEventListener('keydown', function(e) {
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', ' '].includes(e.key)) {
        e.preventDefault();
    }
});

// Add touch controls for mobile
let touchStartX = 0;
let touchStartY = 0;

document.addEventListener('touchstart', function(e) {
    touchStartX = e.touches[0].clientX;
    touchStartY = e.touches[0].clientY;
});

document.addEventListener('touchend', function(e) {
    if (!game.gameRunning || game.gamePaused) return;
    
    const touchEndX = e.changedTouches[0].clientX;
    const touchEndY = e.changedTouches[0].clientY;
    
    const deltaX = touchEndX - touchStartX;
    const deltaY = touchEndY - touchStartY;
    
    const minSwipeDistance = 30;
    
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Horizontal swipe
        if (Math.abs(deltaX) > minSwipeDistance) {
            if (deltaX > 0 && game.dx !== -1) {
                // Swipe right
                game.dx = 1;
                game.dy = 0;
            } else if (deltaX < 0 && game.dx !== 1) {
                // Swipe left
                game.dx = -1;
                game.dy = 0;
            }
        }
    } else {
        // Vertical swipe
        if (Math.abs(deltaY) > minSwipeDistance) {
            if (deltaY > 0 && game.dy !== -1) {
                // Swipe down
                game.dx = 0;
                game.dy = 1;
            } else if (deltaY < 0 && game.dy !== 1) {
                // Swipe up
                game.dx = 0;
                game.dy = -1;
            }
        }
    }
});
