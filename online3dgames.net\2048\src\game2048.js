class Game2048 {
    constructor() {
        this.size = 4;
        this.board = [];
        this.score = 0;
        this.moves = 0;
        this.gameWon = false;
        this.gameOver = false;
        this.gameStarted = false;
        this.previousState = null;
        
        this.initBoard();
        this.setupEventListeners();
        this.loadBestScore();
    }
    
    initBoard() {
        this.board = Array(this.size).fill().map(() => Array(this.size).fill(0));
        this.addRandomTile();
        this.addRandomTile();
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            if (this.gameOver || this.gameStarted === false) return;
            
            let moved = false;
            switch(e.key) {
                case 'ArrowUp':
                case 'w':
                case 'W':
                    e.preventDefault();
                    moved = this.move('up');
                    break;
                case 'ArrowDown':
                case 's':
                case 'S':
                    e.preventDefault();
                    moved = this.move('down');
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    e.preventDefault();
                    moved = this.move('left');
                    break;
                case 'ArrowRight':
                case 'd':
                case 'D':
                    e.preventDefault();
                    moved = this.move('right');
                    break;
            }
            
            if (moved) {
                this.moves++;
                this.addRandomTile();
                this.updateDisplay();
                this.checkGameState();
            }
        });
        

        this.setupTouchControls();
    }
    
    setupTouchControls() {
        let startX, startY, startTime;
        const gameBoard = document.getElementById('game-board');

        // Add touch-action CSS to prevent default behaviors
        gameBoard.style.touchAction = 'none';
        gameBoard.style.userSelect = 'none';
        gameBoard.style.webkitUserSelect = 'none';
        gameBoard.style.webkitTouchCallout = 'none';

        gameBoard.addEventListener('touchstart', (e) => {
            e.preventDefault();
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            startTime = Date.now();

            // Add visual feedback
            gameBoard.style.transform = 'scale(0.98)';
        }, { passive: false });

        gameBoard.addEventListener('touchmove', (e) => {
            e.preventDefault(); // Prevent scrolling
        }, { passive: false });

        gameBoard.addEventListener('touchend', (e) => {
            e.preventDefault();

            // Remove visual feedback
            gameBoard.style.transform = 'scale(1)';

            if (!startX || !startY || this.gameStarted === false) return;

            // Check for long press (ignore if > 500ms)
            const touchDuration = Date.now() - startTime;
            if (touchDuration > 500) {
                startX = startY = null;
                return;
            }

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;

            const deltaX = endX - startX;
            const deltaY = endY - startY;

            const minSwipeDistance = 40; // Increased for better accuracy
            let moved = false;

            // Determine swipe direction
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                // Horizontal swipe
                if (Math.abs(deltaX) > minSwipeDistance) {
                    if (deltaX > 0) {
                        moved = this.move('right');
                    } else {
                        moved = this.move('left');
                    }
                }
            } else {
                // Vertical swipe
                if (Math.abs(deltaY) > minSwipeDistance) {
                    if (deltaY > 0) {
                        moved = this.move('down');
                    } else {
                        moved = this.move('up');
                    }
                }
            }

            if (moved) {
                this.moves++;
                this.addRandomTile();
                this.updateDisplay();
                this.checkGameState();

                // Add haptic feedback if available
                if ('vibrate' in navigator) {
                    navigator.vibrate(50);
                }
            }

            startX = startY = null;
        }, { passive: false });
    }
    
    addRandomTile() {
        const emptyCells = [];
        for (let i = 0; i < this.size; i++) {
            for (let j = 0; j < this.size; j++) {
                if (this.board[i][j] === 0) {
                    emptyCells.push({x: i, y: j});
                }
            }
        }
        
        if (emptyCells.length > 0) {
            const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)];
            this.board[randomCell.x][randomCell.y] = Math.random() < 0.9 ? 2 : 4;
        }
    }
    
    move(direction) {
        this.saveState();
        let moved = false;

        // Store old board state for animation
        const oldBoard = this.board.map(row => [...row]);

        switch(direction) {
            case 'left':
                moved = this.moveLeft();
                break;
            case 'right':
                moved = this.moveRight();
                break;
            case 'up':
                moved = this.moveUp();
                break;
            case 'down':
                moved = this.moveDown();
                break;
        }

        // Always render after move attempt
        this.renderBoard();

        return moved;
    }
    
    moveLeft() {
        let moved = false;
        for (let i = 0; i < this.size; i++) {
            const row = this.board[i].filter(val => val !== 0);
            const merged = this.mergeArray(row);
            const newRow = merged.concat(Array(this.size - merged.length).fill(0));
            
            if (!this.arraysEqual(this.board[i], newRow)) {
                moved = true;
            }
            this.board[i] = newRow;
        }
        return moved;
    }
    
    moveRight() {
        let moved = false;
        for (let i = 0; i < this.size; i++) {
            const row = this.board[i].filter(val => val !== 0).reverse();
            const merged = this.mergeArray(row);
            const newRow = Array(this.size - merged.length).fill(0).concat(merged.reverse());
            
            if (!this.arraysEqual(this.board[i], newRow)) {
                moved = true;
            }
            this.board[i] = newRow;
        }
        return moved;
    }
    
    moveUp() {
        let moved = false;
        for (let j = 0; j < this.size; j++) {
            const column = [];
            for (let i = 0; i < this.size; i++) {
                if (this.board[i][j] !== 0) {
                    column.push(this.board[i][j]);
                }
            }
            const merged = this.mergeArray(column);
            const newColumn = merged.concat(Array(this.size - merged.length).fill(0));
            
            for (let i = 0; i < this.size; i++) {
                if (this.board[i][j] !== newColumn[i]) {
                    moved = true;
                }
                this.board[i][j] = newColumn[i];
            }
        }
        return moved;
    }
    
    moveDown() {
        let moved = false;
        for (let j = 0; j < this.size; j++) {
            const column = [];
            for (let i = this.size - 1; i >= 0; i--) {
                if (this.board[i][j] !== 0) {
                    column.push(this.board[i][j]);
                }
            }
            const merged = this.mergeArray(column);
            const newColumn = Array(this.size - merged.length).fill(0).concat(merged.reverse());
            
            for (let i = 0; i < this.size; i++) {
                if (this.board[i][j] !== newColumn[i]) {
                    moved = true;
                }
                this.board[i][j] = newColumn[i];
            }
        }
        return moved;
    }
    
    mergeArray(arr) {
        const result = [];
        let i = 0;
        
        while (i < arr.length) {
            if (i < arr.length - 1 && arr[i] === arr[i + 1]) {
                const merged = arr[i] * 2;
                result.push(merged);
                this.score += merged;
                
                if (merged === 2048 && !this.gameWon) {
                    this.gameWon = true;
                    this.showWinMessage();
                }
                
                i += 2;
            } else {
                result.push(arr[i]);
                i++;
            }
        }
        
        return result;
    }
    
    arraysEqual(a, b) {
        return a.length === b.length && a.every((val, i) => val === b[i]);
    }
    
    saveState() {
        this.previousState = {
            board: this.board.map(row => [...row]),
            score: this.score,
            moves: this.moves
        };
    }
    
    undo() {
        if (this.previousState) {
            this.board = this.previousState.board;
            this.score = this.previousState.score;
            this.moves = this.previousState.moves;
            this.previousState = null;
            this.updateDisplay();
            this.renderBoard();
        }
    }
    
    canUndo() {
        return this.previousState !== null;
    }
    
    checkGameState() {
        if (this.isGameOver()) {
            this.gameOver = true;
            this.showGameOver();
        }
    }
    
    isGameOver() {
        // Check for empty cells
        for (let i = 0; i < this.size; i++) {
            for (let j = 0; j < this.size; j++) {
                if (this.board[i][j] === 0) {
                    return false;
                }
            }
        }
        
        // Check for possible merges
        for (let i = 0; i < this.size; i++) {
            for (let j = 0; j < this.size; j++) {
                const current = this.board[i][j];
                if ((i < this.size - 1 && this.board[i + 1][j] === current) ||
                    (j < this.size - 1 && this.board[i][j + 1] === current)) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    showWinMessage() {
        document.getElementById('game-over-title').textContent = 'Congratulations! You reached 2048!';
        document.getElementById('game-over-message').textContent = 'You won! Continue playing to achieve higher scores.';
        document.getElementById('continue-btn').classList.remove('hidden');
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('final-moves').textContent = this.moves;
        document.getElementById('game-over').classList.remove('hidden');
    }
    
    showGameOver() {
        const bestScore = this.getBestScore();
        if (this.score > bestScore) {
            this.saveBestScore(this.score);
            document.getElementById('high-score-msg').classList.remove('hidden');
        } else {
            document.getElementById('high-score-msg').classList.add('hidden');
        }
        
        document.getElementById('game-over-title').textContent = 'Game Over';
        document.getElementById('game-over-message').textContent = 'No more moves available!';
        document.getElementById('continue-btn').classList.add('hidden');
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('final-moves').textContent = this.moves;
        document.getElementById('game-over').classList.remove('hidden');
    }
    
    newGame() {
        this.score = 0;
        this.moves = 0;
        this.gameWon = false;
        this.gameOver = false;
        this.gameStarted = false;
        this.previousState = null;
        this.initBoard();
        this.updateDisplay();
        this.renderBoard();
        document.getElementById('game-over').classList.add('hidden');
    }
    
    continueGame() {
        this.gameWon = false;
        document.getElementById('game-over').classList.add('hidden');
    }
    
    updateDisplay() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('moves').textContent = this.moves;
        document.getElementById('best-score').textContent = this.getBestScore();
        
        const undoBtn = document.getElementById('undo-btn');
        undoBtn.disabled = !this.canUndo();
    }
    
    renderBoard() {
        const gameBoard = document.getElementById('game-board');
        gameBoard.innerHTML = '';

        // Create grid
        const gridContainer = document.createElement('div');
        gridContainer.className = 'grid-container';

        for (let i = 0; i < this.size; i++) {
            const row = document.createElement('div');
            row.className = 'grid-row';
            for (let j = 0; j < this.size; j++) {
                const cell = document.createElement('div');
                cell.className = 'grid-cell';
                row.appendChild(cell);
            }
            gridContainer.appendChild(row);
        }
        gameBoard.appendChild(gridContainer);

        // Create tiles
        const tileContainer = document.createElement('div');
        tileContainer.className = 'tile-container';

        // Calculate tile size and spacing dynamically
        const gridCells = gameBoard.querySelectorAll('.grid-cell');
        let tileSize = 87.5; // Default size
        let spacing = 10; // Default spacing

        if (gridCells.length > 0) {
            const firstCell = gridCells[0];
            const cellStyle = window.getComputedStyle(firstCell);
            tileSize = parseFloat(cellStyle.width);
            spacing = parseFloat(cellStyle.marginRight) || 10;
        }

        for (let i = 0; i < this.size; i++) {
            for (let j = 0; j < this.size; j++) {
                if (this.board[i][j] !== 0) {
                    const tile = document.createElement('div');
                    tile.className = `tile tile-${this.board[i][j]}`;
                    tile.textContent = this.board[i][j];
                    tile.style.left = `${j * (tileSize + spacing)}px`;
                    tile.style.top = `${i * (tileSize + spacing)}px`;
                    tileContainer.appendChild(tile);
                }
            }
        }
        gameBoard.appendChild(tileContainer);
    }
    
    getBestScore() {
        return parseInt(localStorage.getItem('2048BestScore') || '0');
    }
    
    saveBestScore(score) {
        localStorage.setItem('2048BestScore', score.toString());
    }
    
    loadBestScore() {
        document.getElementById('best-score').textContent = this.getBestScore();
    }


}
