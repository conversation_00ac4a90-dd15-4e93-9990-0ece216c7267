// Fidget Spinner Game Logic
class FidgetSpinnerGame {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        
        // Game state
        this.gameStarted = false;
        this.angle = 0;
        this.angularVelocity = 0;
        this.friction = 0.98;
        this.minVelocity = 0.01;
        
        // Stats
        this.currentSpeed = 0; // RPM
        this.maxSpeed = 0;
        this.spinTime = 0;
        this.startTime = 0;
        this.isSpinning = false;
        
        // Spinner styles
        this.currentStyle = 0;
        this.styles = [
            { name: 'Classic three-leaf', arms: 3, color: '#4ECDC4' },
            { name: 'Four-leaf lucky', arms: 4, color: '#FF6B9D' },
            { name: 'Five-pointed star', arms: 5, color: '#FFD93D' },
            { name: 'Hexagon', arms: 6, color: '#96CEB4' }
        ];
        
        // Audio
        this.soundEnabled = true;
        this.audioContext = null;
        this.initAudio();
        
        // Mouse/Touch tracking
        this.lastMouseX = 0;
        this.lastMouseY = 0;
        this.mouseVelocityX = 0;
        this.mouseVelocityY = 0;
        this.isDragging = false;
        this.isMouseDown = false;
        this.dragStartTime = 0;
        this.totalDragDistance = 0;
        
        this.setupEventListeners();
        this.draw();
        this.updateStats();
    }
    
    initAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Web Audio API not supported');
            this.soundEnabled = false;
        }
    }
    
    playSpinSound() {
        if (!this.soundEnabled || !this.audioContext || this.angularVelocity < 0.1) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        // Create a spinning sound based on velocity
        const frequency = 200 + Math.abs(this.angularVelocity) * 100;
        oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
        
        const volume = Math.min(Math.abs(this.angularVelocity) * 0.1, 0.3);
        gainNode.gain.setValueAtTime(volume, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.1);
    }
    
    setupEventListeners() {
        // Mouse events
        this.canvas.addEventListener('mousedown', (e) => this.handleStart(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleEnd(e));
        this.canvas.addEventListener('mouseleave', (e) => this.handleEnd(e));
        
        // Touch events
        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.handleStart(e.touches[0]);
        });
        this.canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            if (e.touches.length > 0) {
                this.handleMove(e.touches[0]);
            }
        });
        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.handleEnd(e);
        });
        this.canvas.addEventListener('touchcancel', (e) => {
            e.preventDefault();
            this.handleEnd(e);
        });
    }
    
    handleStart(e) {
        if (!this.gameStarted) return;

        const rect = this.canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        // Check if click is within the spinner area (much larger for better mobile experience)
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        const distance = Math.sqrt(Math.pow(mouseX - centerX, 2) + Math.pow(mouseY - centerY, 2));

        // Use canvas radius minus border for touch area (almost the entire canvas)
        const canvasRadius = Math.min(this.canvas.width, this.canvas.height) / 2 - 20;

        if (distance <= canvasRadius) {
            this.lastMouseX = mouseX;
            this.lastMouseY = mouseY;
            this.isDragging = true;
            this.isMouseDown = true;
            this.mouseVelocityX = 0;
            this.mouseVelocityY = 0;
            this.dragStartTime = Date.now();
            this.totalDragDistance = 0;
        }
    }
    
    handleMove(e) {
        if (!this.gameStarted || !this.isDragging || !this.isMouseDown) return;

        const rect = this.canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        // Calculate velocity
        this.mouseVelocityX = mouseX - this.lastMouseX;
        this.mouseVelocityY = mouseY - this.lastMouseY;

        // Calculate distance moved
        const distance = Math.sqrt(this.mouseVelocityX * this.mouseVelocityX + this.mouseVelocityY * this.mouseVelocityY);
        this.totalDragDistance += distance;

        // Calculate angular velocity based on tangential motion
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;

        const radius = Math.sqrt(Math.pow(mouseX - centerX, 2) + Math.pow(mouseY - centerY, 2));

        // Scale minimum radius based on canvas size
        const canvasSize = Math.min(this.canvas.width, this.canvas.height);
        const minRadius = (canvasSize / 400) * 40; // Scaled minimum radius

        if (radius > minRadius) { // Only if mouse is not too close to center
            // Calculate tangential velocity (perpendicular to radius)
            const tangentialVelocity = (-this.mouseVelocityX * (mouseY - centerY) + this.mouseVelocityY * (mouseX - centerX)) / radius;

            // Apply angular velocity with damping to prevent unrealistic speeds
            const velocityMultiplier = Math.min(0.005, 0.001 + distance * 0.0001);
            this.angularVelocity += tangentialVelocity * velocityMultiplier;

            // Limit maximum angular velocity
            const maxVelocity = 0.5;
            this.angularVelocity = Math.max(-maxVelocity, Math.min(maxVelocity, this.angularVelocity));
        }

        this.lastMouseX = mouseX;
        this.lastMouseY = mouseY;
    }
    
    handleEnd(e) {
        if (!this.isMouseDown) return;

        this.isDragging = false;
        this.isMouseDown = false;

        // Calculate final velocity based on total drag distance and time
        const dragDuration = Date.now() - this.dragStartTime;
        if (dragDuration > 0 && this.totalDragDistance > 10) {
            // More realistic velocity calculation based on drag distance and time
            const dragSpeed = this.totalDragDistance / dragDuration; // pixels per millisecond
            const finalVelocityBoost = Math.min(dragSpeed * 0.1, 0.3); // Cap the boost

            // Apply velocity in the direction of the last movement
            const velocityMagnitude = Math.sqrt(this.mouseVelocityX * this.mouseVelocityX + this.mouseVelocityY * this.mouseVelocityY);
            if (velocityMagnitude > 0) {
                this.angularVelocity += finalVelocityBoost * Math.sign(this.angularVelocity || 1);
            }
        }

        // Start timing if spinner starts spinning
        if (Math.abs(this.angularVelocity) > 0.05 && !this.isSpinning) {
            this.isSpinning = true;
            this.startTime = Date.now();
            this.canvas.classList.add('spinning');
        }

        // Reset tracking variables
        this.totalDragDistance = 0;
        this.dragStartTime = 0;
    }
    
    update() {
        if (!this.gameStarted) return;
        
        // Apply friction
        this.angularVelocity *= this.friction;
        
        // Stop if velocity is too low
        if (Math.abs(this.angularVelocity) < this.minVelocity) {
            this.angularVelocity = 0;
            if (this.isSpinning) {
                this.isSpinning = false;
                this.spinTime = (Date.now() - this.startTime) / 1000;
                this.canvas.classList.remove('spinning');
            }
        }
        
        // Update angle
        this.angle += this.angularVelocity;
        
        // Calculate RPM (more accurate conversion)
        // angularVelocity is in radians per frame (60fps), so multiply by 60 to get radians per second
        // Then convert to RPM: (radians/second) * (60 seconds/minute) / (2π radians/revolution)
        this.currentSpeed = Math.abs(this.angularVelocity) * 60 * 60 / (2 * Math.PI);
        
        // Update max speed
        if (this.currentSpeed > this.maxSpeed) {
            this.maxSpeed = this.currentSpeed;
        }
        
        // Play sound occasionally
        if (Math.random() < 0.1) {
            this.playSpinSound();
        }
        
        this.updateStats();
    }
    
    draw() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        this.ctx.save();
        this.ctx.translate(centerX, centerY);
        this.ctx.rotate(this.angle);
        
        this.drawSpinner();
        
        this.ctx.restore();
        
        // Draw center bearing
        this.drawBearing(centerX, centerY);
        
        this.update();
        requestAnimationFrame(() => this.draw());
    }
    
    drawSpinner() {
        const style = this.styles[this.currentStyle];
        const armCount = style.arms;

        // Scale spinner to be much larger (about 80% of canvas size)
        const canvasSize = Math.min(this.canvas.width, this.canvas.height);
        const scale = canvasSize / 400; // Base scale factor

        const armLength = 120 * scale;
        const armWidth = 80 * scale;
        const bearingRadius = 20 * scale;
        
        this.ctx.fillStyle = style.color;
        this.ctx.strokeStyle = this.darkenColor(style.color, 20);
        this.ctx.lineWidth = 2;
        
        // Draw arms
        for (let i = 0; i < armCount; i++) {
            const angle = (i * 2 * Math.PI) / armCount;
            
            this.ctx.save();
            this.ctx.rotate(angle);
            
            // Draw arm
            this.ctx.beginPath();
            this.ctx.ellipse(armLength / 2, 0, armLength / 2, armWidth / 2, 0, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.stroke();
            
            // Draw bearing at end
            this.ctx.beginPath();
            this.ctx.arc(armLength, 0, bearingRadius, 0, Math.PI * 2);
            this.ctx.fillStyle = this.lightenColor(style.color, 30);
            this.ctx.fill();
            this.ctx.stroke();
            
            this.ctx.restore();
        }
    }
    
    drawBearing(x, y) {
        // Scale bearing to match spinner size
        const canvasSize = Math.min(this.canvas.width, this.canvas.height);
        const scale = canvasSize / 400;

        const outerRadius = 25 * scale;
        const innerRadius = 15 * scale;
        const centerRadius = 6 * scale;

        // Outer ring
        this.ctx.beginPath();
        this.ctx.arc(x, y, outerRadius, 0, Math.PI * 2);
        this.ctx.fillStyle = '#333';
        this.ctx.fill();
        this.ctx.strokeStyle = '#666';
        this.ctx.lineWidth = 3 * scale;
        this.ctx.stroke();

        // Inner ring
        this.ctx.beginPath();
        this.ctx.arc(x, y, innerRadius, 0, Math.PI * 2);
        this.ctx.fillStyle = '#555';
        this.ctx.fill();

        // Center dot
        this.ctx.beginPath();
        this.ctx.arc(x, y, centerRadius, 0, Math.PI * 2);
        this.ctx.fillStyle = '#777';
        this.ctx.fill();
    }
    
    lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
    
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
            (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
            (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }
    
    changeStyle() {
        this.currentStyle = (this.currentStyle + 1) % this.styles.length;
        return this.styles[this.currentStyle].name;
    }
    
    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        return this.soundEnabled;
    }
    
    reset() {
        this.maxSpeed = 0;
        this.spinTime = 0;
        this.angularVelocity = 0;
        this.isSpinning = false;
        this.isDragging = false;
        this.isMouseDown = false;
        this.canvas.classList.remove('spinning');
        this.updateStats();
    }
    
    start() {
        this.gameStarted = true;

        // Set appropriate cursor based on device
        if ('ontouchstart' in window) {
            this.canvas.style.cursor = 'default';
        } else {
            this.canvas.style.cursor = 'grab';
        }
    }
    
    updateStats() {
        document.getElementById('speed').textContent = Math.round(this.currentSpeed);
        document.getElementById('max-speed').textContent = Math.round(this.maxSpeed);
        document.getElementById('spin-time').textContent = this.spinTime.toFixed(1);
    }
}
