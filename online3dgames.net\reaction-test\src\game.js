// Reaction Test UI Controller
let reactionTest;

document.addEventListener('DOMContentLoaded', function() {
    reactionTest = new ReactionTest();
    document.getElementById('home-btn').addEventListener('click', goHome);
});

function goHome() {
    window.location.href = '/';
}

// Prevent context menu on test area
document.getElementById('test-area').addEventListener('contextmenu', function(e) {
    e.preventDefault();
});

// Prevent text selection on test area
document.getElementById('test-area').addEventListener('selectstart', function(e) {
    e.preventDefault();
});

// Add keyboard support
document.addEventListener('keydown', function(e) {
    if (e.code === 'Space' || e.code === 'Enter') {
        e.preventDefault();
        if (reactionTest.state === 'waiting') {
            const startBtn = document.getElementById('start-btn');
            if (!startBtn.classList.contains('hidden')) {
                startBtn.click();
            } else {
                reactionTest.handleClick();
            }
        } else {
            reactionTest.handleClick();
        }
    }
});
