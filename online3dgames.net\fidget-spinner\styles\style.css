/* Fidget Spinner Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
    overflow-x: hidden;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
}

.header h1 {
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin: 0;
}

.home-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.home-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Game Info Panel */
.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
    flex-wrap: wrap;
}

.score-panel {
    display: flex;
    gap: 25px;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
}

.score-item {
    text-align: center;
}

.score-item .label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.score-item .value {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.score-item .unit {
    display: block;
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 2px;
}

.controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.control-btn.primary {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.control-btn.secondary {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: white;
}

.control-btn.success {
    background: linear-gradient(135deg, #8BC34A, #689F38);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Game Container */
.game-container {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    /* Prevent text selection and improve touch experience */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
}

#game-canvas {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    cursor: grab;
    transition: all 0.2s ease;
    /* Improve touch experience */
    touch-action: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#game-canvas:hover {
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    transform: scale(1.02);
}

#game-canvas:active {
    cursor: grabbing;
    transform: scale(0.98);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
}

#game-canvas.spinning {
    cursor: default;
    animation: spinnerGlow 2s ease-in-out infinite alternate;
}

@keyframes spinnerGlow {
    from {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
    to {
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4), 0 0 20px rgba(102, 126, 234, 0.2);
    }
}

/* Game Overlays */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    
    z-index: 1000;
}

.game-overlay.hidden {
    display: none;
}

.game-start-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: scale(0.8);
    animation: modalAppear 0.3s ease forwards;
    min-width: 300px;
}

@keyframes modalAppear {
    to {
        transform: scale(1);
    }
}

.game-start-content h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.game-start-content p {
    font-size: 1.2rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.game-start-content .tip {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 25px;
}

/* Instructions */
.instructions {
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 15px;
    
}

.instructions h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.instruction-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
}

.instruction-item .icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.instruction-item .desc {
    font-size: 0.9rem;
    line-height: 1.4;
}

.instruction-item .desc strong {
    display: block;
    margin-bottom: 5px;
}

.tips {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #4ECDC4;
}

.tips h4 {
    margin-bottom: 15px;
    color: #4ECDC4;
    font-size: 1.2rem;
}

.tips ul {
    list-style: none;
    padding: 0;
}

.tips li {
    padding: 5px 0;
    padding-left: 20px;
    position: relative;
    opacity: 0.9;
}

.tips li:before {
    content: "🌀";
    position: absolute;
    left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .game-info {
        flex-direction: column;
        align-items: stretch;
    }
    
    .score-panel {
        justify-content: center;
    }
    
    .controls {
        justify-content: center;
    }
    
    #game-canvas {
        width: 90vw;
        height: 90vw;
        max-width: 350px;
        max-height: 350px;
        min-width: 280px;
        min-height: 280px;
    }
    
    .instruction-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .score-panel {
        gap: 15px;
    }
    
    .score-item .value {
        font-size: 1.5rem;
    }
    
    .controls {
        flex-direction: column;
    }
    
    .control-btn {
        width: 100%;
    }
    
    #game-canvas {
        width: 300px;
        height: 300px;
    }
}
