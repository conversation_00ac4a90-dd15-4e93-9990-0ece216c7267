
class GameModal {
  constructor() {
    this.overlay = null;
    this.modal = null;
    this.isInitialized = false;
    this.currentResolve = null;
    this.init();
  }

  init() {
    if (this.isInitialized) return;

    const modalHTML = `
      <div id="game-modal-overlay" class="modal-overlay hidden">
        <div id="game-modal" class="modal">
          <h2 id="game-modal-title" class="modal-title"></h2>
          <div id="game-modal-content" class="modal-content"></div>
          <div id="game-modal-buttons" class="modal-buttons"></div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);

    this.overlay = document.getElementById('game-modal-overlay');
    this.modal = document.getElementById('game-modal');
    this.titleEl = document.getElementById('game-modal-title');
    this.contentEl = document.getElementById('game-modal-content');
    this.buttonsEl = document.getElementById('game-modal-buttons');

    this.bindEvents();
    this.isInitialized = true;
  }

  bindEvents() {
    this.overlay.addEventListener('click', (e) => {
      if (e.target === this.overlay) {
        this.hide();
      }
    });

    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !this.overlay.classList.contains('hidden')) {
        this.hide();
      }
    });
  }

  show(options = {}) {
    const {
      title = 'Tips',
      content = '',
      type = 'info', // info, success, warning, error, confirm
      buttons = [{ text: 'Confirm', type: 'primary' }],
      closable = true
    } = options;

    return new Promise((resolve) => {
      this.currentResolve = resolve;

      this.titleEl.textContent = title;

      this.contentEl.textContent = content;

      this.modal.className = `modal ${type}`;

      this.createButtons(buttons, resolve);

      this.overlay.classList.remove('hidden');
      setTimeout(() => {
        this.overlay.classList.add('show');
      }, 10);
    });
  }

  createButtons(buttons, resolve) {
    this.buttonsEl.innerHTML = '';

    buttons.forEach((button, index) => {
      const btn = document.createElement('button');
      btn.className = `modal-btn ${button.type || 'secondary'}`;
      btn.textContent = button.text;
      
      btn.addEventListener('click', () => {
        if (button.callback) {
          button.callback();
        }
        this.hide();
        resolve(button.value !== undefined ? button.value : index);
      });

      this.buttonsEl.appendChild(btn);
    });
  }

  hide() {
    this.overlay.classList.remove('show');
    setTimeout(() => {
      this.overlay.classList.add('hidden');
      if (this.currentResolve) {
        this.currentResolve(-1); 
        this.currentResolve = null;
      }
    }, 300);
  }

  info(title, content, options = {}) {
    return this.show({
      title,
      content,
      type: 'info',
      buttons: [{ text: 'Confirm', type: 'primary', value: true }],
      ...options
    });
  }

  success(title, content, options = {}) {
    return this.show({
      title,
      content,
      type: 'success',
      buttons: [{ text: 'Confirm', type: 'success', value: true }],
      ...options
    });
  }

  warning(title, content, options = {}) {
    return this.show({
      title,
      content,
      type: 'warning',
      buttons: [{ text: 'Confirm', type: 'primary', value: true }],
      ...options
    });
  }

  error(title, content, options = {}) {
    return this.show({
      title,
      content,
      type: 'error',
      buttons: [{ text: 'Confirm', type: 'danger', value: true }],
      ...options
    });
  }

  confirm(title, content, options = {}) {
    return this.show({
      title,
      content,
      type: 'confirm',
      buttons: [
        { text: 'Cancel', type: 'secondary', value: false },
        { text: 'Confirm', type: 'primary', value: true }
      ],
      ...options
    });
  }

  custom(title, content, buttons, type = 'info') {
    return this.show({
      title,
      content,
      type,
      buttons
    });
  }
}

const gameModal = new GameModal();

window.gameAlert = (message, title = 'Tips') => {
  return gameModal.info(title, message);
};

window.gameConfirm = (message, title = 'Confirm') => {
  return gameModal.confirm(title, message);
};

window.gameSuccess = (message, title = 'Success') => {
  return gameModal.success(title, message);
};

window.gameError = (message, title = 'Error') => {
  return gameModal.error(title, message);
};

window.gameWarning = (message, title = 'Warning') => {
  return gameModal.warning(title, message);
};

if (typeof module !== 'undefined' && module.exports) {
  module.exports = GameModal;
}

if (typeof define === 'function' && define.amd) {
  define([], function() {
    return GameModal;
  });
}
