// Simplified Tetris Game Logic
class TetrisGame {
    constructor(canvas, nextCanvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.nextCanvas = nextCanvas;
        this.nextCtx = nextCanvas.getContext('2d');
        
        this.blockSize = 30;
        this.rows = 20;
        this.cols = 10;
        
        this.board = Array(this.rows).fill().map(() => Array(this.cols).fill(0));
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.gameRunning = false;
        this.gamePaused = false;
        
        this.currentPiece = null;
        this.nextPiece = null;
        
        this.pieces = [
            // I piece - horizontal and vertical
            [[[1,1,1,1]], [[1],[1],[1],[1]]],
            // O piece
            [[[1,1],[1,1]]],
            // T piece
            [[[0,1,0],[1,1,1]], [[1,0],[1,1],[1,0]], [[1,1,1],[0,1,0]], [[0,1],[1,1],[0,1]]],
            // S piece
            [[[0,1,1],[1,1,0]], [[1,0],[1,1],[0,1]]],
            // Z piece
            [[[1,1,0],[0,1,1]], [[0,1],[1,1],[1,0]]],
            // J piece
            [[[1,0,0],[1,1,1]], [[1,1],[1,0],[1,0]], [[1,1,1],[0,0,1]], [[0,1],[0,1],[1,1]]],
            // L piece
            [[[0,0,1],[1,1,1]], [[1,0],[1,0],[1,1]], [[1,1,1],[1,0,0]], [[1,1],[0,1],[0,1]]]
        ];
        
        this.colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#F39C12'];
        
        this.setupEventListeners();
        this.loadHighScore();
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            if (!this.gameRunning || this.gamePaused) return;
            
            switch(e.key) {
                case 'ArrowLeft':
                    this.movePiece(-1, 0);
                    break;
                case 'ArrowRight':
                    this.movePiece(1, 0);
                    break;
                case 'ArrowDown':
                    this.movePiece(0, 1);
                    break;
                case 'ArrowUp':
                    this.rotatePiece();
                    break;
                case ' ':
                    e.preventDefault();
                    this.dropPiece();
                    break;
            }
        });
    }
    
    generatePiece() {
        const pieceIndex = Math.floor(Math.random() * this.pieces.length);
        const rotationIndex = Math.floor(Math.random() * this.pieces[pieceIndex].length);
        const shape = this.pieces[pieceIndex][rotationIndex];
        
        // Calculate piece width to ensure centered placement
        const pieceWidth = shape[0].length;
        const startX = Math.floor((this.cols - pieceWidth) / 2);
        
        return {
            shape: shape,
            x: startX,
            y: 0,
            color: this.colors[pieceIndex],
            pieceIndex: pieceIndex,
            rotationIndex: rotationIndex
        };
    }
    
    // Check if there are available moves
    hasValidMoves(piece) {
        // Check all possible positions and rotations
        const rotations = this.pieces[piece.pieceIndex];
        
        for (let rotationIndex = 0; rotationIndex < rotations.length; rotationIndex++) {
            const shape = rotations[rotationIndex];
            
            // Check all possible horizontal positions
            for (let x = -2; x <= this.cols + 2; x++) {
                const testPiece = {
                    ...piece,
                    shape: shape,
                    x: x,
                    y: 0,
                    rotationIndex: rotationIndex
                };
                
                // If this position is valid, there are still available moves
                if (!this.checkCollision(testPiece, 0, 0)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    newPiece() {
        if (!this.nextPiece) {
            this.nextPiece = this.generatePiece();
        }
        this.currentPiece = this.nextPiece;
        this.nextPiece = this.generatePiece();
        
        // Check if new piece can be placed
        if (this.checkCollision(this.currentPiece, 0, 0)) {
            // Check if there are any valid moves
            if (!this.hasValidMoves(this.currentPiece)) {
                this.gameOver();
                return;
            }
        }
        
        // Ensure piece is in valid position
        if (this.currentPiece.x < 0) {
            this.currentPiece.x = 0;
        } else if (this.currentPiece.x + this.currentPiece.shape[0].length > this.cols) {
            this.currentPiece.x = this.cols - this.currentPiece.shape[0].length;
        }
        
        this.drawNextPiece();
    }
    
    checkCollision(piece, dx, dy) {
        for (let y = 0; y < piece.shape.length; y++) {
            for (let x = 0; x < piece.shape[y].length; x++) {
                if (piece.shape[y][x]) {
                    const newX = piece.x + x + dx;
                    const newY = piece.y + y + dy;
                    
                    // Check boundary collision
                    if (newX < 0 || newX >= this.cols || newY >= this.rows) {
                        return true;
                    }
                    
                    // Check collision with other pieces
                    if (newY >= 0 && this.board[newY][newX]) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
    movePiece(dx, dy) {
        if (!this.checkCollision(this.currentPiece, dx, dy)) {
            this.currentPiece.x += dx;
            this.currentPiece.y += dy;
            this.draw();
            return true;
        } else if (dy > 0) {
            // Ensure piece is placed in correct position
            this.placePiece();
            return false;
        }
        return false;
    }
    
    rotatePiece() {
        const piece = this.currentPiece;
        const rotations = this.pieces[piece.pieceIndex];
        const newRotationIndex = (piece.rotationIndex + 1) % rotations.length;
        const newShape = rotations[newRotationIndex];
        
        const tempPiece = {
            ...piece,
            shape: newShape,
            rotationIndex: newRotationIndex
        };
        
        if (!this.checkCollision(tempPiece, 0, 0)) {
            this.currentPiece.shape = newShape;
            this.currentPiece.rotationIndex = newRotationIndex;
            this.draw();
            return true;
        }
        return false;
    }
    
    dropPiece() {
        let dropDistance = 0;
        while (!this.checkCollision(this.currentPiece, 0, 1)) {
            this.currentPiece.y++;
            dropDistance++;
        }
        this.placePiece();
        return dropDistance;
    }
    
    placePiece() {
        // Ensure piece is in valid position
        if (!this.currentPiece) return;
        
        for (let y = 0; y < this.currentPiece.shape.length; y++) {
            for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                if (this.currentPiece.shape[y][x]) {
                    const boardY = this.currentPiece.y + y;
                    const boardX = this.currentPiece.x + x;
                    
                    // Ensure position is within valid range
                    if (boardY >= 0 && boardY < this.rows && 
                        boardX >= 0 && boardX < this.cols) {
                        this.board[boardY][boardX] = this.currentPiece.color;
                    }
                }
            }
        }
        
        this.clearLines();
        this.newPiece();
        this.draw();
    }
    
    clearLines() {
        let linesCleared = 0;
        
        for (let y = this.rows - 1; y >= 0; y--) {
            if (this.board[y].every(cell => cell !== 0)) {
                this.board.splice(y, 1);
                this.board.unshift(Array(this.cols).fill(0));
                linesCleared++;
                y++; // Check the same line again
            }
        }
        
        if (linesCleared > 0) {
            this.lines += linesCleared;
            this.score += linesCleared * 100 * this.level;
            this.level = Math.floor(this.lines / 10) + 1;
            this.updateScore();
        }
    }
    
    draw() {
        // Clear canvas
        this.ctx.fillStyle = '#000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw board
        for (let y = 0; y < this.rows; y++) {
            for (let x = 0; x < this.cols; x++) {
                if (this.board[y][x]) {
                    this.ctx.fillStyle = this.board[y][x];
                    this.ctx.fillRect(x * this.blockSize, y * this.blockSize, this.blockSize - 1, this.blockSize - 1);
                }
            }
        }
        
        // Draw current piece
        if (this.currentPiece) {
            this.ctx.fillStyle = this.currentPiece.color;
            for (let y = 0; y < this.currentPiece.shape.length; y++) {
                for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                    if (this.currentPiece.shape[y][x]) {
                        this.ctx.fillRect(
                            (this.currentPiece.x + x) * this.blockSize,
                            (this.currentPiece.y + y) * this.blockSize,
                            this.blockSize - 1,
                            this.blockSize - 1
                        );
                    }
                }
            }
        }
        
        // Draw grid
        this.ctx.strokeStyle = '#333';
        this.ctx.lineWidth = 1;
        for (let x = 0; x <= this.cols; x++) {
            this.ctx.beginPath();
            this.ctx.moveTo(x * this.blockSize, 0);
            this.ctx.lineTo(x * this.blockSize, this.canvas.height);
            this.ctx.stroke();
        }
        for (let y = 0; y <= this.rows; y++) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y * this.blockSize);
            this.ctx.lineTo(this.canvas.width, y * this.blockSize);
            this.ctx.stroke();
        }
    }
    
    drawNextPiece() {
        this.nextCtx.fillStyle = '#000';
        this.nextCtx.fillRect(0, 0, this.nextCanvas.width, this.nextCanvas.height);
        
        if (this.nextPiece) {
            this.nextCtx.fillStyle = this.nextPiece.color;
            const offsetX = (this.nextCanvas.width - this.nextPiece.shape[0].length * 20) / 2;
            const offsetY = (this.nextCanvas.height - this.nextPiece.shape.length * 20) / 2;
            
            for (let y = 0; y < this.nextPiece.shape.length; y++) {
                for (let x = 0; x < this.nextPiece.shape[y].length; x++) {
                    if (this.nextPiece.shape[y][x]) {
                        this.nextCtx.fillRect(
                            offsetX + x * 20,
                            offsetY + y * 20,
                            19, 19
                        );
                    }
                }
            }
        }
    }
    
    start() {
        this.gameRunning = true;
        this.gamePaused = false;
        this.newPiece();
        this.gameLoop();
    }
    
    pause() {
        this.gamePaused = true;
    }
    
    resume() {
        this.gamePaused = false;
        this.gameLoop();
    }
    
    togglePause() {
        if (this.gamePaused) {
            this.resume();
        } else {
            this.pause();
        }
    }
    
    reset() {
        this.board = Array(this.rows).fill().map(() => Array(this.cols).fill(0));
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.gameRunning = false;
        this.gamePaused = false;
        this.currentPiece = null;
        this.nextPiece = null;
        this.updateScore();
        this.draw();
        this.drawNextPiece();
    }
    
    gameOver() {
        this.gameRunning = false;
        this.gamePaused = false;
        
        const highScore = this.getHighScore();
        if (this.score > highScore) {
            this.saveHighScore(this.score);
            document.getElementById('high-score-msg').classList.remove('hidden');
        } else {
            document.getElementById('high-score-msg').classList.add('hidden');
        }
        
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('final-lines').textContent = this.lines;
        document.getElementById('final-level').textContent = this.level;
        document.getElementById('game-over').classList.remove('hidden');
    }
    
    gameLoop() {
        if (!this.gameRunning || this.gamePaused) return;
        
        // Use more precise movement logic
        const moved = this.movePiece(0, 1);
        
        // If piece cannot move, ensure it is placed correctly
        if (!moved && this.currentPiece) {
            this.placePiece();
        }
        
        const speed = Math.max(100, 1000 - (this.level - 1) * 100);
        setTimeout(() => this.gameLoop(), speed);
    }
    
    updateScore() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('level').textContent = this.level;
        document.getElementById('lines').textContent = this.lines;
        document.getElementById('high-score').textContent = this.getHighScore();
    }
    
    getHighScore() {
        return parseInt(localStorage.getItem('tetrisHighScore') || '0');
    }
    
    saveHighScore(score) {
        localStorage.setItem('tetrisHighScore', score.toString());
    }
    
    loadHighScore() {
        document.getElementById('high-score').textContent = this.getHighScore();
    }
}
