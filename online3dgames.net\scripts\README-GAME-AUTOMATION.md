# 游戏自动化管理系统

这个自动化系统可以简化游戏添加流程，自动管理推荐系统和SEO内容。

## 📁 文件结构

```
online3dgames.net/
├── scripts/                # 脚本和配置文件夹
│   ├── game-config.js      # 游戏配置和分类定义
│   ├── template-generator.js # 模板生成器
│   ├── update-all-games.js # 批量更新脚本
│   ├── meta.json           # 网站元数据配置
│   └── README-GAME-AUTOMATION.md # 使用说明
├── backups/                # 备份文件夹
└── [游戏目录]/             # 各个游戏的页面文件
```

## 🎯 主要功能

### 1. 统一游戏配置 (game-config.js)
- **游戏分类管理**: 定义所有游戏类别和描述
- **完整游戏数据**: 包含SEO标题、描述、评分等
- **自动布局计算**: 蜂巢布局规则自动验证
- **分类查询函数**: 按类别获取游戏列表

### 2. 模板自动生成 (template-generator.js)
- **推荐卡片模板**: 自动生成游戏推荐卡片
- **蜂巢布局生成**: 自动计算相邻行奇数差规则
- **同类游戏推荐**: 基于分类自动推荐
- **其他游戏推荐**: 跨分类游戏推荐
- **首页分类面板**: 自动生成分类页面

### 3. 批量更新系统 (update-all-games.js)
- **自动备份**: 更新前自动创建备份
- **批量更新**: 一次性更新所有游戏页面
- **首页更新**: 自动更新首页分类
- **站点地图**: 自动生成sitemap.xml
- **布局验证**: 验证蜂巢布局规则

## 🚀 使用方法

### 添加新游戏

1. **在 game-config.js 中添加游戏数据**:
```javascript
'new-game': {
    name: 'New Game',
    icon: '🎮',
    rating: '4.5',
    difficulty: '⭐⭐⭐',
    description: 'Game description...',
    tags: ['Tag1', 'Tag2'],
    url: '/new-game',
    bgClass: 'newgame-bg',
    category: 'puzzle', // 选择合适的分类
    seoTitle: 'SEO Title...',
    seoDescription: 'SEO Description...'
}
```

2. **更新分类游戏列表**:
```javascript
gameCategories['puzzle'].games.push('new-game');
```

3. **运行批量更新**:
```bash
cd scripts
node update-all-games.js
```

### 修改游戏分类

1. **在 game-config.js 中修改游戏的 category 属性**
2. **更新对应分类的 games 数组**
3. **运行批量更新脚本**

### 验证布局

```bash
cd scripts
node -e "require('./update-all-games.js').validateAllLayouts()"
```

## 📋 游戏分类

当前支持的游戏分类：

- **card-games**: 卡牌游戏 (Hearts, Solitaire, Spider Solitaire, Texas Hold'em, Spades)
- **blackjack**: 21点游戏 (Blackjack, Practice, Free Bet, Pontoon)
- **puzzle**: 益智游戏 (Sudoku, 2048, Tetris, Memory Game)
- **arcade**: 街机游戏 (Snake, Breakout, Reaction Test)
- **relaxation**: 放松游戏 (Pop-it, Fidget Spinner, Breathing Ball, Drawing Wall, Bubble Float)
- **strategy**: 策略游戏 (Chess)
- **other**: 其他游戏 (Particle Trail, Typing Machine, Virtual Pet)

## 🔧 蜂巢布局规则

系统自动确保：
- 相邻行游戏数量相差奇数个
- 最大行数不超过4个游戏
- 自动计算offset-row类

## 📝 SEO优化

每个游戏包含：
- **seoTitle**: 优化的页面标题
- **seoDescription**: 搜索引擎描述
- **tags**: 游戏标签
- **自动sitemap**: 自动生成站点地图

## 🔄 批量操作

### 更新所有游戏页面
```bash
cd scripts
node update-all-games.js
```

### 只更新首页
```bash
cd scripts
node -e "require('./update-all-games.js').updateHomepageCategories()"
```

### 只更新特定游戏
```bash
cd scripts
node -e "require('./update-all-games.js').updateGamePageRecommendations('blackjack')"
```

### 更新站点地图
```bash
cd scripts
node -e "require('./update-all-games.js').updateSitemap()"
```

## 💾 备份系统

- 所有更新前自动创建备份
- 备份文件存储在 `backups/` 目录
- 备份文件名包含时间戳
- 可以手动恢复任何备份

## ⚠️ 注意事项

1. **运行前确保Node.js环境**
2. **首次运行前建议手动备份重要文件**
3. **修改game-config.js后需要运行更新脚本**
4. **新游戏需要先创建对应的游戏目录和基础文件**
5. **确保游戏URL路径与实际目录结构匹配**

## 🎯 优势

- **一次配置，处处生效**: 在game-config.js中配置一次，自动更新所有相关页面
- **SEO友好**: 自动生成优化的标题、描述和站点地图
- **布局一致**: 自动维护蜂巢布局规则
- **安全可靠**: 自动备份，可随时恢复
- **易于维护**: 集中管理，减少重复工作
