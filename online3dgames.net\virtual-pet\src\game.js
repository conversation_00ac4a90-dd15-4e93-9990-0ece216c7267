// Virtual Pet UI Controller - Additional enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        switch(e.key) {
            case 'f':
            case 'F':
                document.getElementById('feed-btn').click();
                break;
            case 'p':
            case 'P':
                document.getElementById('play-btn').click();
                break;
            case 'c':
            case 'C':
                document.getElementById('clean-btn').click();
                break;
            case 's':
            case 'S':
                document.getElementById('sleep-btn').click();
                break;
            case 'r':
            case 'R':
                document.getElementById('rename-btn').click();
                break;
        }
    });
    
    // Add visual feedback for action buttons
    document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(-2px) scale(0.95)';
        });
        
        btn.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-2px) scale(1)';
        });
        
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Add hover effects for canvas
    const canvas = document.getElementById('pet-canvas');
    canvas.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.02)';
    });
    
    canvas.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
    });
    
    // Add click feedback for canvas
    canvas.addEventListener('mousedown', function() {
        this.style.transform = 'scale(0.98)';
    });
    
    canvas.addEventListener('mouseup', function() {
        this.style.transform = 'scale(1.02)';
    });
});
