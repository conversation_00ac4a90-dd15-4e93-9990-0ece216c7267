
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 40px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transform: scale(0.7);
  transition: all 0.3s ease;
  overflow-y: auto;
}

.modal-overlay.show .modal {
  transform: scale(1);
}

.modal-title {
  color: white;
  font-size: 32px;
  margin: 0 0 20px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: bold;
}

.modal-content {
  color: white;
  font-size: 18px;
  margin-bottom: 30px;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  white-space: pre-line;
}

.modal-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.modal-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  
  min-width: 120px;
  outline: none;
}

.modal-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.modal-btn:active {
  transform: translateY(0);
}

.modal-btn.primary {
  background-color: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border-color: rgba(255, 255, 255, 0.9);
}

.modal-btn.primary:hover {
  background-color: white;
  color: #5a67d8;
  border-color: white;
}

.modal-btn.secondary {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.4);
}

.modal-btn.danger {
  background-color: rgba(231, 76, 60, 0.8);
  border-color: rgba(231, 76, 60, 0.9);
}

.modal-btn.danger:hover {
  background-color: rgba(231, 76, 60, 0.9);
  border-color: #e74c3c;
}

.modal-btn.success {
  background-color: rgba(46, 204, 113, 0.8);
  border-color: rgba(46, 204, 113, 0.9);
}

.modal-btn.success:hover {
  background-color: rgba(46, 204, 113, 0.9);
  border-color: #2ecc71;
}

.modal.info {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.modal.success {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
}

.modal.warning {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

.modal.error {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
}

.modal.confirm {
  background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
}

@media (max-width: 600px) {
  .modal {
    padding: 30px 20px;
    margin: 20px;
  }
  
  .modal-title {
    font-size: 24px;
  }
  
  .modal-content {
    font-size: 16px;
  }
  
  .modal-buttons {
    flex-direction: column;
    gap: 15px;
  }
  
  .modal-btn {
    width: 100%;
    min-width: auto;
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.7);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes modalFadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.7);
  }
}

.modal-overlay.hidden {
  display: none;
}
