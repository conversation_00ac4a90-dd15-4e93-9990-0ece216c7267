// Particle Trail UI Controller
let game;

document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('particle-canvas');
    game = new ParticleTrail(canvas);
    
    // Button event listeners
    document.getElementById('canvas-start-btn').addEventListener('click', startGame);
    document.getElementById('home-btn').addEventListener('click', goHome);
    
    // Close game start modal when clicking outside
    document.getElementById('game-start').addEventListener('click', function(e) {
        if (e.target === this) {
            startGame();
        }
    });
});

function startGame() {
    // Hide start overlay
    document.getElementById('game-start').classList.add('hidden');
    // Enable game
    game.start();
}

function goHome() {
    window.location.href = '/';
}

// Handle window resize
window.addEventListener('resize', function() {
    // Canvas maintains its size, no need to redraw
});

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (!game.gameStarted) return;
    
    switch(e.key) {
        case ' ':
            e.preventDefault();
            game.togglePause();
            break;
        case 'c':
        case 'C':
            game.clearParticles();
            break;
        case 's':
        case 'S':
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                game.takeScreenshot();
            }
            break;
        case '1':
            game.setMode('fireworks');
            game.updateModeButtons();
            break;
        case '2':
            game.setMode('galaxy');
            game.updateModeButtons();
            break;
        case '3':
            game.setMode('rainbow');
            game.updateModeButtons();
            break;
        case '4':
            game.setMode('neon');
            game.updateModeButtons();
            break;
    }
});
