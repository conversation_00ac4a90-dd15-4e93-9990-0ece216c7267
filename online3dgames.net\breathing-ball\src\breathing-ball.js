// Breathing Ball Game Logic
class BreathingBallGame {
    constructor() {
        this.gameStarted = false;
        
        // Breathing settings
        this.breathRate = 4; // seconds per breath cycle
        this.breathRates = [2, 3, 4, 5, 6]; // available rates
        this.currentRateIndex = 2; // default to 4 seconds
        
        // Color themes
        this.colorThemes = [
            'rainbow',
            'ocean',
            'sunset',
            'forest',
            'lavender'
        ];
        this.currentTheme = 0;
        
        // Stats
        this.relaxTime = 0; // in minutes
        this.breathCount = 0;
        this.startTime = 0;
        this.lastBreathTime = 0;
        
        // Elements
        this.breathingBall = document.getElementById('breathing-ball');
        this.breathInstruction = document.getElementById('breath-instruction');
        
        this.setupEventListeners();
        this.updateStats();
    }
    
    setupEventListeners() {
        document.getElementById('canvas-start-btn').addEventListener('click', () => this.start());
        document.getElementById('speed-btn').addEventListener('click', () => this.changeSpeed());
        document.getElementById('color-btn').addEventListener('click', () => this.changeColor());
        document.getElementById('reset-btn').addEventListener('click', () => this.reset());
        document.getElementById('home-btn').addEventListener('click', () => this.goHome());
        
        // Close start modal when clicking outside
        document.getElementById('game-start').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.start();
            }
        });
    }
    
    start() {
        // Hide start overlay
        document.getElementById('game-start').classList.add('hidden');
        this.gameStarted = true;
        this.startTime = Date.now();
        this.lastBreathTime = Date.now();
        
        this.startBreathingCycle();
        this.startTimer();
    }
    
    startBreathingCycle() {
        if (!this.gameStarted) return;
        
        // Update breathing animation
        this.updateBreathingAnimation();
        
        // Update instruction text
        this.updateInstructionText();
        
        // Count breaths (every full cycle)
        this.countBreaths();
    }
    
    updateBreathingAnimation() {
        // Remove existing animation classes
        this.breathingBall.classList.remove('slow', 'fast');
        
        // Apply speed class based on breath rate
        if (this.breathRate >= 5) {
            this.breathingBall.classList.add('slow');
        } else if (this.breathRate <= 2) {
            this.breathingBall.classList.add('fast');
        }
        
        // Update CSS animation duration
        this.breathingBall.style.animationDuration = `${this.breathRate}s`;
        this.breathInstruction.style.animationDuration = `${this.breathRate}s`;
        
        // Update guide text animations
        const inhaleText = document.querySelector('.inhale-text');
        const exhaleText = document.querySelector('.exhale-text');
        if (inhaleText && exhaleText) {
            inhaleText.style.animationDuration = `${this.breathRate}s`;
            exhaleText.style.animationDuration = `${this.breathRate}s`;
        }
    }
    
    updateInstructionText() {
        const cycleTime = this.breathRate * 1000; // Convert to milliseconds
        const halfCycle = cycleTime / 2;
        
        const updateText = () => {
            if (!this.gameStarted) return;
            
            const now = Date.now();
            const elapsed = (now - this.lastBreathTime) % cycleTime;
            
            if (elapsed < halfCycle) {
                this.breathInstruction.textContent = 'Inhale';
            } else {
                this.breathInstruction.textContent = 'Exhale';
            }
            
            requestAnimationFrame(updateText);
        };
        
        updateText();
    }
    
    countBreaths() {
        const breathInterval = setInterval(() => {
            if (!this.gameStarted) {
                clearInterval(breathInterval);
                return;
            }
            
            this.breathCount++;
            this.updateStats();
        }, this.breathRate * 1000);
    }
    
    startTimer() {
        const timer = setInterval(() => {
            if (!this.gameStarted) {
                clearInterval(timer);
                return;
            }
            
            const elapsed = (Date.now() - this.startTime) / 1000 / 60; // Convert to minutes
            this.relaxTime = elapsed;
            this.updateStats();
        }, 1000);
    }
    
    changeSpeed() {
        this.currentRateIndex = (this.currentRateIndex + 1) % this.breathRates.length;
        this.breathRate = this.breathRates[this.currentRateIndex];
        
        if (this.gameStarted) {
            this.updateBreathingAnimation();
        }
        
        this.updateStats();
        
        // Update button text
        const btn = document.getElementById('speed-btn');
        btn.textContent = `${this.breathRate} seconds per breath`;
        
        return this.breathRate;
    }
    
    changeColor() {
        this.currentTheme = (this.currentTheme + 1) % this.colorThemes.length;
        const theme = this.colorThemes[this.currentTheme];
        
        this.applyColorTheme(theme);
        
        // Update button text
        const btn = document.getElementById('color-btn');
        const themeNames = {
            rainbow: 'Rainbow',
            ocean: 'Ocean',
            sunset: 'Sunset',
            forest: 'Forest',
            lavender: 'Lavender'
        };
        btn.textContent = themeNames[theme];
        
        return theme;
    }
    
    applyColorTheme(theme) {
        const ballInner = document.querySelector('.ball-inner');
        const ballGlow = document.querySelector('.ball-glow');
        
        const themes = {
            rainbow: {
                inner: `conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7, #dda0dd, #ff6b6b)`,
                glow: `conic-gradient(from 0deg, rgba(255,107,107,0.3), rgba(78,205,196,0.3), rgba(69,183,209,0.3), rgba(150,206,180,0.3), rgba(255,234,167,0.3), rgba(221,160,221,0.3), rgba(255,107,107,0.3))`
            },
            ocean: {
                inner: `conic-gradient(from 0deg, #4ecdc4, #45b7d1, #96ceb4, #4ecdc4, #45b7d1, #96ceb4, #4ecdc4)`,
                glow: `conic-gradient(from 0deg, rgba(78,205,196,0.3), rgba(69,183,209,0.3), rgba(150,206,180,0.3), rgba(78,205,196,0.3), rgba(69,183,209,0.3), rgba(150,206,180,0.3), rgba(78,205,196,0.3))`
            },
            sunset: {
                inner: `conic-gradient(from 0deg, #ff6b6b, #ffeaa7, #ff8e9b, #ff6b6b, #ffeaa7, #ff8e9b, #ff6b6b)`,
                glow: `conic-gradient(from 0deg, rgba(255,107,107,0.3), rgba(255,234,167,0.3), rgba(255,142,155,0.3), rgba(255,107,107,0.3), rgba(255,234,167,0.3), rgba(255,142,155,0.3), rgba(255,107,107,0.3))`
            },
            forest: {
                inner: `conic-gradient(from 0deg, #96ceb4, #6bcf7f, #4ecdc4, #96ceb4, #6bcf7f, #4ecdc4, #96ceb4)`,
                glow: `conic-gradient(from 0deg, rgba(150,206,180,0.3), rgba(107,207,127,0.3), rgba(78,205,196,0.3), rgba(150,206,180,0.3), rgba(107,207,127,0.3), rgba(78,205,196,0.3), rgba(150,206,180,0.3))`
            },
            lavender: {
                inner: `conic-gradient(from 0deg, #dda0dd, #c8a2c8, #b19cd9, #dda0dd, #c8a2c8, #b19cd9, #dda0dd)`,
                glow: `conic-gradient(from 0deg, rgba(221,160,221,0.3), rgba(200,162,200,0.3), rgba(177,156,217,0.3), rgba(221,160,221,0.3), rgba(200,162,200,0.3), rgba(177,156,217,0.3), rgba(221,160,221,0.3))`
            }
        };
        
        if (ballInner && ballGlow && themes[theme]) {
            ballInner.style.background = themes[theme].inner;
            ballGlow.style.background = themes[theme].glow;
        }
    }
    
    reset() {
        this.relaxTime = 0;
        this.breathCount = 0;
        this.startTime = Date.now();
        this.lastBreathTime = Date.now();
        this.updateStats();
        
        // Add visual feedback
        const btn = document.getElementById('reset-btn');
        btn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            btn.style.transform = 'scale(1)';
        }, 100);
    }
    
    updateStats() {
        document.getElementById('breath-rate').textContent = this.breathRate;
        document.getElementById('relax-time').textContent = this.relaxTime.toFixed(1);
        document.getElementById('breath-count').textContent = this.breathCount;
    }
    
    goHome() {
        window.location.href = '/';
    }
}

// Initialize the game when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new BreathingBallGame();
});
