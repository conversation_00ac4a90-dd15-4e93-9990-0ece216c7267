/* Drawing Wall Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.2);
    padding: 15px 25px;
    border-radius: 15px;
    
}

.header h1 {
    font-size: 2.2rem;
    color: #ff6b9d;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    margin: 0;
}

.home-btn {
    background: rgba(255, 255, 255, 0.3);
    color: #ff6b9d;
    border: 2px solid rgba(255, 107, 157, 0.3);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.home-btn:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Tool Panel */
.tool-panel {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.2);
    padding: 20px;
    border-radius: 15px;
    
}

.brush-tools, .color-tools, .action-tools {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.tool-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.tool-group label {
    font-size: 0.9rem;
    font-weight: bold;
    color: #ff6b9d;
}

.tool-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.3);
    outline: none;
    cursor: pointer;
}

.tool-group span {
    font-size: 0.8rem;
    color: #666;
    text-align: center;
}

.color-presets {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-bottom: 10px;
}

.color-preset {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.3s ease;
}

.color-preset:hover, .color-preset.active {
    border-color: white;
    transform: scale(1.1);
}

#color-picker {
    width: 100%;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    margin-bottom: 10px;
}

.tool-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 20px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tool-btn.primary {
    background: linear-gradient(135deg, #ff6b9d, #ff8e9b);
    color: white;
}

.tool-btn.secondary {
    background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
    color: white;
}

.tool-btn.danger {
    background: linear-gradient(135deg, #ff6b6b, #ff8e9b);
    color: white;
}

.tool-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.tool-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Canvas Container */
.canvas-container {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

#drawing-canvas {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    background: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    cursor: crosshair;
}

/* Game Overlays */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
    
    z-index: 1000;
}

.game-overlay.hidden {
    display: none;
}

.game-start-content {
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8e9b 100%);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: scale(0.8);
    animation: modalAppear 0.3s ease forwards;
    min-width: 300px;
    color: white;
}

@keyframes modalAppear {
    to {
        transform: scale(1);
    }
}

.game-start-content h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.game-start-content p {
    font-size: 1.1rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.game-start-content .tip {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 25px;
}

/* Instructions */
.instructions {
    background: rgba(255, 255, 255, 0.2);
    padding: 25px;
    border-radius: 15px;
    
}

.instructions h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
    color: #ff6b9d;
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.instruction-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 15px;
}

.instruction-item .icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.instruction-item .desc {
    font-size: 0.9rem;
    line-height: 1.4;
    color: #333;
}

.instruction-item .desc strong {
    color: #ff6b9d;
    display: block;
    margin-bottom: 5px;
}

.tips {
    background: rgba(255, 107, 157, 0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #ff6b9d;
}

.tips h4 {
    margin-bottom: 15px;
    color: #ff6b9d;
    font-size: 1.2rem;
}

.tips ul {
    list-style: none;
    padding: 0;
}

.tips li {
    padding: 5px 0;
    padding-left: 20px;
    position: relative;
    color: #333;
}

.tips li:before {
    content: "🎨";
    position: absolute;
    left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
    }
    
    .header h1 {
        font-size: 1.8rem;
    }
    
    .tool-panel {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .brush-tools, .color-tools, .action-tools {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .tool-group {
        flex: 1;
        min-width: 120px;
    }
    
    #drawing-canvas {
        width: 100%;
        max-width: 400px;
        height: auto;
    }
    
    .instruction-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .color-presets {
        grid-template-columns: repeat(8, 1fr);
    }
    
    .color-preset {
        width: 25px;
        height: 25px;
    }
    
    .tool-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
}
