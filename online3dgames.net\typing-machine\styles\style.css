/* Typing Machine Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.2);
    padding: 15px 25px;
    border-radius: 15px;
    
}

.header h1 {
    font-size: 2.2rem;
    color: #8B4513;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    margin: 0;
}

.home-btn {
    background: rgba(255, 255, 255, 0.3);
    color: #8B4513;
    border: 2px solid rgba(139, 69, 19, 0.3);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.home-btn:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Settings Panel */
.settings-panel {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 20px;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.2);
    padding: 20px;
    border-radius: 15px;
    
    align-items: center;
}

.sound-settings {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.setting-group label {
    font-size: 0.9rem;
    font-weight: bold;
    color: #8B4513;
}

.setting-group select {
    padding: 8px;
    border: 2px solid rgba(139, 69, 19, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    font-family: inherit;
    cursor: pointer;
}

.setting-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(139, 69, 19, 0.3);
    outline: none;
    cursor: pointer;
}

.setting-group span {
    font-size: 0.8rem;
    color: #666;
    text-align: center;
}

.typing-modes {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.mode-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 20px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    background: rgba(255, 255, 255, 0.3);
    color: #8B4513;
    border: 2px solid transparent;
    font-family: inherit;
}

.mode-btn:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.mode-btn.active {
    background: rgba(139, 69, 19, 0.2);
    border-color: rgba(139, 69, 19, 0.5);
    box-shadow: 0 0 15px rgba(139, 69, 19, 0.3);
}

.stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
}

.stat-item .label {
    display: block;
    font-size: 0.8rem;
    color: #8B4513;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-item .value {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
}

/* Typing Container */
.typing-container {
    position: relative;
    margin-bottom: 20px;
}

.typewriter-paper {
    background: #f8f8f0;
    border: 1px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
}

.paper-header {
    background: linear-gradient(135deg, #e8e8e0 0%, #f0f0e8 100%);
    padding: 15px 20px;
    border-bottom: 2px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.paper-holes {
    display: flex;
    gap: 10px;
}

.hole {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ccc;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.paper-title {
    font-size: 1.1rem;
    font-weight: bold;
    color: #8B4513;
}

#typing-area {
    width: 100%;
    min-height: 300px;
    max-height: 400px;
    padding: 30px;
    border: none;
    outline: none;
    background: transparent;
    font-family: 'Courier New', monospace;
    font-size: 1rem;
    line-height: 1.6;
    color: #333;
    resize: none;
    overflow-y: auto;
}

#typing-area::placeholder {
    color: #999;
    font-style: italic;
}

.cursor-blink {
    position: absolute;
    width: 2px;
    height: 20px;
    background: #333;
    animation: blink 1s infinite;
    pointer-events: none;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.prompt-text {
    margin-top: 20px;
    padding: 20px;
    background: rgba(139, 69, 19, 0.1);
    border-radius: 10px;
    border-left: 4px solid #8B4513;
    font-style: italic;
    line-height: 1.6;
}

.prompt-text p {
    margin-bottom: 10px;
}

.prompt-text strong {
    color: #8B4513;
}

/* Game Overlays */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    
    z-index: 1000;
}

.game-overlay.hidden {
    display: none;
}

.game-start-content {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: scale(0.8);
    animation: modalAppear 0.3s ease forwards;
    min-width: 300px;
    color: white;
}

@keyframes modalAppear {
    to {
        transform: scale(1);
    }
}

.game-start-content h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.game-start-content p {
    font-size: 1.1rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.game-start-content .tip {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 25px;
}

/* Action Panel */
.action-panel {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.action-btn, .control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-family: inherit;
}

.action-btn.primary, .control-btn.primary {
    background: linear-gradient(135deg, #8B4513, #A0522D);
    color: white;
}

.action-btn.secondary, .control-btn.secondary {
    background: linear-gradient(135deg, #CD853F, #DEB887);
    color: white;
}

.action-btn.danger, .control-btn.danger {
    background: linear-gradient(135deg, #DC143C, #B22222);
    color: white;
}

.action-btn.success, .control-btn.success {
    background: linear-gradient(135deg, #228B22, #32CD32);
    color: white;
}

.action-btn:hover, .control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Instructions */
.instructions {
    background: rgba(255, 255, 255, 0.2);
    padding: 25px;
    border-radius: 15px;
    
}

.instructions h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
    color: #8B4513;
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.instruction-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 15px;
}

.instruction-item .icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.instruction-item .desc {
    font-size: 0.9rem;
    line-height: 1.4;
    color: #333;
}

.instruction-item .desc strong {
    color: #8B4513;
    display: block;
    margin-bottom: 5px;
}

.tips {
    background: rgba(139, 69, 19, 0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #8B4513;
}

.tips h4 {
    margin-bottom: 15px;
    color: #8B4513;
    font-size: 1.2rem;
}

.tips ul {
    list-style: none;
    padding: 0;
}

.tips li {
    padding: 5px 0;
    padding-left: 20px;
    position: relative;
    color: #333;
}

.tips li:before {
    content: "⌨️";
    position: absolute;
    left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
    }
    
    .header h1 {
        font-size: 1.8rem;
    }
    
    .settings-panel {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .sound-settings {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    
    .setting-group {
        flex: 1;
        min-width: 120px;
        margin: 0 5px;
    }
    
    .typing-modes {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .stats {
        flex-direction: row;
        justify-content: space-around;
    }
    
    .action-panel {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn, .control-btn {
        width: 100%;
        max-width: 200px;
    }
    
    .instruction-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .mode-btn, .action-btn, .control-btn {
        padding: 10px 15px;
        font-size: 0.8rem;
    }
    
    .sound-settings, .typing-modes, .stats {
        flex-direction: column;
    }
    
    #typing-area {
        padding: 20px;
        font-size: 0.9rem;
    }
}
