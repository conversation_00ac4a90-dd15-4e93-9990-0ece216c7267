class MemoryGame {
  constructor(cards) {
    this.allCards = cards;
    this.cards = [];
    this.pickedCards = [];
    this.pairsClicked = 0;
    this.pairsGuessed = 0;
    this.currentLevel = 1;
    this.maxLevel = 3;


    this.levelConfig = {
      1: { pairs: 6, description: 'Easy' },
      2: { pairs: 12, description: 'Medium' },
      3: { pairs: 18, description: 'Hard' }
    };
  }

  setLevel(level) {
    this.currentLevel = level;
    this.generateCardsForLevel();
    this.shuffleCards();
  }

  generateCardsForLevel() {
    const pairsNeeded = this.levelConfig[this.currentLevel].pairs;


    const uniqueCards = this.allCards.slice(0, 12);


    let selectedCards = [];

    if (pairsNeeded <= 12) {
      // For levels 1 and 2, select the needed number of unique cards
      selectedCards = uniqueCards.slice(0, pairsNeeded);
    } else {
      // For level 3, use all 12 unique cards and repeat 6 of them
      selectedCards = [...uniqueCards];
      const additionalCards = uniqueCards.slice(0, pairsNeeded - 12);
      selectedCards = [...selectedCards, ...additionalCards];
    }

    // Create pairs by duplicating each card
    this.cards = [...selectedCards, ...selectedCards];
  }

  shuffleCards() {
    // ... write your code here
    if (!this.cards || this.cards.length === 0) {
      return undefined;
    }

    // Fisher-Yates shuffle algorithm
    for (let i = this.cards.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [this.cards[i], this.cards[j]] = [this.cards[j], this.cards[i]];
    }
  }

  checkIfPair(card1, card2) {
    // ... write your code here
    this.pairsClicked++;

    if (card1 === card2) {
      this.pairsGuessed++;
      return true;
    }
    return false;
  }

  checkIfFinished() {
    // ... write your code here
    return this.pairsGuessed === this.levelConfig[this.currentLevel].pairs;
  }

  canAdvanceToNextLevel() {
    return this.currentLevel < this.maxLevel && this.checkIfFinished();
  }

  advanceToNextLevel() {
    if (this.canAdvanceToNextLevel()) {
      this.currentLevel++;
      this.resetLevelStats();
      this.setLevel(this.currentLevel);
      return true;
    }
    return false;
  }

  resetLevelStats() {
    this.pickedCards = [];
    this.pairsClicked = 0;
    this.pairsGuessed = 0;
  }

  resetGame() {
    this.currentLevel = 1;
    this.resetLevelStats();
    this.setLevel(this.currentLevel);
  }
}
