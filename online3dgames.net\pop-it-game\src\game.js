// Pop-it Game UI Controller
let game;

document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('game-canvas');
    game = new PopItGame(canvas);
    
    // Button event listeners
    document.getElementById('canvas-start-btn').addEventListener('click', startGame);
    document.getElementById('reset-btn').addEventListener('click', resetGame);
    document.getElementById('size-btn').addEventListener('click', toggleSize);
    document.getElementById('sound-btn').addEventListener('click', toggleSound);
    document.getElementById('home-btn').addEventListener('click', goHome);
    
    // Close game start modal when clicking outside
    document.getElementById('game-start').addEventListener('click', function(e) {
        if (e.target === this) {
            startGame();
        }
    });
});

function startGame() {
    // Hide start overlay
    document.getElementById('game-start').classList.add('hidden');
    // Enable game
    game.start();
}

function resetGame() {
    game.reset();
    // Add visual feedback
    const btn = document.getElementById('reset-btn');
    btn.style.transform = 'scale(0.95)';
    setTimeout(() => {
        btn.style.transform = 'scale(1)';
    }, 100);
}

function toggleSize() {
    game.toggleSize();
    const btn = document.getElementById('size-btn');
    btn.textContent = game.bubbleSize === 35 ? 'Switch size' : 'Big bubbles';
    
    // Add visual feedback
    btn.style.transform = 'scale(0.95)';
    setTimeout(() => {
        btn.style.transform = 'scale(1)';
    }, 100);
}

function toggleSound() {
    const soundEnabled = game.toggleSound();
    const btn = document.getElementById('sound-btn');
    btn.textContent = soundEnabled ? '🔊 Sound' : '🔇 Mute';
    
    // Add visual feedback
    btn.style.transform = 'scale(0.95)';
    setTimeout(() => {
        btn.style.transform = 'scale(1)';
    }, 100);
}

function goHome() {
    window.location.href = '/';
}

// Handle window resize
window.addEventListener('resize', function() {
    if (game) {
        // Redraw the game when window is resized
        game.draw();
    }
});

// Add some fun interactions
document.addEventListener('keydown', function(e) {
    if (!game.gameStarted) return;
    
    switch(e.key) {
        case 'r':
        case 'R':
            resetGame();
            break;
        case 's':
        case 'S':
            toggleSound();
            break;
        case ' ':
            e.preventDefault();
            toggleSize();
            break;
    }
});

// Add visual feedback for canvas interactions
document.getElementById('game-canvas').addEventListener('mousedown', function() {
    this.style.transform = 'scale(0.98)';
});

document.getElementById('game-canvas').addEventListener('mouseup', function() {
    this.style.transform = 'scale(1)';
});

document.getElementById('game-canvas').addEventListener('mouseleave', function() {
    this.style.transform = 'scale(1)';
});
