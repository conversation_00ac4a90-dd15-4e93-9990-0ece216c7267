/* Virtual Pet Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    min-height: 100vh;
    color: white;
    overflow-x: hidden;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px 25px;
    border-radius: 15px;
    
}

.header h1 {
    font-size: 2.2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin: 0;
}

.home-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.home-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Status Panel */
.status-panel {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
    margin-bottom: 20px;
}

.pet-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.pet-name {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pet-name span {
    font-size: 1.5rem;
    font-weight: bold;
}

.rename-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.rename-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.pet-level {
    font-size: 1.2rem;
    font-weight: bold;
    color: #FFD700;
}

.pet-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-bar {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-bar label {
    font-size: 0.9rem;
    font-weight: bold;
}

.bar {
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
    position: relative;
}

.fill.happiness {
    background: linear-gradient(90deg, #ff6b6b, #ff8e9b);
}

.fill.hunger {
    background: linear-gradient(90deg, #4ecdc4, #45b7d1);
}

.fill.cleanliness {
    background: linear-gradient(90deg, #96ceb4, #ffeaa7);
}

.stat-bar span {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.8rem;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.action-btn {
    padding: 12px 15px;
    border: none;
    border-radius: 20px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.action-btn.feed {
    background: linear-gradient(135deg, #4ecdc4, #45b7d1);
    color: white;
}

.action-btn.play {
    background: linear-gradient(135deg, #ff6b6b, #ff8e9b);
    color: white;
}

.action-btn.clean {
    background: linear-gradient(135deg, #96ceb4, #ffeaa7);
    color: white;
}

.action-btn.sleep {
    background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Pet Container */
.pet-container {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

#pet-canvas {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    cursor: pointer;
}

/* Game Overlays */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20px;
    
    z-index: 1000;
}

.game-overlay.hidden {
    display: none;
}

.game-start-content {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: scale(0.8);
    animation: modalAppear 0.3s ease forwards;
    min-width: 300px;
}

@keyframes modalAppear {
    to {
        transform: scale(1);
    }
}

.game-start-content h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.game-start-content p {
    font-size: 1.1rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.game-start-content .tip {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 25px;
}

.control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Interaction Area */
.interaction-area {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    pointer-events: none;
}

.speech-bubble {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    padding: 10px 15px;
    border-radius: 20px;
    position: relative;
    margin-bottom: 10px;
    max-width: 200px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.speech-bubble.show {
    opacity: 1;
    transform: translateY(0);
}

.speech-bubble::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid rgba(255, 255, 255, 0.9);
}

.mood-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 12px;
    border-radius: 15px;
    
    width: fit-content;
}

#mood-emoji {
    font-size: 1.5rem;
}

#mood-text {
    font-size: 0.9rem;
    font-weight: bold;
}

/* Care Panel */
.care-panel {
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 15px;
    
}

.care-panel h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.care-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.care-item {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
}

.care-item .icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    display: block;
}

.care-item .desc {
    font-size: 0.8rem;
    line-height: 1.4;
}

.care-item .desc strong {
    display: block;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.tips {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #fd79a8;
}

.tips h4 {
    margin-bottom: 15px;
    color: #fd79a8;
    font-size: 1.2rem;
}

.tips ul {
    list-style: none;
    padding: 0;
}

.tips li {
    padding: 5px 0;
    padding-left: 20px;
    position: relative;
    opacity: 0.9;
}

.tips li:before {
    content: "🐱";
    position: absolute;
    left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
    }
    
    .header h1 {
        font-size: 1.8rem;
    }
    
    .pet-stats {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        grid-template-columns: repeat(2, 1fr);
    }
    
    #pet-canvas {
        width: 100%;
        max-width: 400px;
        height: auto;
    }
    
    .care-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .care-grid {
        grid-template-columns: 1fr;
    }
    
    .action-btn {
        padding: 10px 12px;
        font-size: 0.8rem;
    }
}
