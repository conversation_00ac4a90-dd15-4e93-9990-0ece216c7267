// Tetris Game UI Controller
let game;

document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('game-canvas');
    const nextCanvas = document.getElementById('next-canvas');
    game = new TetrisGame(canvas, nextCanvas);
    
    // Initialize the game display
    game.draw();
    game.drawNextPiece();
    
    // Button event listeners
    document.getElementById('start-btn').addEventListener('click', startGame);
    document.getElementById('canvas-start-btn').addEventListener('click', startGame);
    document.getElementById('pause-btn').addEventListener('click', togglePause);
    document.getElementById('reset-btn').addEventListener('click', resetGame);
    document.getElementById('restart-btn').addEventListener('click', restartGame);
    document.getElementById('home-btn').addEventListener('click', goHome);

    // Mobile control buttons
    setupMobileControls();

    // Close game over modal when clicking outside
    document.getElementById('game-over').addEventListener('click', function(e) {
        if (e.target === this) {
            closeGameOver();
        }
    });
});

function startGame() {
    if (game.gameRunning) return;

    // Hide start overlay
    document.getElementById('game-start').classList.add('hidden');

    game.start();
    updateButtonStates();
}

function togglePause() {
    if (!game.gameRunning) return;
    
    game.togglePause();
    updateButtonStates();
}

function resetGame() {
    game.reset();
    closeGameOver();
    showStartOverlay();
    updateButtonStates();
}

function restartGame() {
    game.reset();
    closeGameOver();
    showStartOverlay();
}

function showStartOverlay() {
    document.getElementById('game-start').classList.remove('hidden');
}

function goHome() {
    window.location.href = '/';
}

function closeGameOver() {
    document.getElementById('game-over').classList.add('hidden');
}

function updateButtonStates() {
    const startBtn = document.getElementById('start-btn');
    const pauseBtn = document.getElementById('pause-btn');
    const resetBtn = document.getElementById('reset-btn');
    
    if (game.gameRunning && !game.gamePaused) {
        startBtn.disabled = true;
        startBtn.textContent = 'Game Running...';
        pauseBtn.disabled = false;
        pauseBtn.textContent = 'Pause';
        resetBtn.disabled = false;
    } else if (game.gameRunning && game.gamePaused) {
        startBtn.disabled = true;
        startBtn.textContent = 'Game Running...';
        pauseBtn.disabled = false;
        pauseBtn.textContent = 'Continue';
        resetBtn.disabled = false;
    } else {
        startBtn.disabled = false;
        startBtn.textContent = 'Start Game';
        pauseBtn.disabled = true;
        pauseBtn.textContent = 'Pause';
        resetBtn.disabled = false;
    }
}

function setupMobileControls() {
    // Mobile control buttons
    document.getElementById('btn-left').addEventListener('touchstart', (e) => {
        e.preventDefault();
        if (game && game.gameRunning && !game.gamePaused) {
            const moved = game.movePiece(-1, 0);
            if (!moved) {
                // 如果移动失败，可以添加轻微的视觉反馈
                e.target.style.opacity = '0.7';
                setTimeout(() => { e.target.style.opacity = '1'; }, 100);
            }
        }
    });

    document.getElementById('btn-right').addEventListener('touchstart', (e) => {
        e.preventDefault();
        if (game && game.gameRunning && !game.gamePaused) {
            const moved = game.movePiece(1, 0);
            if (!moved) {
                e.target.style.opacity = '0.7';
                setTimeout(() => { e.target.style.opacity = '1'; }, 100);
            }
        }
    });

    document.getElementById('btn-down').addEventListener('touchstart', (e) => {
        e.preventDefault();
        if (game && game.gameRunning && !game.gamePaused) {
            const moved = game.movePiece(0, 1);
            if (!moved) {
                e.target.style.opacity = '0.7';
                setTimeout(() => { e.target.style.opacity = '1'; }, 100);
            }
        }
    });

    document.getElementById('btn-rotate').addEventListener('touchstart', (e) => {
        e.preventDefault();
        if (game && game.gameRunning && !game.gamePaused) {
            const rotated = game.rotatePiece();
            if (!rotated) {
                e.target.style.opacity = '0.7';
                setTimeout(() => { e.target.style.opacity = '1'; }, 100);
            }
        }
    });

    document.getElementById('btn-drop').addEventListener('touchstart', (e) => {
        e.preventDefault();
        if (game && game.gameRunning && !game.gamePaused) {
            const dropDistance = game.dropPiece();
            // 可以根据下落距离给予不同的反馈
            if (dropDistance > 0) {
                e.target.style.opacity = '0.7';
                setTimeout(() => { e.target.style.opacity = '1'; }, 100);
            }
        }
    });

    document.getElementById('btn-pause').addEventListener('touchstart', (e) => {
        e.preventDefault();
        togglePause();
    });

    // Also add click events for desktop testing
    document.getElementById('btn-left').addEventListener('click', (e) => {
        e.preventDefault();
        if (game && game.gameRunning && !game.gamePaused) {
            const moved = game.movePiece(-1, 0);
            if (!moved) {
                e.target.style.opacity = '0.7';
                setTimeout(() => { e.target.style.opacity = '1'; }, 100);
            }
        }
    });

    document.getElementById('btn-right').addEventListener('click', (e) => {
        e.preventDefault();
        if (game && game.gameRunning && !game.gamePaused) {
            const moved = game.movePiece(1, 0);
            if (!moved) {
                e.target.style.opacity = '0.7';
                setTimeout(() => { e.target.style.opacity = '1'; }, 100);
            }
        }
    });

    document.getElementById('btn-down').addEventListener('click', (e) => {
        e.preventDefault();
        if (game && game.gameRunning && !game.gamePaused) {
            const moved = game.movePiece(0, 1);
            if (!moved) {
                e.target.style.opacity = '0.7';
                setTimeout(() => { e.target.style.opacity = '1'; }, 100);
            }
        }
    });

    document.getElementById('btn-rotate').addEventListener('click', (e) => {
        e.preventDefault();
        if (game && game.gameRunning && !game.gamePaused) {
            const rotated = game.rotatePiece();
            if (!rotated) {
                e.target.style.opacity = '0.7';
                setTimeout(() => { e.target.style.opacity = '1'; }, 100);
            }
        }
    });

    document.getElementById('btn-drop').addEventListener('click', (e) => {
        e.preventDefault();
        if (game && game.gameRunning && !game.gamePaused) {
            const dropDistance = game.dropPiece();
            if (dropDistance > 0) {
                e.target.style.opacity = '0.7';
                setTimeout(() => { e.target.style.opacity = '1'; }, 100);
            }
        }
    });

    document.getElementById('btn-pause').addEventListener('click', (e) => {
        e.preventDefault();
        togglePause();
    });
}

// Prevent default behavior for arrow keys and space
document.addEventListener('keydown', function(e) {
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', ' '].includes(e.key)) {
        e.preventDefault();
    }
});
