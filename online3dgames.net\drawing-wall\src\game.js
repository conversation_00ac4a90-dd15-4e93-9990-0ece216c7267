// Drawing Wall UI Controller
let game;

document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('drawing-canvas');
    game = new DrawingWall(canvas);
    
    // Button event listeners
    document.getElementById('canvas-start-btn').addEventListener('click', startGame);
    document.getElementById('home-btn').addEventListener('click', goHome);
    
    // Close game start modal when clicking outside
    document.getElementById('game-start').addEventListener('click', function(e) {
        if (e.target === this) {
            startGame();
        }
    });
});

function startGame() {
    // Hide start overlay
    document.getElementById('game-start').classList.add('hidden');
    // Enable game
    game.start();
}

function goHome() {
    window.location.href = '/';
}

// Handle window resize
window.addEventListener('resize', function() {
    // Canvas maintains its size, no need to redraw
});

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (!game.gameStarted) return;
    
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case 'z':
                e.preventDefault();
                if (e.shiftKey) {
                    game.redo();
                } else {
                    game.undo();
                }
                break;
            case 'y':
                e.preventDefault();
                game.redo();
                break;
            case 's':
                e.preventDefault();
                game.saveImage();
                break;
        }
    }
    
    // Number keys for brush size
    if (e.key >= '1' && e.key <= '9') {
        const size = parseInt(e.key) * 5;
        document.getElementById('brush-size').value = size;
        game.brushSize = size;
        document.getElementById('size-display').textContent = size + 'px';
    }
});
