/* Pop-it Game Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #FF9A9E 0%, #FECFEF 50%, #FECFEF 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.2);
    padding: 20px;
    border-radius: 15px;
    
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.header h1 {
    font-size: 2.5rem;
    color: #FF6B9D;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    margin: 0;
}

.home-btn {
    background: rgba(255, 255, 255, 0.3);
    color: #FF6B9D;
    border: 2px solid rgba(255, 107, 157, 0.3);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.home-btn:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Game Info Panel */
.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
    flex-wrap: wrap;
}

.score-panel {
    display: flex;
    gap: 25px;
    background: rgba(255, 255, 255, 0.2);
    padding: 20px;
    border-radius: 15px;
    
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.score-item {
    text-align: center;
}

.score-item .label {
    display: block;
    font-size: 0.9rem;
    color: #FF6B9D;
    font-weight: bold;
    margin-bottom: 5px;
}

.score-item .value {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
    color: #333;
}

.controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.control-btn.primary {
    background: linear-gradient(135deg, #FF6B9D, #FF8E9B);
    color: white;
}

.control-btn.secondary {
    background: linear-gradient(135deg, #A8E6CF, #7FCDCD);
    color: white;
}

.control-btn.success {
    background: linear-gradient(135deg, #FFD93D, #FF6B9D);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Game Container */
.game-container {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

#game-canvas {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    background: linear-gradient(135deg, #FFE5F1 0%, #FFE5F1 100%);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    touch-action: none; /* Prevent default touch behaviors */
    user-select: none; /* Prevent text selection */
    -webkit-user-select: none;
    -webkit-touch-callout: none; /* Prevent callout on iOS */
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight */
}

/* Game Overlays */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
    
    z-index: 1000;
}

.game-overlay.hidden {
    display: none;
}

.game-start-content {
    background: linear-gradient(135deg, #FF6B9D 0%, #FF8E9B 100%);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: scale(0.8);
    animation: modalAppear 0.3s ease forwards;
    min-width: 300px;
    color: white;
}

@keyframes modalAppear {
    to {
        transform: scale(1);
    }
}

.game-start-content h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.game-start-content p {
    font-size: 1.1rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.game-start-content .tip {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 25px;
}

/* Instructions */
.instructions {
    background: rgba(255, 255, 255, 0.2);
    padding: 25px;
    border-radius: 15px;
    
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.instructions h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
    color: #FF6B9D;
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.instruction-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.instruction-item .icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.instruction-item .desc {
    font-size: 0.9rem;
    line-height: 1.4;
    color: #333;
}

.instruction-item .desc strong {
    color: #FF6B9D;
    display: block;
    margin-bottom: 5px;
}

.tips {
    background: rgba(255, 107, 157, 0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #FF6B9D;
}

.tips h4 {
    margin-bottom: 15px;
    color: #FF6B9D;
    font-size: 1.2rem;
}

.tips ul {
    list-style: none;
    padding: 0;
}

.tips li {
    padding: 5px 0;
    padding-left: 20px;
    position: relative;
    color: #333;
}

.tips li:before {
    content: "💡";
    position: absolute;
    left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .game-info {
        flex-direction: column;
        align-items: stretch;
        margin-bottom: 15px;
    }

    .score-panel {
        justify-content: center;
        gap: 20px;
    }

    .controls {
        justify-content: center;
        gap: 10px;
    }

    .game-container {
        margin-bottom: 15px;
    }

    #game-canvas {
        width: 100%;
        max-width: 100vw;
        height: 70vh;
        min-height: 400px;
        max-height: 600px;
    }

    .instruction-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 5px;
    }

    .header {
        padding: 10px;
    }

    .header h1 {
        font-size: 1.8rem;
    }

    .score-panel {
        gap: 15px;
        flex-wrap: wrap;
    }

    .score-item .value {
        font-size: 1.3rem;
    }

    .score-item .label {
        font-size: 0.8rem;
    }

    .controls {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 8px;
    }

    .control-btn {
        flex: 1;
        min-width: 80px;
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    #game-canvas {
        width: 100%;
        height: 65vh;
        min-height: 350px;
        max-height: 500px;
    }

    .game-start-content {
        padding: 30px 20px;
        min-width: 280px;
    }

    .game-start-content h2 {
        font-size: 1.8rem;
    }
}

/* Bubble Animation */
@keyframes bubblePop {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(0.8);
        opacity: 0.7;
    }
}

.bubble-popping {
    animation: bubblePop 0.3s ease-out;
}
