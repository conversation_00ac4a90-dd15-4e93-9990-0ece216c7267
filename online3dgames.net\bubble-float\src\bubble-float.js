// Bubble Float Game Logic
class BubbleFloat {
    constructor() {
        this.gameStarted = false;
        this.isPaused = false;

        // Settings
        this.bubbleDensity = 20;
        this.floatSpeed = 1;
        this.bubbleSize = 1;
        this.currentMode = 'classic';

        // Bubble management
        this.bubbles = [];
        this.bubbleId = 0;
        this.createInterval = null;

        // Performance optimization
        this.lastFrameTime = 0;
        this.targetFPS = 60;
        this.frameInterval = 1000 / this.targetFPS;
        this.animationId = null;

        // SVG elements
        this.svg = document.getElementById('bubble-svg');
        this.container = document.getElementById('bubble-container');

        this.setupEventListeners();
        this.updateDisplays();
        this.startAnimationLoop();
    }
    
    setupEventListeners() {
        // Settings controls
        document.getElementById('bubble-density').addEventListener('input', (e) => {
            this.bubbleDensity = parseInt(e.target.value);
            document.getElementById('density-display').textContent = e.target.value;
            this.updateBubbleCreation();
        });
        
        document.getElementById('float-speed').addEventListener('input', (e) => {
            this.floatSpeed = parseFloat(e.target.value);
            document.getElementById('speed-display').textContent = e.target.value + 'x';
        });
        
        document.getElementById('bubble-size').addEventListener('input', (e) => {
            this.bubbleSize = parseFloat(e.target.value);
            document.getElementById('size-display').textContent = e.target.value + 'x';
        });
        
        // Mode buttons
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setMode(e.target.id.replace('-mode', ''));
                this.updateModeButtons();
            });
        });
        
        // Action buttons
        document.getElementById('pause-btn').addEventListener('click', () => this.togglePause());
        document.getElementById('reset-btn').addEventListener('click', () => this.reset());
        document.getElementById('fullscreen-btn').addEventListener('click', () => this.toggleFullscreen());
        
        // Mouse interaction
        this.container.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.container.addEventListener('touchmove', (e) => {
            e.preventDefault();
            this.handleTouchMove(e.touches[0]);
        });
        
        // Start button
        document.getElementById('canvas-start-btn').addEventListener('click', () => this.start());
        document.getElementById('home-btn').addEventListener('click', () => this.goHome());
        
        // Close start modal
        document.getElementById('game-start').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.start();
            }
        });
    }
    
    handleMouseMove(e) {
        if (!this.gameStarted || this.isPaused) return;
        
        const rect = this.container.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // Create extra bubbles on mouse move
        if (Math.random() < 0.3) {
            this.createBubbleAt(x, y);
        }
    }
    
    handleTouchMove(touch) {
        if (!this.gameStarted || this.isPaused) return;
        
        const rect = this.container.getBoundingClientRect();
        const x = touch.clientX - rect.left;
        const y = touch.clientY - rect.top;
        
        // Create extra bubbles on touch move
        if (Math.random() < 0.5) {
            this.createBubbleAt(x, y);
        }
    }
    
    start() {
        document.getElementById('game-start').classList.add('hidden');
        this.gameStarted = true;
        this.startBubbleCreation();
        
        // Hide interaction hint after a few seconds
        setTimeout(() => {
            const hint = document.querySelector('.interaction-hint');
            if (hint) {
                hint.style.opacity = '0';
                setTimeout(() => {
                    hint.style.display = 'none';
                }, 1000);
            }
        }, 5000);
    }
    
    startBubbleCreation() {
        this.updateBubbleCreation();
    }
    
    startAnimationLoop() {
        const animate = (currentTime) => {
            if (currentTime - this.lastFrameTime >= this.frameInterval) {
                this.updateBubbles();
                this.lastFrameTime = currentTime;
            }

            if (this.gameStarted && !this.isPaused) {
                this.animationId = requestAnimationFrame(animate);
            }
        };

        if (this.gameStarted && !this.isPaused) {
            this.animationId = requestAnimationFrame(animate);
        }
    }

    updateBubbles() {
        // Create new bubbles based on density
        if (Math.random() < this.bubbleDensity / 1000) {
            this.createRandomBubble();
        }

        // Clean up old bubbles that are off-screen
        this.bubbles = this.bubbles.filter(bubble => {
            const rect = bubble.element.getBoundingClientRect();
            if (rect.bottom < 0) {
                if (bubble.element.parentNode) {
                    bubble.element.parentNode.removeChild(bubble.element);
                }
                return false;
            }
            return true;
        });
    }

    updateBubbleCreation() {
        // This method is now handled by the animation loop
        // Keep for compatibility but make it start the animation loop
        if (!this.gameStarted || this.isPaused) return;

        this.startAnimationLoop();
    }
    
    createRandomBubble() {
        const containerWidth = this.container.clientWidth;
        const x = Math.random() * containerWidth;
        const y = this.container.clientHeight + 50;
        this.createBubbleAt(x, y);
    }
    
    createBubbleAt(x, y) {
        // Limit total bubbles for performance
        if (this.bubbles.length > 100) {
            return;
        }

        const bubble = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        const bubbleId = `bubble-${this.bubbleId++}`;

        // Bubble properties
        const baseSize = 5 + Math.random() * 15;
        const size = baseSize * this.bubbleSize;
        const duration = (6 + Math.random() * 3) / this.floatSpeed; // Shorter duration for better performance
        const drift = (Math.random() - 0.5) * 80;

        bubble.setAttribute('id', bubbleId);
        bubble.setAttribute('cx', x);
        bubble.setAttribute('cy', y);
        bubble.setAttribute('r', size);
        bubble.setAttribute('fill', 'url(#bubbleGradient)');
        bubble.setAttribute('opacity', 0.6 + Math.random() * 0.4);

        // Apply mode-specific styling
        this.applyModeStyle(bubble);

        // Add animation with better performance
        bubble.style.setProperty('--drift', drift + 'px');
        bubble.style.animationDuration = duration + 's';
        bubble.style.willChange = 'transform, opacity'; // Optimize for animations

        // Set CSS classes for SVG elements
        let cssClass = 'bubble';
        if (this.currentMode !== 'classic') {
            cssClass += ' ' + this.currentMode;
        }
        bubble.setAttribute('class', cssClass);

        // Add to SVG
        this.svg.appendChild(bubble);
        this.bubbles.push({
            element: bubble,
            id: bubbleId,
            createdTime: Date.now(),
            duration: duration * 1000
        });
    }
    
    applyModeStyle(bubble) {
        // Reset to default first
        bubble.removeAttribute('filter');
        bubble.setAttribute('fill', 'url(#bubbleGradient)');
        bubble.style.removeProperty('--hue');

        switch (this.currentMode) {
            case 'colorful':
                const hue = Math.random() * 360;
                bubble.style.setProperty('--hue', hue + 'deg');
                // Create a colorful gradient
                const colorfulFill = `hsl(${hue}, 70%, 80%)`;
                bubble.setAttribute('fill', colorfulFill);
                bubble.setAttribute('opacity', 0.7);
                break;

            case 'neon':
                const neonColors = ['#ff0080', '#00ff80', '#8000ff', '#ff8000', '#0080ff'];
                const color = neonColors[Math.floor(Math.random() * neonColors.length)];
                bubble.setAttribute('fill', color);
                bubble.setAttribute('filter', 'url(#glow)');
                bubble.setAttribute('opacity', 0.8);
                break;

            case 'gentle':
                bubble.setAttribute('opacity', 0.3);
                bubble.setAttribute('filter', 'url(#blur)');
                break;

            case 'classic':
            default:
                bubble.setAttribute('fill', 'url(#bubbleGradient)');
                bubble.setAttribute('opacity', 0.6 + Math.random() * 0.4);
                break;
        }
    }
    
    removeBubble(bubbleId) {
        const bubbleIndex = this.bubbles.findIndex(b => b.id === bubbleId);
        if (bubbleIndex !== -1) {
            const bubble = this.bubbles[bubbleIndex];
            if (bubble.element.parentNode) {
                bubble.element.parentNode.removeChild(bubble.element);
            }
            this.bubbles.splice(bubbleIndex, 1);
        }
    }
    
    setMode(mode) {
        this.currentMode = mode;
        // Update existing bubbles
        this.bubbles.forEach(bubble => {
            // For SVG elements, use setAttribute instead of className
            bubble.element.setAttribute('class', 'bubble');
            if (mode !== 'classic') {
                const currentClass = bubble.element.getAttribute('class') || '';
                bubble.element.setAttribute('class', currentClass + ' ' + mode);
            }
            this.applyModeStyle(bubble.element);
        });
    }
    
    updateModeButtons() {
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(`${this.currentMode}-mode`).classList.add('active');
    }
    
    togglePause() {
        this.isPaused = !this.isPaused;
        const btn = document.getElementById('pause-btn');
        btn.textContent = this.isPaused ? '▶️ Continue' : '⏸️ Pause';

        if (this.isPaused) {
            // Cancel animation frame
            if (this.animationId) {
                cancelAnimationFrame(this.animationId);
                this.animationId = null;
            }
            // Pause all animations
            this.bubbles.forEach(bubble => {
                bubble.element.style.animationPlayState = 'paused';
            });
        } else {
            // Resume animation loop
            this.startAnimationLoop();
            // Resume all animations
            this.bubbles.forEach(bubble => {
                bubble.element.style.animationPlayState = 'running';
            });
        }
    }
    
    reset() {
        // Cancel animation frame
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }

        // Clear all bubbles efficiently
        this.bubbles.forEach(bubble => {
            if (bubble.element.parentNode) {
                bubble.element.parentNode.removeChild(bubble.element);
            }
        });
        this.bubbles = [];

        // Reset bubble ID counter
        this.bubbleId = 0;

        if (this.gameStarted && !this.isPaused) {
            this.startAnimationLoop();
        }
    }
    
    toggleFullscreen() {
        if (!document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement) {
            // Enter fullscreen
            const requestFullscreen = this.container.requestFullscreen ||
                                    this.container.webkitRequestFullscreen ||
                                    this.container.mozRequestFullScreen;

            if (requestFullscreen) {
                requestFullscreen.call(this.container).catch(err => {
                    console.log('Fullscreen not supported:', err);
                });
            }
        } else {
            // Exit fullscreen
            const exitFullscreen = document.exitFullscreen ||
                                  document.webkitExitFullscreen ||
                                  document.mozCancelFullScreen;

            if (exitFullscreen) {
                exitFullscreen.call(document);
            }
        }
    }
    
    updateDisplays() {
        document.getElementById('density-display').textContent = this.bubbleDensity;
        document.getElementById('speed-display').textContent = this.floatSpeed + 'x';
        document.getElementById('size-display').textContent = this.bubbleSize + 'x';
    }
    
    goHome() {
        window.location.href = '/';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new BubbleFloat();
});
