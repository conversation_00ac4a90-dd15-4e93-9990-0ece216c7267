class SpiderSolitaire {
    constructor(testMode = false) {
        this.testMode = testMode;
        this.deck = [];
        this.stock = [];
        this.tableau = [[], [], [], [], [], [], [], [], [], []];
        this.completed = [];
        this.score = 0;
        this.moves = 0;
        this.startTime = null;
        this.timer = null;
        this.gameWon = false;
        this.moveHistory = [];
        this.difficulty = 'easy';

        this.comboCount = 0;
        this.lastMoveTime = 0;
        this.comboTimeWindow = 10000;
        this.maxComboMultiplier = 5;

        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElement = null;
        this.draggedElements = null;
        this.isDragging = false;
        this.justFinishedDrag = false;
        this.dragOffset = { x: 0, y: 0 };
        this.dragStartPos = { x: 0, y: 0 };
        this.dragThreshold = this.isMobile() ? 12 : 8;
        this.longPressTimer = null;
        this.longPressDelay = 300;
        this.hasAutoFullscreened = false;

        this.suits = ['spades', 'hearts', 'clubs', 'diamonds'];
        this.ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        this.suitSymbols = { hearts: '♥', diamonds: '♦', clubs: '♣', spades: '♠' };
        this.suitColors = { hearts: 'red', diamonds: 'red', clubs: 'black', spades: 'black' };

        this.isAnimatingCard = false;
        this.isAutoCompleting = false;
        this.isProcessingAction = false;
        this.lastActionTime = 0;
        this.actionCooldown = 100;
        this.buttonCooldowns = new Map();

        if (!testMode) {
            this.bindEvents();
        } else {
            this.initializeGame();
        }
    }

    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               ('ontouchstart' in window) ||
               (navigator.maxTouchPoints > 0);
    }


    async animateCardMovement(cards, startPositions, endPositions, options = {}) {
        const {
            speedMultiplier = 1,
            animationType = 'flying-to-tableau',
            staggerDelay = 60,
            baseSpeed = 800
        } = options;

        if (!Array.isArray(cards)) {
            cards = [cards];
            startPositions = [startPositions];
            endPositions = [endPositions];
        }

        const animations = cards.map((card, index) => {
            const $card = card instanceof jQuery ? card : $(card);
            if (!$card.length) return Promise.resolve();

            const startPos = startPositions[index] || startPositions[0];
            const endPos = endPositions[index] || endPositions[0];

            const distance = Math.sqrt(
                Math.pow(endPos.x - startPos.x, 2) +
                Math.pow(endPos.y - startPos.y, 2)
            );
            const duration = Math.max(200, (distance / baseSpeed) * 1000 / speedMultiplier);

            return new Promise((resolve) => {
                setTimeout(() => {
                    this.animateSingleCard($card, startPos, endPos, animationType, duration)
                        .then(resolve);
                }, index * staggerDelay);
            });
        });

        return Promise.all(animations);
    }

    async animateSingleCard($card, startPos, endPos, animationType, duration) {
        return new Promise((resolve) => {
            if (!$card.length) {
                resolve();
                return;
            }

            const originalStyles = {
                position: $card.css('position'),
                left: $card.css('left'),
                top: $card.css('top'),
                zIndex: $card.css('z-index'),
                transition: $card.css('transition'),
                transform: $card.css('transform')
            };

            const cardWidth = $card.outerWidth();
            const cardHeight = $card.outerHeight();

            $card.css({
                position: 'fixed',
                left: (startPos.x - cardWidth / 2) + 'px',
                top: (startPos.y - cardHeight / 2) + 'px',
                zIndex: 10000,
                transition: 'none',
                transform: 'translateZ(0)'
            });

            $card.addClass(animationType);
            $card[0].offsetHeight;
            $card.css({
                left: (endPos.x - cardWidth / 2) + 'px',
                top: (endPos.y - cardHeight / 2) + 'px',
                transition: `all ${duration}ms linear`
            });

            setTimeout(() => {
                $card.removeClass(animationType);
                Object.keys(originalStyles).forEach(prop => {
                    $card.css(prop, originalStyles[prop]);
                });
                resolve();
            }, duration);
        });
    }

    // 专门的收牌动画方法
    async animateCardToCompleted($card, targetPos, duration) {
        return new Promise((resolve) => {
            if (!$card.length) {
                resolve();
                return;
            }

            // 获取卡牌当前位置和目标位置的差值
            const currentRect = $card[0].getBoundingClientRect();
            const currentPos = {
                x: currentRect.left + currentRect.width / 2,
                y: currentRect.top + currentRect.height / 2
            };

            const deltaX = targetPos.x - currentPos.x;
            const deltaY = targetPos.y - currentPos.y;

            // 保存原始样式
            const originalTransform = $card.css('transform');
            const originalTransition = $card.css('transition');
            const originalZIndex = $card.css('z-index');

            // 设置高z-index
            $card.css({
                'z-index': 10000,
                'transition': 'none'
            });

            // 强制重绘
            $card[0].offsetHeight;

            // 开始动画（只移动，不缩放不透明）
            $card.css({
                'transition': `transform ${duration}ms ease-out`,
                'transform': `${originalTransform} translate(${deltaX}px, ${deltaY}px)`
            });

            setTimeout(() => {
                // 恢复原始样式
                $card.css({
                    'transform': originalTransform,
                    'transition': originalTransition,
                    'z-index': originalZIndex
                });
                resolve();
            }, duration);
        });
    }

    async animateDealFromStock() {
        const $cardBack = $('#stock .card-back');
        if (!$cardBack.length) return;

        const cardsToAdd = [];
        const animations = [];


        for (let i = 0; i < 10 && this.stock.length > 0; i++) {
            const card = this.stock.pop();
            card.faceUp = true;
            cardsToAdd.push({ card, targetPile: i });
        }


        for (let i = 0; i < cardsToAdd.length; i++) {
            const { card, targetPile } = cardsToAdd[i];

            animations.push(new Promise(resolve => {
                setTimeout(async () => {

                    const cardBackRect = $cardBack[0].getBoundingClientRect();


                    const $tempCard = this.createCardElement(card);
                    $tempCard.css({
                        position: 'fixed',
                        left: cardBackRect.left + 'px',
                        top: cardBackRect.top + 'px',
                        width: cardBackRect.width + 'px',
                        height: cardBackRect.height + 'px',
                        zIndex: 10001 + i,
                        transform: 'scale(1.05)',
                        transition: 'none'
                    });
                    $('body').append($tempCard);


                    const startPos = {
                        x: cardBackRect.left + cardBackRect.width / 2,
                        y: cardBackRect.top + cardBackRect.height / 2
                    };
                    const endPos = this.getTableauPileTargetPosition(targetPile);


                    await this.animateSingleCard($tempCard, startPos, endPos, 'flying-from-stock', 450);


                    this.tableau[targetPile].push(card);
                    $tempCard.remove();


                    this.updateSingleTableauPile(targetPile);

                    resolve();
                }, i * 60);
            }));
        }


        await Promise.all(animations);


        this.updateStock();
    }

    async animateCompletedSequence(pileIndex) {
        const $pile = $(`#tableau-${pileIndex}`);
        const $cards = $pile.find('.card').slice(-13);

        const $completedContainer = $('#completedSequences');
        const containerRect = $completedContainer[0].getBoundingClientRect();
        const completedCount = this.completed.length + 1;
        const leftOffset = (completedCount - 1) * 15;

        const targetPos = {
            x: containerRect.left + leftOffset + 50,
            y: containerRect.top + 72.5
        };

        const animations = [];
        let firstCardCompleted = false;

        for (let i = $cards.length - 1; i >= 0; i--) {
            const $card = $($cards[i]);
            const cardIndex = $cards.length - 1 - i;

            animations.push(new Promise((resolve) => {
                setTimeout(async () => {
                    await this.animateCardToCompleted($card, targetPos, 600);
                    $card.remove();

                    if (!firstCardCompleted) {
                        firstCardCompleted = true;
                        this.addCompletedSequenceDisplay();
                    }

                    resolve();
                }, cardIndex * 100);
            }));
        }

        await Promise.all(animations);
        this.updateSingleTableauPile(pileIndex);
    }

    addCompletedSequenceDisplay() {
        const $completedContainer = $('#completedSequences');

        if (this.completed.length === 1) {
            $completedContainer.find('.completed-placeholder').hide();
        }

        const completedCount = this.completed.length;
        const leftOffset = (completedCount - 1) * 15;

        const $cardBack = $('<div>').addClass('card face-down completed-sequence-display');
        $cardBack.css({
            width: '100px',
            height: '145px',
            position: 'absolute',
            left: leftOffset + 'px',
            top: '0px',
            zIndex: completedCount
        });

        $completedContainer.append($cardBack);
    }



    getCardPosition($card) {
        const offset = $card.offset();
        const width = $card.outerWidth();
        const height = $card.outerHeight();
        
        return {
            x: offset.left + width / 2,
            y: offset.top + height / 2
        };
    }

    getTableauPilePosition(pileIndex) {
        const $pile = $(`#tableau-${pileIndex}`);
        const offset = $pile.offset();
        const height = $pile.outerHeight();
        const width = $pile.outerWidth();

        // Get the last card in the pile to position the flying card correctly
        const $lastCard = $pile.find('.card').last();
        let targetY = offset.top + height - 20; // Default position

        if ($lastCard.length) {
            const lastCardOffset = $lastCard.offset();
            const lastCardHeight = $lastCard.outerHeight();
            targetY = lastCardOffset.top + lastCardHeight + 8; // Position below the last card with small gap
        }

        return {
            x: offset.left + width / 2,
            y: targetY
        };
    }

    getTableauPileTargetPosition(pileIndex) {
        const $pile = $(`#tableau-${pileIndex}`);
        const $lastCard = $pile.find('.card').last();
        const offset = window.innerWidth < 1024 ? 15 : 25;

        if ($lastCard.length) {

            const lastCardRect = $lastCard[0].getBoundingClientRect();
            return {
                x: lastCardRect.left + lastCardRect.width / 2,
                y: lastCardRect.top + offset + lastCardRect.height / 2
            };
        } else {

            const pileRect = $pile[0].getBoundingClientRect();
            return {
                x: pileRect.left + pileRect.width / 2,
                y: pileRect.top + pileRect.height / 2
            };
        }
    }

    getCompletedAreaPosition() {
        const $placeholder = $('.completed-placeholder');
        const $completedArea = $('.completed-sequences');
        const $target = $placeholder.length ? $placeholder : $completedArea;
        const offset = $target.offset();

        if (!offset) {
            return {
                x: window.innerWidth - 100,
                y: 100
            };
        }

        return {
            x: offset.left + $target.outerWidth() / 2,
            y: offset.top + $target.outerHeight() / 2
        };
    }


    setDifficulty(difficulty) {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        this.difficulty = difficulty;

        // Update difficulty button states
        $('.btn-difficulty').removeClass('active');
        $(`#${difficulty}Btn`).addClass('active');

        this.newGame();
    }

    initializeGame() {
        this.deck = this.createDeck();
        this.shuffleArray(this.deck);
        this.dealCards();
        this.updateDisplay();
        this.startTimer();
    }

    createDeck() {
        const suits = this.getSuitsForDifficulty();
        const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        let idCounter = 1;
        const deck = [];

        const copiesPerSuit = 104 / (suits.length * ranks.length);

        for (let suit of suits) {
            for (let i = 0; i < copiesPerSuit; i++) {
                for (let rank of ranks) {
                    deck.push({
                        id: idCounter++,
                        suit,
                        rank,
                        value: this.getCardValue(rank),
                        color: this.suitColors[suit],
                        faceUp: false
                    });
                }
            }
        }
        return deck;
    }

    getSuitsForDifficulty() {
        switch (this.difficulty) {
            case 'easy':
                return ['spades'];
            case 'medium':
                return ['spades', 'hearts'];
            case 'hard':
                return ['spades', 'hearts', 'clubs', 'diamonds'];
            default:
                return ['spades'];
        }
    }

    getCardValue(rank) {
        if (rank === 'A') return 1;
        if (rank === 'J') return 11;
        if (rank === 'Q') return 12;
        if (rank === 'K') return 13;
        return parseInt(rank);
    }

    dealCards() {
        this.stock = [];
        this.tableau = [[], [], [], [], [], [], [], [], [], []];
        this.completed = [];

        let cardIndex = 0;

        for (let pile = 0; pile < 4; pile++) {
            for (let card = 0; card < 6; card++) {
                if (cardIndex < this.deck.length) {
                    const currentCard = this.deck[cardIndex];
                    currentCard.faceUp = (card === 5);
                    this.tableau[pile].push(currentCard);
                    cardIndex++;
                }
            }
        }

        for (let pile = 4; pile < 10; pile++) {
            for (let card = 0; card < 5; card++) {
                if (cardIndex < this.deck.length) {
                    const currentCard = this.deck[cardIndex];
                    currentCard.faceUp = (card === 4);
                    this.tableau[pile].push(currentCard);
                    cardIndex++;
                }
            }
        }

        while (cardIndex < this.deck.length) {
            this.stock.push(this.deck[cardIndex]);
            cardIndex++;
        }
    }

    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }


    calculateCardStates(pileIndex) {
        const pile = this.tableau[pileIndex];
        const states = {
            movableCards: [],
            disabledCards: [],
            topCardIndex: pile.length - 1
        };

        if (pile.length === 0) return states;


        states.movableCards = this.getMovableCards(pileIndex, states.topCardIndex);


        const faceUpCards = pile.filter(card => card.faceUp);
        states.disabledCards = faceUpCards.filter(card =>
            !states.movableCards.some(movableCard => movableCard.id === card.id)
        );

        return states;
    }

    canMoveCardsTogether(cards) {
        if (cards.length <= 1) return true;

        for (let i = 0; i < cards.length - 1; i++) {
            const currentCard = cards[i];
            const nextCard = cards[i + 1];

            if (currentCard.suit !== nextCard.suit ||
                currentCard.value !== nextCard.value + 1) {
                return false;
            }
        }
        return true;
    }

    getMovableCards(pileIndex, startIndex) {
        const pile = this.tableau[pileIndex];
        if (startIndex >= pile.length || startIndex < 0) return [];

        const movableCards = [];
        const startCard = pile[startIndex];

        if (!startCard || !startCard.faceUp) return [];

        movableCards.push(startCard);

        for (let i = startIndex - 1; i >= 0; i--) {
            const currentCard = pile[i];

            if (!currentCard.faceUp) break;

            const lastCard = movableCards[movableCards.length - 1];

            if (lastCard.rank === currentCard.rank && lastCard.suit === currentCard.suit) {
                break;
            }

            if (lastCard.suit === currentCard.suit &&
                currentCard.value === lastCard.value + 1) {
                movableCards.push(currentCard);
            } else {
                break;
            }
        }

        return movableCards.reverse();
    }

    canPlaceCard(cardToPlace, targetCard) {
        if (!targetCard) return true;

        return cardToPlace.value === targetCard.value - 1;
    }

    async checkForCompletedSequences() {
        let foundCompleted = false;
        let hasMoreSequences = true;

        while (hasMoreSequences) {
            hasMoreSequences = false;

            for (let pileIndex = 0; pileIndex < this.tableau.length; pileIndex++) {
                const pile = this.tableau[pileIndex];
                if (pile.length < 13) continue;

                const topCards = pile.slice(-13);
                if (this.isCompleteSequence(topCards)) {
                    await this.animateCompletedSequence(pileIndex);

                    this.tableau[pileIndex].splice(-13, 13);

                    this.completed.push(topCards);

                    this.score += 100;
                    foundCompleted = true;
                    hasMoreSequences = true;

                    if (this.tableau[pileIndex].length > 0) {
                        const newTopCard = this.tableau[pileIndex][this.tableau[pileIndex].length - 1];
                        if (newTopCard && !newTopCard.faceUp) {
                            newTopCard.faceUp = true;
                            this.score += 5;
                        }
                    }

                    this.updateDisplay();

                    break;
                }
            }
        }

        return foundCompleted;
    }

    isCompleteSequence(cards) {
        if (cards.length !== 13) return false;
        
        const suit = cards[0].suit;
        
        for (let i = 0; i < 13; i++) {
            const expectedValue = 13 - i;
            if (cards[i].suit !== suit || cards[i].value !== expectedValue || !cards[i].faceUp) {
                return false;
            }
        }
        
        return true;
    }

    // Deal new cards from stock (one to each non-empty pile)
    async dealFromStock() {
        if (this.stock.length === 0) {
            return false;
        }
        if (this.isAnimatingCard) {
            return false;
        }

        for (let i = 0; i < this.tableau.length; i++) {
            if (this.tableau[i].length === 0) {
                if (typeof gameAlert === 'function') {
                    gameAlert('All piles must have at least one card before dealing new cards!', 'Cannot Deal');
                } else {
                    alert('All piles must have at least one card before dealing new cards!');
                }
                return false;
            }
        }

        this.isAnimatingCard = true;
        try {
            await this.animateDealFromStock();
        } finally {
            this.isAnimatingCard = false;
        }

        this.moves++;
        this.recordMove({
            type: 'deal',
            count: Math.min(10, this.stock.length)
        });

        this.saveGameState();

        return true;
    }

    checkWinCondition() {
        const expectedSequences = this.difficulty === 'easy' ? 8 :
                                 this.difficulty === 'medium' ? 8 : 8;
        return this.completed.length >= expectedSequences;
    }

    recordMove(move) {
        this.moveHistory.push(move);
    }

    calculateComboBonus() {
        const now = Date.now();

        if (now - this.lastMoveTime <= this.comboTimeWindow) {
            this.comboCount++;
        } else {
            this.comboCount = 1;
        }

        this.lastMoveTime = now;

        let multiplier = 1;
        if (this.comboCount >= 2) multiplier = 1.5;
        if (this.comboCount >= 3) multiplier = 2;
        if (this.comboCount >= 5) multiplier = 3;
        if (this.comboCount >= 8) multiplier = this.maxComboMultiplier;

        return {
            count: this.comboCount,
            multiplier: multiplier,
            bonus: Math.floor((this.comboCount - 1) * 2 * multiplier)
        };
    }

    showComboEffect(comboData) {
        if (comboData.count <= 1) return;

        const comboText = `${comboData.count}x COMBO! +${comboData.bonus}`;
        const $comboEffect = $('<div>').addClass('combo-effect').text(comboText);

        $comboEffect.css({
            position: 'fixed',
            top: '20%',
            left: '50%',
            transform: 'translateX(-50%)',
            background: 'linear-gradient(45deg, #ff6b35, #f7931e)',
            color: 'white',
            padding: '10px 20px',
            borderRadius: '25px',
            fontSize: '18px',
            fontWeight: 'bold',
            zIndex: 1500,
            boxShadow: '0 4px 15px rgba(255,107,53,0.4)',
            animation: 'comboPopup 2s ease-out forwards'
        });

        $('body').append($comboEffect);

        setTimeout(() => {
            $comboEffect.remove();
        }, 2000);
    }

    canPerformAction(actionType = 'default') {
        const now = Date.now();
        if (this.isAnimatingCard || this.isProcessingAction) {
            return false;
        }
        if (now - this.lastActionTime < this.actionCooldown) {
            return false;
        }

        const lastButtonTime = this.buttonCooldowns.get(actionType) || 0;
        if (now - lastButtonTime < 300) {
            return false;
        }

        return true;
    }

    setActionCooldown(actionType = 'default') {
        this.lastActionTime = Date.now();
        this.buttonCooldowns.set(actionType, Date.now());
    }

    bindEvents() {
        $('#homeBtn').on('click', () => {
            window.location.href = '/';
        });

        $('#newGameBtn').on('click', () => {
            if (!this.canPerformAction('newGame')) return;
            this.setActionCooldown('newGame');

            if (!this.hasAutoFullscreened) {
                this.hasAutoFullscreened = true;
                this.autoFullscreen();
            }
            this.newGame();
        });

        $('#undoBtn').on('click', () => {
            if (!this.canPerformAction('undo')) return;
            this.setActionCooldown('undo');
            this.undoMove();
        });

        $('#hintBtn').on('click', () => {
            if (!this.canPerformAction('hint')) return;
            this.setActionCooldown('hint');
            this.showHint();
        });

        $('#helpBtn').on('click', () => {
            if (!this.canPerformAction('help')) return;
            this.setActionCooldown('help');
            this.showHelp();
        });

        // Mobile stock button
        $('#stockBtn').on('click', async () => {
            if (!this.canPerformAction('dealStock')) return;
            this.setActionCooldown('dealStock');
            this.isProcessingAction = true;

            try {
                await this.dealFromStock();
                await this.checkForCompletedSequences();
                this.checkWinCondition();
            } finally {
                this.isProcessingAction = false;
            }
        });

        $('#closeHelpBtn').on('click', () => this.hideHelp());
        $('#closeHelpBtnBottom').on('click', () => this.hideHelp());

        $('#fullscreenBtn').on('click', () => {
            if (!this.canPerformAction('fullscreen')) return;
            this.setActionCooldown('fullscreen');
            this.toggleFullscreen();
        });

        // Add difficulty selection buttons
        $('#easyBtn').on('click', () => {
            if (!this.canPerformAction('difficulty')) return;
            this.setActionCooldown('difficulty');
            this.setDifficulty('easy');
        });

        $('#mediumBtn').on('click', () => {
            if (!this.canPerformAction('difficulty')) return;
            this.setActionCooldown('difficulty');
            this.setDifficulty('medium');
        });

        $('#hardBtn').on('click', () => {
            if (!this.canPerformAction('difficulty')) return;
            this.setActionCooldown('difficulty');
            this.setDifficulty('hard');
        });

        // Add message button handlers
        $('#playAgainBtn').on('click', () => {
            if (!this.canPerformAction('playAgain')) return;
            this.setActionCooldown('playAgain');
            this.hideMessage();
            this.newGame();
        });

        $('#closeMessageBtn').on('click', () => this.hideMessage());

        // Mouse and touch events
        $(document).on('mousedown', async (e) => await this.onPointerDown(e.originalEvent || e));
        $(document).on('mousemove', (e) => this.onPointerMove(e.originalEvent || e));
        $(document).on('mouseup', async (e) => await this.onPointerUp(e.originalEvent || e));

        document.addEventListener('touchstart', async (e) => await this.onPointerDown(e), { passive: false });
        document.addEventListener('touchmove', (e) => this.onPointerMove(e), { passive: false });
        document.addEventListener('touchend', async (e) => await this.onPointerUp(e), { passive: false });
        document.addEventListener('touchcancel', async (e) => await this.onPointerUp(e), { passive: false });

        $(document).on('dragstart', (e) => e.preventDefault());
        $(document).on('selectstart', (e) => e.preventDefault());
        $(document).on('keydown', (e) => this.onKeyDown(e));
        $(document).on('contextmenu', (e) => e.preventDefault());

        // Fullscreen events
        $(document).on('fullscreenchange webkitfullscreenchange mozfullscreenchange MSFullscreenChange', () => {
            this.updateFullscreenButton();
        });

        this.updateFullscreenButton();
    }

    newGame() {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        this.isProcessingAction = true;
        this.stopTimer();
        this.score = 0;
        this.moves = 0;
        this.gameWon = false;
        this.moveHistory = [];
        this.completed = [];
        this.resetDragState();

        this.comboCount = 0;
        this.lastMoveTime = 0;

        this.clearSavedGame();

        $('.card').removeClass('dragging moving-to-foundation moving-to-tableau card-returning');
        $('.card').css({
            left: '',
            top: '',
            position: '',
            'z-index': '',
            'transition': ''
        });

        $('.completed-sequence-display').remove();
        $('#completedSequences .completed-placeholder').show();

        this.hideMessage();
        this.hideHelp();
        this.initializeGame();

        this.updateMobileStockButton();
        this.isProcessingAction = false;
    }

    updateMobileStockButton() {
        const mobileStockCount = $('#stockCount');
        const mobileStockBtn = $('#stockBtn');

        if (this.stock && this.stock.length > 0) {
            mobileStockCount.text(this.stock.length);
            mobileStockBtn.prop('disabled', false);
            mobileStockBtn.attr('title', `点击发牌 (剩余${this.stock.length}张)`);
        } else {
            mobileStockCount.text('0');
            mobileStockBtn.prop('disabled', true);
            mobileStockBtn.attr('title', '无剩余卡牌');
        }
    }

    resetDragState() {
        this.isDragging = false;
        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElement = null;
        this.draggedElements = null;
        this.clickedCard = null;
        this.justFinishedDrag = false;
        this.dragOffset = { x: 0, y: 0 };
        this.dragStartPos = { x: 0, y: 0 };
        this.isAnimatingCard = false;

        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    }

    startTimer() {
        this.startTime = Date.now();
        this.timer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');
            $('#timer').text(`${minutes}:${seconds}`);
        }, 1000);
    }

    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    formatTime() {
        if (!this.startTime) return '00:00';
        
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    updateDisplay() {
        if (!this.testMode) {
            this.updateStock();
            this.updateTableau();
            this.updateStats();
            this.updateCompleted();
        }

        if (this.checkWinCondition()) {
            this.gameWon = true;
            this.clearSavedGame();
            this.showWinMessage();
        }
    }

    updateStock() {
        const stockElement = $('#stock');
        const mobileStockCount = $('#stockCount');
        const mobileStockBtn = $('#stockBtn');

        stockElement.empty();

        if (this.stock.length > 0) {
            const cardBack = $('<div class="card-back"></div>');
            stockElement.append(cardBack);

            const countIndicator = $('<div class="stock-count"></div>');
            countIndicator.text(this.stock.length);
            countIndicator.css({
                position: 'absolute',
                top: '5px',
                right: '5px',
                background: 'rgba(0,0,0,0.7)',
                color: 'white',
                borderRadius: '10px',
                padding: '2px 6px',
                fontSize: '12px',
                fontWeight: 'bold',
                zIndex: 10
            });
            stockElement.append(countIndicator);

            stockElement.removeClass('empty');

            mobileStockCount.text(this.stock.length);
            mobileStockBtn.prop('disabled', false);
            mobileStockBtn.attr('title', `Click to Deal 10 Cards (${this.stock.length} remaining)`);
        } else {
            stockElement.html('<div class="stock-empty">Empty</div>');
            stockElement.addClass('empty');

            mobileStockCount.text('0');
            mobileStockBtn.prop('disabled', true);
            mobileStockBtn.attr('title', 'No cards remaining');
        }
    }

    updateTableau() {
        for (let i = 0; i < 10; i++) {
            this.updateSingleTableauPile(i);
        }
    }

    updateSingleTableauPile(pileIndex) {
        const tableauElement = $(`#tableau-${pileIndex}`);
        tableauElement.empty();

        const spacing = window.innerWidth < 1024 ? 15 : 25;
        const cardStates = this.calculateCardStates(pileIndex);

        this.tableau[pileIndex].forEach((card, index) => {
            const cardElement = this.createCardElement(card);
            cardElement.css({
                position: 'absolute',
                top: `${index * spacing}px`,
                zIndex: index + 1
            });

            if (card.faceUp) {
                this.applyCardState(cardElement, card, cardStates);
            }

            if (card.faceUp && index === this.tableau[pileIndex].length - 1) {
                cardElement.addClass('top-card');
            }

            tableauElement.append(cardElement);
        });
    }

    applyCardState(cardElement, card, cardStates) {
        cardElement.removeClass('draggable-card disabled-card');
        cardElement.css({
            'cursor': '',
            'box-shadow': ''
        });

        const isMovable = cardStates.movableCards.some(movableCard => movableCard.id === card.id);

        if (isMovable) {
            cardElement.addClass('draggable-card');
        } else {
            cardElement.addClass('disabled-card');
        }
    }

    updateCompleted() {
        const completedElement = $('#completed');
        if (completedElement.length) {
            completedElement.text(`Completed: ${this.completed.length}/8`);
        }
    }

    createCardElement(card) {
        const cardElement = $('<div>').addClass('card');

        if (!card.faceUp) {
            cardElement.addClass('face-down');
            return cardElement;
        }

        cardElement.addClass(card.color);
        cardElement.attr('data-suit', card.suit);
        cardElement.attr('data-rank', card.rank);
        cardElement.attr('data-value', card.value);

        const symbol = this.suitSymbols[card.suit];

        cardElement.html(`
            <div class="card-top">
                <span class="rank">${card.rank}</span>
                <span class="suit">${symbol}</span>
            </div>
            <div class="card-center">${symbol}</div>
            <div class="card-bottom">
                <span class="rank">${card.rank}</span>
                <span class="suit">${symbol}</span>
            </div>
        `);

        return cardElement;
    }

    updateStats() {
        const $score = $('#score');
        const $moves = $('#moves');
        const $completed = $('#completed');
        
        if ($score.text() !== this.score.toString()) {
            $score.addClass('score-updating');
            $score.text(this.score);
            setTimeout(() => $score.removeClass('score-updating'), 500);
        }

        if ($moves.text() !== this.moves.toString()) {
            $moves.addClass('moves-updating');
            $moves.text(this.moves);
            setTimeout(() => $moves.removeClass('moves-updating'), 300);
        }

        if ($completed.text() !== `${this.completed.length}/8`) {
            $completed.addClass('completed-updating');
            $completed.text(`${this.completed.length}/8`);
            setTimeout(() => $completed.removeClass('completed-updating'), 600);
        }
        
        $('#timer').text(this.formatTime());
    }

    async onPointerDown(e) {
        if (this.isAnimatingCard || this.isProcessingAction) return;
        if (e.type === 'mousedown' && (e.which !== 1 && e.button !== 0)) return;

        this.resetDragState();
        const coords = this.getEventCoordinates(e);
        this.dragStartPos = { x: coords.clientX, y: coords.clientY };

        const target = e.target || e.srcElement;

        if (target.closest('#stock') || $(target).closest('#stock').length) {
            e.preventDefault();
            e.stopPropagation();

            if (!this.canPerformAction('dealStock')) return;
            this.setActionCooldown('dealStock');
            this.isProcessingAction = true;

            try {
                await this.dealFromStock();
                await this.checkForCompletedSequences();
                this.checkWinCondition();
            } finally {
                this.isProcessingAction = false;
            }
            return;
        }

        if (!this.canPerformAction('pointer')) return;

        const cardElement = target.closest('.card') || $(target).closest('.card')[0];
        if (!cardElement) return;

        e.preventDefault();
        e.stopPropagation();

        const $cardElement = $(cardElement);

        if ($cardElement.hasClass('face-down')) {
            return;
        }

        if ($cardElement.hasClass('disabled-card')) {
            return;
        }

        this.setActionCooldown('pointer');

        const cardRect = cardElement.getBoundingClientRect();
        this.dragOffset.x = coords.clientX - cardRect.left;
        this.dragOffset.y = coords.clientY - cardRect.top;

        this.prepareDragData($cardElement);

        if (!this.draggedCards || this.draggedCards.length === 0) {
            this.resetDragState();
            return;
        }

        const isMobile = e.type.startsWith('touch');
        if (isMobile) {
            this.longPressTimer = setTimeout(() => {
                if (!this.isDragging && this.draggedCards && this.draggedCards.length > 0) {
                    navigator.vibrate && navigator.vibrate(50);
                }
            }, this.longPressDelay);

            e.preventDefault();
        }
    }

    onPointerMove(e) {
        if (this.isAnimatingCard || this.isProcessingAction) return;
        if (!this.draggedCards || this.draggedCards.length === 0) return;

        const coords = this.getEventCoordinates(e);
        const deltaX = coords.clientX - this.dragStartPos.x;
        const deltaY = coords.clientY - this.dragStartPos.y;

        if (!this.isDragging) {
            if (Math.abs(deltaX) > this.dragThreshold || Math.abs(deltaY) > this.dragThreshold) {
                this.startDrag();
            }
            return;
        }

        e.preventDefault();
        e.stopPropagation();

        this.updateDragPosition(coords.clientX, coords.clientY);
        this.updateDropZones(coords.clientX, coords.clientY);
    }

    async onPointerUp(e) {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }

        if (this.isProcessingAction) return;

        if (!this.isDragging && this.draggedCards && this.draggedCards.length > 0) {
            this.isProcessingAction = true;
            try {
                await this.handleCardClick();
            } finally {
                this.isProcessingAction = false;
                this.resetDragState();
            }
            return;
        }

        if (this.isDragging) {
            this.isProcessingAction = true;
            try {
                await this.handleDrop(e);
            } finally {
                this.isProcessingAction = false;
            }
        }

        this.resetDragState();
    }

    getEventCoordinates(e) {
        if (e.type && e.type.startsWith('touch')) {
            const touchEvent = e.originalEvent || e;
            if (touchEvent.touches && touchEvent.touches.length > 0) {
                const touch = touchEvent.touches[0];
                return {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                };
            } else if (touchEvent.changedTouches && touchEvent.changedTouches.length > 0) {
                const touch = touchEvent.changedTouches[0];
                return {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                };
            }
        }
        return {
            clientX: e.clientX || 0,
            clientY: e.clientY || 0
        };
    }

    prepareDragData($cardElement) {
        const pileElement = $cardElement.closest('.tableau-pile');
        if (!pileElement.length) return;

        const pileId = pileElement.attr('id');
        const pileIndex = parseInt(pileId.split('-')[1]);

        const cards = pileElement.find('.card');
        let cardIndex = -1;
        for (let i = 0; i < cards.length; i++) {
            if (cards[i] === $cardElement[0]) {
                cardIndex = i;
                break;
            }
        }

        if (cardIndex === -1) return;

        this.draggedFrom = { type: 'tableau', index: pileIndex };

        if (this.tableau[pileIndex].length === 0 || cardIndex >= this.tableau[pileIndex].length) {
            this.draggedCards = [];
            return;
        }

        const clickedCard = this.tableau[pileIndex][cardIndex];
        if (!clickedCard) {
            this.draggedCards = [];
            return;
        }
        this.clickedCard = clickedCard;

        const topCardIndex = this.tableau[pileIndex].length - 1;
        this.draggedCards = this.getMovableCards(pileIndex, topCardIndex);

        const clickedCardIndex = this.draggedCards.findIndex(card => card.id === clickedCard.id);
        if (clickedCardIndex !== -1) {
            this.draggedCards = this.draggedCards.slice(clickedCardIndex);
        } else {
            this.draggedCards = [];
        }

        const isClickable = this.draggedCards.some(movableCard => movableCard.id === clickedCard.id);
        if (!isClickable) {
            this.draggedCards = [];
            return;
        }
        if (this.draggedCards.length === 0) return;

        this.originalCardPositions = [];

        const sourcePile = this.tableau[pileIndex];
        const startIndex = sourcePile.length - this.draggedCards.length;

        const allCardElements = pileElement.find('.card');
        for (let j = 0; j < this.draggedCards.length; j++) {
            const domIndex = startIndex + j;
            if (domIndex >= 0 && domIndex < allCardElements.length) {
                const card = allCardElements[domIndex];
                const $card = $(card);
                const rect = card.getBoundingClientRect();
                this.originalCardPositions.push({
                    element: $card,
                    left: rect.left,
                    top: rect.top,
                    position: $card.css('position'),
                    zIndex: $card.css('z-index')
                });
            }
        }
    }

    startDrag() {
        this.isDragging = true;

        this.draggedElements = [];
        
        this.originalCardPositions.forEach((pos, index) => {
            const $card = pos.element;
            const rect = $card[0].getBoundingClientRect();

            $card.css({
                position: 'fixed',
                left: rect.left + 'px',
                top: rect.top + 'px',
                zIndex: index + 9999,
                pointerEvents: 'none',
                transition: 'none',
                transform: 'none'
            });
            this.draggedElements.push($card);
        });
    }

    updateDragPosition(clientX, clientY) {
        if (!this.draggedElements || this.draggedElements.length === 0) return;

        requestAnimationFrame(() => {
            if (!Array.isArray(this.draggedElements) || this.draggedElements.length === 0) return;

            const baseX = clientX - this.dragOffset.x;
            const baseY = clientY - this.dragOffset.y;
            const spacing = window.innerWidth < 768 ? 8 : (window.innerWidth < 1024 ? 15 : 25);
            const scale = this.isMobile() ? 1.05 : 1.02;

            this.draggedElements.forEach((element, index) => {
                element.css({
                    left: baseX + 'px',
                    top: (baseY + index * spacing) + 'px',
                    transform: `scale(${scale})`,
                    opacity: 0.95,
                    zIndex: 1000 + index
                });
            });
        });
    }

    updateDropZones(clientX, clientY) {
        $('.drop-zone-valid, .drop-zone-invalid').removeClass('drop-zone-valid drop-zone-invalid');

        const elementBelow = document.elementFromPoint(clientX, clientY);
        if (!elementBelow) return;

        const $pile = $(elementBelow).closest('.tableau-pile');
        if (!$pile.length) return;
    }

    async handleDrop(e) {
        const coords = this.getEventCoordinates(e);
        const elementBelow = document.elementFromPoint(coords.clientX, coords.clientY);
        
        if (!elementBelow) {
            this.returnCardsToOriginalPosition();
            return;
        }

        const $targetPile = $(elementBelow).closest('.tableau-pile');
        if (!$targetPile.length) {
            this.returnCardsToOriginalPosition();
            return;
        }

        const targetPileIndex = parseInt($targetPile.attr('id').split('-')[1]);

        if (this.isValidMove(targetPileIndex)) {
            await this.executeMove(targetPileIndex);
            await this.checkForCompletedSequences();
            this.checkWinCondition();
        } else {
            this.returnCardsToOriginalPosition();
        }
    }

    async handleCardClick() {
        const validMoves = this.findValidMoves();

        if (validMoves.length === 0) {
            return;
        }

        const bestMove = this.selectBestMove(validMoves);

        await this.executeAutoMove(bestMove.targetPile);
    }

    findValidMoves() {
        const validMoves = [];

        for (let i = 0; i < 10; i++) {
            if (i !== this.draggedFrom.index && this.isValidMove(i)) {
                const targetPile = this.tableau[i];
                validMoves.push({
                    targetPile: i,
                    pileLength: targetPile.length,
                    isEmpty: targetPile.length === 0
                });
            }
        }

        return validMoves;
    }

    selectBestMove(validMoves) {
        const nonEmptyMoves = validMoves.filter(move => !move.isEmpty);
        const emptyMoves = validMoves.filter(move => move.isEmpty);

        if (nonEmptyMoves.length > 0) {
            const movesWithSequenceLength = nonEmptyMoves.map(move => {
                const targetPile = this.tableau[move.targetPile];
                const movingCard = this.draggedCards[0];

                let sequenceLength = this.draggedCards.length;
                for (let i = targetPile.length - 1; i >= 0; i--) {
                    const card = targetPile[i];
                    if (!card.faceUp) break;

                    const expectedValue = movingCard.value + (sequenceLength);
                    if (card.value === expectedValue && card.suit === movingCard.suit) {
                        sequenceLength++;
                    } else {
                        break;
                    }
                }

                return {
                    ...move,
                    sequenceLength: sequenceLength
                };
            });

            return movesWithSequenceLength.reduce((best, current) =>
                current.sequenceLength > best.sequenceLength ? current : best
            );
        }

        if (emptyMoves.length > 0) {
            return emptyMoves[0];
        }

        return validMoves[0];
    }

    async executeAutoMove(targetPileIndex) {
        const pileIndex = this.draggedFrom.index;
        const cards = this.draggedCards;
        const fromElements = [];

        const $pileElement = $(`#tableau-${pileIndex}`);
        const allCardElements = $pileElement.find('.card');

        const sourcePile = this.tableau[pileIndex];
        const startIndex = sourcePile.length - cards.length;

        for (let j = 0; j < cards.length; j++) {
            const domIndex = startIndex + j;
            if (domIndex >= 0 && domIndex < allCardElements.length) {
                const $el = $(allCardElements[domIndex]);
                fromElements.push($el);
            }
        }

        if (fromElements.length === cards.length) {
            await this.animateAutoMatchFlight(cards, fromElements, targetPileIndex);
            await this.executeMove(targetPileIndex);
            await this.checkForCompletedSequences();
            this.checkWinCondition();
        }
    }

    async animateAutoMatchFlight(cards, fromElements, toPileIndex) {
        this.isAnimatingCard = true;

        fromElements.forEach($el => $el.css('visibility', 'hidden'));

        const tempCards = [];
        const startPositions = [];
        const endPositions = [];

        const spacing = window.innerWidth < 1024 ? 15 : 25;

        for (let i = 0; i < cards.length; i++) {
            const $tempCard = this.createCardElement(cards[i]);
            const startRect = fromElements[i][0].getBoundingClientRect();

            $tempCard.css({
                position: 'fixed',
                left: startRect.left + 'px',
                top: startRect.top + 'px',
                width: startRect.width + 'px',
                height: startRect.height + 'px',
                zIndex: 10001 + i,
                pointerEvents: 'none',
                margin: 0,
                boxSizing: 'border-box',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
            });
            $('body').append($tempCard);
            tempCards.push($tempCard);

            startPositions.push({
                x: startRect.left + startRect.width / 2,
                y: startRect.top + startRect.height / 2
            });

            const targetPos = this.getTableauPileTargetPosition(toPileIndex);

            endPositions.push({
                x: targetPos.x,
                y: targetPos.y + (i * spacing)
            });
        }

        await new Promise(resolve => requestAnimationFrame(resolve));

        await this.animateCardMovement(tempCards, startPositions, endPositions, {
            speedMultiplier: 2.4,
            animationType: 'flying-to-tableau-real',
            staggerDelay: 0,
            baseSpeed: 1200
        });

        tempCards.forEach($el => $el.remove());
        this.isAnimatingCard = false;
    }

    isValidMove(targetPileIndex) {
        if (targetPileIndex === this.draggedFrom.index) return false;

        const targetPile = this.tableau[targetPileIndex];
        const topCard = targetPile.length > 0 ? targetPile[targetPile.length - 1] : null;

        const cardToValidate = this.clickedCard || this.draggedCards[0];

        return this.canPlaceCard(cardToValidate, topCard);
    }

    async executeMove(targetPileIndex) {
        this.isAnimatingCard = true;

        const sourcePileIndex = this.draggedFrom.index;
        const movedCards = this.draggedCards;

        const sourcePile = this.tableau[sourcePileIndex];
        const cardsToMove = sourcePile.splice(-movedCards.length);

        this.tableau[targetPileIndex].push(...cardsToMove);

        let flippedCard = false;
        if (sourcePile.length > 0) {
            const newTopCard = sourcePile[sourcePile.length - 1];
            if (!newTopCard.faceUp) {
                newTopCard.faceUp = true;
                this.score += 5;
                flippedCard = true;
            }
        }

        this.moves++;

        const comboData = this.calculateComboBonus();
        let moveScore = 1;

        const sequenceLength = movedCards.length;
        if (sequenceLength > 1) {
            moveScore += (sequenceLength - 1) * 2;
        }

        moveScore = Math.floor(moveScore * comboData.multiplier);

        this.score += moveScore + comboData.bonus;

        this.showComboEffect(comboData);

        this.animateScoreUpdate();

        this.recordMove({
            type: 'move',
            from: this.draggedFrom,
            to: { type: 'tableau', index: targetPileIndex },
            cards: [...this.draggedCards],
            flippedCard: flippedCard
        });

        this.updateDisplay();
        this.isAnimatingCard = false;

        this.saveGameState();
    }

    animateScoreUpdate() {
        const $scoreElement = $('#score');
        $scoreElement.addClass('score-updating');
        setTimeout(() => {
            $scoreElement.removeClass('score-updating');
        }, 500);
    }

    returnCardsToOriginalPosition() {
        if (!Array.isArray(this.draggedElements) || this.draggedElements.length === 0) return;

        const transitionDuration = this.isMobile() ? 0.2 : 0.3;

        this.draggedElements.forEach((element, index) => {
            const originalPos = this.originalCardPositions[index];
            element.addClass('returning');
            element.css({
                left: originalPos.left + 'px',
                top: originalPos.top + 'px',
                transform: 'scale(1)',
                opacity: 1,
                zIndex: originalPos.zIndex || 'auto',
                transition: `all ${transitionDuration}s ease-out`
            });
        });

        setTimeout(() => {
            if (Array.isArray(this.draggedElements)) {
                this.draggedElements.forEach(element => {
                    element.removeClass('returning');
                    element.css({
                        transition: '',
                        transform: '',
                        opacity: ''
                    });
                });
            }
            this.updateDisplay();
        }, transitionDuration * 1000);
    }

    onKeyDown(e) {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        switch(e.key) {
            case 'Escape':
                this.hideHelp();
                this.hideMessage();
                break;
            case 'h':
            case 'H':
                if (!e.ctrlKey && !e.metaKey) {
                    if (!this.canPerformAction('hint')) return;
                    this.setActionCooldown('hint');
                    this.showHint();
                }
                break;
            case 'n':
            case 'N':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    if (!this.canPerformAction('newGame')) return;
                    this.setActionCooldown('newGame');
                    this.newGame();
                }
                break;
            case 'z':
            case 'Z':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    if (!this.canPerformAction('undo')) return;
                    this.setActionCooldown('undo');
                    this.undoMove();
                }
                break;
            case ' ':
                e.preventDefault();
                if (!this.canPerformAction('dealStock')) return;
                this.setActionCooldown('dealStock');
                this.isProcessingAction = true;
                this.dealFromStock().finally(() => {
                    this.isProcessingAction = false;
                });
                break;
        }
    }

    undoMove() {
        if (this.moveHistory.length === 0) return;
        if (this.isAnimatingCard || this.isProcessingAction) return;

        // 重置连击
        this.comboCount = 0;

        const lastMove = this.moveHistory.pop();
        
        switch(lastMove.type) {
            case 'move':
                // Reverse the move
                const cardsToReverse = this.tableau[lastMove.to.index].splice(-lastMove.cards.length);
                this.tableau[lastMove.from.index].push(...cardsToReverse);
                
                // Handle face-down card that might have been flipped
                if (this.tableau[lastMove.from.index].length > lastMove.cards.length) {
                    const cardBelowMoved = this.tableau[lastMove.from.index][this.tableau[lastMove.from.index].length - lastMove.cards.length - 1];
                    if (cardBelowMoved && cardBelowMoved.faceUp && lastMove.flippedCard) {
                        cardBelowMoved.faceUp = false;
                        this.score -= 5;
                    }
                }
                
                this.moves--;
                this.score -= 1;
                break;
                
            case 'deal':
                // Reverse dealing cards
                for (let i = 0; i < 10 && this.tableau[i].length > 0; i++) {
                    const card = this.tableau[i].pop();
                    card.faceUp = false;
                    this.stock.push(card);
                }
                this.moves--;
                break;
        }
        
        this.updateDisplay();
    }

    showHint() {
        if (this.isAnimatingCard || this.isProcessingAction) return;
        $('.hint-highlight').removeClass('hint-highlight');

        const possibleMove = this.findBestMove();
        if (possibleMove) {
            const $pile = $(`#tableau-${possibleMove.fromPile}`);
            const $card = $pile.find('.card').eq(possibleMove.cardIndex);
            $card.addClass('hint-highlight');

            setTimeout(() => {
                $('.hint-highlight').removeClass('hint-highlight');
            }, 2000);
        } else {
            // Check if game is deadlocked
            if (this.stock.length === 0 && !this.hasAnyValidMoves()) {
                this.showGameOverMessage();
            } else if (this.stock.length > 0) {
                // Suggest dealing from stock
                $('#stock').addClass('hint-highlight');
                setTimeout(() => {
                    $('#stock').removeClass('hint-highlight');
                }, 2000);
            }
        }
    }

    findBestMove() {
        let allPossibleMoves = [];

        // Find all possible moves
        for (let fromPile = 0; fromPile < 10; fromPile++) {
            const cardStates = this.calculateCardStates(fromPile);
            if (cardStates.movableCards.length === 0) continue;

            const pile = this.tableau[fromPile];

            // Only check movable cards, not all cards
            for (const movableCard of cardStates.movableCards) {
                const cardIndex = pile.findIndex(card => card.id === movableCard.id);
                if (cardIndex === -1) continue;

                const movableCards = this.getMovableCards(fromPile, cardIndex);
                if (movableCards.length === 0) continue;

                for (let toPile = 0; toPile < 10; toPile++) {
                    if (toPile === fromPile) continue;

                    const targetPile = this.tableau[toPile];
                    const topCard = targetPile.length > 0 ? targetPile[targetPile.length - 1] : null;

                    if (this.canPlaceCard(movableCards[0], topCard)) {
                        // Calculate sequence length for this move
                        let sequenceLength = movableCards.length;
                        const movingCard = movableCards[0];

                        // Check backwards from the target pile's top card
                        for (let i = targetPile.length - 1; i >= 0; i--) {
                            const targetCard = targetPile[i];
                            if (!targetCard.faceUp) break;

                            const expectedValue = movingCard.value + sequenceLength;
                            if (targetCard.value === expectedValue && targetCard.suit === movingCard.suit) {
                                sequenceLength++;
                            } else {
                                break;
                            }
                        }

                        allPossibleMoves.push({
                            fromPile,
                            cardIndex,
                            toPile,
                            sequenceLength,
                            isEmpty: targetPile.length === 0,
                            movableCards
                        });
                    }
                }
            }
        }

        if (allPossibleMoves.length === 0) {
            return null;
        }

        // 增强提示优先级算法
        allPossibleMoves.forEach(move => {
            move.priority = this.calculateMovePriority(move);
        });

        // Sort by enhanced priority
        allPossibleMoves.sort((a, b) => {
            return b.priority - a.priority;
        });

        return allPossibleMoves[0];
    }

    // 计算移动的智能优先级
    calculateMovePriority(move) {
        let priority = 0;

        // 1. 序列长度奖励 (最重要)
        priority += move.sequenceLength * 100;

        // 2. 翻牌奖励 (非常重要)
        const sourcePile = this.tableau[move.fromPile];
        const cardBelowIndex = move.cardIndex - 1;
        if (cardBelowIndex >= 0 && !sourcePile[cardBelowIndex].faceUp) {
            priority += 200; // 能翻牌的移动优先级很高
        }

        // 3. 空牌堆惩罚 (除非是K)
        if (move.isEmpty) {
            const movingCard = move.movableCards[0];
            if (movingCard.value === 13) { // K
                priority += 50; // K到空牌堆是好移动
            } else {
                priority -= 50; // 其他牌到空牌堆不太好
            }
        }

        // 4. 同花色连接奖励
        const targetPile = this.tableau[move.toPile];
        if (targetPile.length > 0) {
            const topCard = targetPile[targetPile.length - 1];
            const movingCard = move.movableCards[0];
            if (topCard.suit === movingCard.suit) {
                priority += 80; // 同花色连接很好
            }
        }

        // 5. 释放被压牌奖励
        const cardsBelow = sourcePile.slice(0, move.cardIndex);
        const faceDownBelow = cardsBelow.filter(card => !card.faceUp).length;
        priority += faceDownBelow * 30; // 每张被压的暗牌增加优先级

        // 6. 避免破坏已有序列
        if (move.cardIndex > 0) {
            const cardBelow = sourcePile[move.cardIndex - 1];
            const movingCard = move.movableCards[0];
            if (cardBelow.faceUp && cardBelow.suit === movingCard.suit &&
                cardBelow.value === movingCard.value + 1) {
                priority -= 30; // 破坏同花色序列的惩罚
            }
        }

        return priority;
    }

    // 保存游戏状态到本地存储
    saveGameState() {
        if (this.gameWon || this.moves === 0) return; // 不保存已完成或未开始的游戏

        const gameState = {
            tableau: this.tableau,
            stock: this.stock,
            completed: this.completed,
            score: this.score,
            moves: this.moves,
            difficulty: this.difficulty,
            startTime: this.startTime,
            elapsedTime: this.elapsedTime,
            comboCount: this.comboCount,
            lastMoveTime: this.lastMoveTime,
            moveHistory: this.moveHistory,
            timestamp: Date.now()
        };

        try {
            localStorage.setItem('spider-solitaire-save', JSON.stringify(gameState));
        } catch (e) {
            console.warn('Unable to save game state:', e);
        }
    }

    // 从本地存储恢复游戏状态
    loadGameState() {
        try {
            const savedState = localStorage.getItem('spider-solitaire-save');
            if (!savedState) return false;

            const gameState = JSON.parse(savedState);

            // 检查保存时间，超过24小时的存档自动删除
            const now = Date.now();
            if (now - gameState.timestamp > 24 * 60 * 60 * 1000) {
                this.clearSavedGame();
                return false;
            }

            // 恢复游戏状态
            this.tableau = gameState.tableau;
            this.stock = gameState.stock;
            this.completed = gameState.completed;
            this.score = gameState.score;
            this.moves = gameState.moves;
            this.difficulty = gameState.difficulty;
            this.startTime = gameState.startTime;
            this.elapsedTime = gameState.elapsedTime || 0;
            this.comboCount = gameState.comboCount || 0;
            this.lastMoveTime = gameState.lastMoveTime || 0;
            this.moveHistory = gameState.moveHistory || [];

            return true;
        } catch (e) {
            console.warn('Unable to restore game state:', e);
            this.clearSavedGame();
            return false;
        }
    }

    // 清除保存的游戏
    clearSavedGame() {
        try {
            localStorage.removeItem('spider-solitaire-save');
        } catch (e) {
            console.warn('Unable to clear saved game:', e);
        }
    }

    // 检查是否有保存的游戏
    hasSavedGame() {
        try {
            const savedState = localStorage.getItem('spider-solitaire-save');
            if (!savedState) return false;

            const gameState = JSON.parse(savedState);
            const now = Date.now();

            // 检查是否过期
            if (now - gameState.timestamp > 24 * 60 * 60 * 1000) {
                this.clearSavedGame();
                return false;
            }

            return true;
        } catch (e) {
            return false;
        }
    }

    // 显示恢复游戏对话框
    showRestoreGameDialog() {
        if (typeof gameAlert === 'function') {
            // 使用自定义模态框，避免与modal.js冲突
            const modal = document.querySelector('#restore-game-modal') || document.createElement('div');
            modal.id = 'restore-game-modal';
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal">
                    <h2 class="modal-title">🎮 Restore Game</h2>
                    <div class="modal-content">
                        <p>Unfinished game detected. Would you like to restore it?</p>
                        <p style="font-size: 14px; color: rgba(255,255,255,0.7);">
                            Saved games will be automatically deleted after 24 hours
                        </p>
                    </div>
                    <div class="modal-buttons">
                        <button id="restoreGameBtn" class="btn btn-primary">Restore Game</button>
                        <button id="newGameInsteadBtn" class="btn btn-secondary">Start New Game</button>
                    </div>
                </div>
            `;

            if (!document.body.contains(modal)) {
                document.body.appendChild(modal);
            }

            modal.classList.remove('hidden');
            modal.classList.add('show');

            // 阻止点击空白区域关闭对话框
            modal.onclick = (e) => {
                if (e.target === modal) {
                    e.stopPropagation();
                    e.preventDefault();
                    return false;
                }
            };

            // 阻止modal内部点击事件冒泡
            const modalContent = modal.querySelector('.modal');
            if (modalContent) {
                modalContent.onclick = (e) => {
                    e.stopPropagation();
                };
            }

            // 绑定事件
            $('#restoreGameBtn').on('click', () => {
                if (this.loadGameState()) {
                    this.updateDisplay();
                    this.startTimer();
                    modal.classList.add('hidden');
                } else {
                    alert('Failed to restore game, starting new game');
                    this.initializeGame();
                    modal.classList.add('hidden');
                }
            });

            $('#newGameInsteadBtn').on('click', () => {
                this.clearSavedGame();
                this.initializeGame();
                modal.classList.add('hidden');
            });
        } else {
            // Fallback to system confirm dialog
            if (confirm('Unfinished game detected. Would you like to restore it?')) {
                if (this.loadGameState()) {
                    this.updateDisplay();
                    this.startTimer();
                } else {
                    alert('Failed to restore game, starting new game');
                    this.initializeGame();
                }
            } else {
                this.clearSavedGame();
                this.initializeGame();
            }
        }
    }

    hasAnyValidMoves() {
        // Check if there are any valid card moves
        for (let fromPile = 0; fromPile < 10; fromPile++) {
            const cardStates = this.calculateCardStates(fromPile);
            if (cardStates.movableCards.length === 0) continue;

            const pile = this.tableau[fromPile];

            // Only check movable cards, not all cards
            for (const movableCard of cardStates.movableCards) {
                const cardIndex = pile.findIndex(card => card.id === movableCard.id);
                if (cardIndex === -1) continue;

                const movableCards = this.getMovableCards(fromPile, cardIndex);
                if (movableCards.length === 0) continue;

                for (let toPile = 0; toPile < 10; toPile++) {
                    if (toPile === fromPile) continue;

                    const targetPile = this.tableau[toPile];
                    const topCard = targetPile.length > 0 ? targetPile[targetPile.length - 1] : null;

                    if (this.canPlaceCard(movableCards[0], topCard)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    showGameOverMessage() {
        $('#messageTitle').text('🎯 Game Over');
        $('#messageText').html(`
            <div style="margin-bottom: 15px;">
                <div style="font-size: 1.1em; color: #ff6b6b; margin-bottom: 8px;">😔 No More Moves</div>
                <div>Better luck next time!</div>
            </div>
        `);

        // Update message stats to include completed sequences
        $('.message-stats').html(`
            <div style="background: rgba(255,107,107,0.2); border: 1px solid #ff6b6b;">
                <div style="color: #ff6b6b; font-size: 16px; margin-bottom: 4px;">🎯 Completed</div>
                <div style="font-size: 18px; font-weight: bold;">${this.completed.length}/8</div>
            </div>
            <div style="background: rgba(255,107,107,0.2); border: 1px solid #ff6b6b;">
                <div style="color: #ff6b6b; font-size: 16px; margin-bottom: 4px;">🏆 Score</div>
                <div style="font-size: 18px; font-weight: bold;">${this.score}</div>
            </div>
            <div style="background: rgba(255,107,107,0.2); border: 1px solid #ff6b6b;">
                <div style="color: #ff6b6b; font-size: 16px; margin-bottom: 4px;">⏱️ Time</div>
                <div style="font-size: 18px; font-weight: bold;">${this.formatTime(this.elapsedTime)}</div>
            </div>
            <div style="background: rgba(255,107,107,0.2); border: 1px solid #ff6b6b;">
                <div style="color: #ff6b6b; font-size: 16px; margin-bottom: 4px;">🎯 Moves</div>
                <div style="font-size: 18px; font-weight: bold;">${this.moves}</div>
            </div>
        `);

        // Simple buttons without difficulty selection
        $('.message-buttons').html(`
            <button id="restartGameBtn" class="btn btn-primary" style="background: linear-gradient(135deg, #007bff, #0056b3); border: 2px solid #007bff;">
                🔄 Try Again
            </button>
            <button id="closeGameOverBtn" class="btn btn-secondary" style="background: linear-gradient(135deg, #6c757d, #495057);">
                🏠 Close
            </button>
        `);

        $('#gameMessage').removeClass('hidden');

        $('#restartGameBtn').on('click', () => {
            this.newGame();
            this.hideMessage();
        });

        $('#closeGameOverBtn').on('click', () => {
            this.hideMessage();
        });
    }

    showHelp() {
        $('#helpPanel').removeClass('hidden');
    }

    hideHelp() {
        $('#helpPanel').addClass('hidden');
    }

    hideMessage() {
        $('#gameMessage').addClass('hidden');
    }

    showWinMessage() {
        $('#messageTitle').text('🎉 Congratulations!');

        // Create a more detailed win message based on difficulty
        let difficultyText = '';
        switch(this.difficulty) {
            case 'easy': difficultyText = 'Easy (1 Suit)'; break;
            case 'medium': difficultyText = 'Medium (2 Suits)'; break;
            case 'hard': difficultyText = 'Hard (4 Suits)'; break;
            default: difficultyText = 'Spider Solitaire';
        }

        $('#messageText').html(`
            <div style="margin-bottom: 15px;">
                <div style="font-size: 1.3em; color: #7fb069; margin-bottom: 8px;">🏆 Victory!</div>
                <div>You completed ${difficultyText} Spider Solitaire!</div>
            </div>
        `);

        // Enhanced stats display
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
        const seconds = (elapsed % 60).toString().padStart(2, '0');

        $('.message-stats').html(`
            <div style="background: rgba(127,176,105,0.2); border: 1px solid #7fb069;">
                <div style="color: #7fb069; font-size: 16px; margin-bottom: 4px;">🏆 Score</div>
                <div style="font-size: 18px; font-weight: bold;">${this.score}</div>
            </div>
            <div style="background: rgba(127,176,105,0.2); border: 1px solid #7fb069;">
                <div style="color: #7fb069; font-size: 16px; margin-bottom: 4px;">⏱️ Time</div>
                <div style="font-size: 18px; font-weight: bold;">${minutes}:${seconds}</div>
            </div>
            <div style="background: rgba(127,176,105,0.2); border: 1px solid #7fb069;">
                <div style="color: #7fb069; font-size: 16px; margin-bottom: 4px;">🎯 Moves</div>
                <div style="font-size: 18px; font-weight: bold;">${this.moves}</div>
            </div>
        `);

        // Enhanced buttons
        $('.message-buttons').html(`
            <button id="playAgainBtn" class="btn btn-success" style="background: linear-gradient(135deg, #28a745, #20c997); border: 2px solid #28a745;">
                🎮 Play Again
            </button>
            <button id="closeMessageBtn" class="btn btn-secondary" style="background: linear-gradient(135deg, #6c757d, #495057);">
                🏠 Back to Home
            </button>
        `);

        $('#gameMessage').removeClass('hidden');

        // Add event listeners for the new buttons
        $('#playAgainBtn').on('click', () => {
            this.newGame();
            this.hideMessage();
        });

        $('#closeMessageBtn').on('click', () => {
            this.hideMessage();
            window.location.href = '/';
        });
    }

    autoFullscreen() {
        if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen().catch(() => {});
        } else if (document.documentElement.webkitRequestFullscreen) {
            document.documentElement.webkitRequestFullscreen();
        } else if (document.documentElement.mozRequestFullScreen) {
            document.documentElement.mozRequestFullScreen();
        } else if (document.documentElement.msRequestFullscreen) {
            document.documentElement.msRequestFullscreen();
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement && !document.webkitFullscreenElement && 
            !document.mozFullScreenElement && !document.msFullscreenElement) {
            this.autoFullscreen();
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
    }

    updateFullscreenButton() {
        const isFullscreen = !!(document.fullscreenElement || document.webkitFullscreenElement || 
                              document.mozFullScreenElement || document.msFullscreenElement);
        $('#fullscreenBtn').text(isFullscreen ? '⛶' : '⛶');
    }
}

$(document).ready(function() {
    window.game = new SpiderSolitaire();

    if (window.game.hasSavedGame()) {
        window.game.showRestoreGameDialog();
    } else {
        window.game.initializeGame();
    }
});

