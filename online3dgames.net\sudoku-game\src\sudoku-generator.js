/**
 * Sudoku generator class
 * Use the empty algorithm to generate different difficulty Sudoku puzzles
 */
class SudokuGenerator {
    constructor() {
        this.board = Array(9).fill().map(() => Array(9).fill(0));
    }
    
    /**
     * Generate a complete Sudoku solution
     */
    generateComplete() {
        // Reset the board
        this.board = Array(9).fill().map(() => Array(9).fill(0));

        // Randomly fill the first row as a seed
        this.fillFirstRowRandomly();

        // Fill the remaining cells
        this.fillRemaining(1, 0);

        // Random transformations to increase diversity
        this.applyRandomTransformations();

        return this.board.map(row => [...row]);
    }

    /**
     * Randomly fill the first row
     */
    fillFirstRowRandomly() {
        const numbers = this.shuffleArray([1, 2, 3, 4, 5, 6, 7, 8, 9]);
        for (let col = 0; col < 9; col++) {
            this.board[0][col] = numbers[col];
        }
    }

    /**
     * Apply random transformations to increase diversity
     */
    applyRandomTransformations() {
        const transformations = Math.floor(Math.random() * 5) + 3; // 3-7 transformations

        for (let i = 0; i < transformations; i++) {
            const transformType = Math.floor(Math.random() * 6);

            switch (transformType) {
                case 0:
                    this.swapRows();
                    break;
                case 1:
                    this.swapColumns();
                    break;
                case 2:
                    this.swapRowGroups();
                    break;
                case 3:
                    this.swapColumnGroups();
                    break;
                case 4:
                    this.transpose();
                    break;
                case 5:
                    this.swapNumbers();
                    break;
            }
        }
    }

    /**
     * Swap two rows within the same 3x3 box
     */
    swapRows() {
        const group = Math.floor(Math.random() * 3); // Select 3x3 box group (0, 1, 2)
        const row1 = group * 3 + Math.floor(Math.random() * 3);
        const row2 = group * 3 + Math.floor(Math.random() * 3);

        if (row1 !== row2) {
            [this.board[row1], this.board[row2]] = [this.board[row2], this.board[row1]];
        }
    }

    /**
     * Swap two columns within the same 3x3 box
     */
    swapColumns() {
        const group = Math.floor(Math.random() * 3); // Select 3x3 box group (0, 1, 2)
        const col1 = group * 3 + Math.floor(Math.random() * 3);
        const col2 = group * 3 + Math.floor(Math.random() * 3);

        if (col1 !== col2) {
            for (let row = 0; row < 9; row++) {
                [this.board[row][col1], this.board[row][col2]] = [this.board[row][col2], this.board[row][col1]];
            }
        }
    }

    /**
     * Swap two row groups (rows of 3x3 boxes)
     */
    swapRowGroups() {
        const group1 = Math.floor(Math.random() * 3);
        const group2 = Math.floor(Math.random() * 3);

        if (group1 !== group2) {
            for (let i = 0; i < 3; i++) {
                [this.board[group1 * 3 + i], this.board[group2 * 3 + i]] =
                [this.board[group2 * 3 + i], this.board[group1 * 3 + i]];
            }
        }
    }

    /**
     * Swap two column groups (columns of 3x3 boxes)
     */
    swapColumnGroups() {
        const group1 = Math.floor(Math.random() * 3);
        const group2 = Math.floor(Math.random() * 3);

        if (group1 !== group2) {
            for (let row = 0; row < 9; row++) {
                for (let i = 0; i < 3; i++) {
                    [this.board[row][group1 * 3 + i], this.board[row][group2 * 3 + i]] =
                    [this.board[row][group2 * 3 + i], this.board[row][group1 * 3 + i]];
                }
            }
        }
    }

    /**
     * Transpose the matrix (swap rows and columns)
     */
    transpose() {
        const newBoard = Array(9).fill().map(() => Array(9).fill(0));
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                newBoard[col][row] = this.board[row][col];
            }
        }
        this.board = newBoard;
    }

    /**
     * Swap two numbers
     */
    swapNumbers() {
        const num1 = Math.floor(Math.random() * 9) + 1;
        const num2 = Math.floor(Math.random() * 9) + 1;

        if (num1 !== num2) {
            for (let row = 0; row < 9; row++) {
                for (let col = 0; col < 9; col++) {
                    if (this.board[row][col] === num1) {
                        this.board[row][col] = num2;
                    } else if (this.board[row][col] === num2) {
                        this.board[row][col] = num1;
                    }
                }
            }
        }
    }
    

    
    /**
     * Fill the remaining cells
     */
    fillRemaining(row, col) {
        // If we reach the next row, the filling is complete
        if (row === 9) {
            return true;
        }
        
        // If we reach the end of the row, move to the next row
        if (col === 9) {
            return this.fillRemaining(row + 1, 0);
        }
        
        // If the current cell is already filled, skip it
        if (this.board[row][col] !== 0) {
            return this.fillRemaining(row, col + 1);
        }
        
        // Try to fill in numbers 1-9
        const numbers = this.shuffleArray([1, 2, 3, 4, 5, 6, 7, 8, 9]);
        
        for (const num of numbers) {
            if (this.isSafe(row, col, num)) {
                this.board[row][col] = num;
                
                if (this.fillRemaining(row, col + 1)) {
                    return true;
                }
                
                this.board[row][col] = 0;
            }
        }
        
        return false;
    }
    
    /**
     * Check if placing a number in a specified position is safe
     */
    isSafe(row, col, num) {
        return this.isRowSafe(row, num) && 
               this.isColSafe(col, num) && 
               this.isBoxSafe(row - row % 3, col - col % 3, num);
    }
    
    /**
     * Check if a row is safe
     */
    isRowSafe(row, num) {
        for (let col = 0; col < 9; col++) {
            if (this.board[row][col] === num) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Check if a column is safe
     */
    isColSafe(col, num) {
        for (let row = 0; row < 9; row++) {
            if (this.board[row][col] === num) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Check if a 3x3 box is safe
     */
    isBoxSafe(boxRow, boxCol, num) {
        for (let row = 0; row < 3; row++) {
            for (let col = 0; col < 3; col++) {
                if (this.board[boxRow + row][boxCol + col] === num) {
                    return false;
                }
            }
        }
        return true;
    }
    
    /**
     * Shuffle an array
     */
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
    
    /**
     * Generate a Sudoku puzzle (by removing numbers)
     */
    generatePuzzle(difficulty = 'easy') {
        // First generate a complete solution
        const solution = this.generateComplete();
        const puzzle = solution.map(row => [...row]);

        // Determine the number of empty cells based on difficulty
        const removeCount = this.getRemoveCount(difficulty);

        // Use a more intelligent empty strategy
        this.removeNumbersStrategically(puzzle, removeCount);

        return {
            puzzle: puzzle,
            solution: solution
        };
    }

    /**
     * Strategic empty algorithm
     */
    removeNumbersStrategically(puzzle, targetCount) {
        let removed = 0;
        const maxAttempts = targetCount * 3; // Prevent infinite loop
        let attempts = 0;

        while (removed < targetCount && attempts < maxAttempts) {
            attempts++;

            // Select empty strategy
            const strategy = Math.random();
            let row, col;

            if (strategy < 0.3) {
                // 30% probability: randomly select
                row = Math.floor(Math.random() * 9);
                col = Math.floor(Math.random() * 9);
            } else if (strategy < 0.6) {
                // 30% probability: symmetric empty
                const positions = this.getSymmetricPositions();
                const randomPos = positions[Math.floor(Math.random() * positions.length)];
                row = randomPos.row;
                col = randomPos.col;
            } else {
                // 40% probability: distributed empty (avoid too much concentration in a region)
                const position = this.getDistributedPosition(puzzle);
                row = position.row;
                col = position.col;
            }

                // If the position is already empty, skip it
            if (puzzle[row][col] === 0) continue;

            // Try to empty
            const backup = puzzle[row][col];
            puzzle[row][col] = 0;

            // Simplified uniqueness check
            if (this.isValidRemoval(puzzle, row, col)) {
                removed++;

                // Symmetric empty (50% probability)
                if (Math.random() < 0.5 && removed < targetCount) {
                    const symRow = 8 - row;
                    const symCol = 8 - col;
                    if (puzzle[symRow][symCol] !== 0) {
                        puzzle[symRow][symCol] = 0;
                        removed++;
                    }
                }
            } else {
                // Restore number
                puzzle[row][col] = backup;
            }
        }

        // If not yet reached the target, perform the final random empty
        if (removed < targetCount) {
            this.finalRandomRemoval(puzzle, targetCount - removed);
        }
    }

    /**
     * Get symmetric positions
     */
    getSymmetricPositions() {
        const positions = [];
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                // Only add the upper half and the diagonal to avoid repetition
                if (row <= col) {
                    positions.push({ row, col });
                }
            }
        }
        return positions;
    }

    /**
     * Get distributed positions (avoid too much concentration)
     */
    getDistributedPosition(puzzle) {
        const regions = [];

        // Divide the board into 9 3x3 regions, calculate the number of empty cells in each region
        for (let boxRow = 0; boxRow < 3; boxRow++) {
            for (let boxCol = 0; boxCol < 3; boxCol++) {
                let emptyCount = 0;
                const positions = [];

                for (let r = boxRow * 3; r < (boxRow + 1) * 3; r++) {
                    for (let c = boxCol * 3; c < (boxCol + 1) * 3; c++) {
                        if (puzzle[r][c] === 0) {
                            emptyCount++;
                        } else {
                            positions.push({ row: r, col: c });
                        }
                    }
                }

                regions.push({ emptyCount, positions });
            }
        }

        // Select the region with the fewest empty cells
        regions.sort((a, b) => a.emptyCount - b.emptyCount);
        const selectedRegion = regions[0];

        if (selectedRegion.positions.length > 0) {
            return selectedRegion.positions[Math.floor(Math.random() * selectedRegion.positions.length)];
        }

        // If no suitable position, randomly select
        return {
            row: Math.floor(Math.random() * 9),
            col: Math.floor(Math.random() * 9)
        };
    }

    /**
     * Check if the removal is valid
     */
    isValidRemoval(puzzle, row, col) {
        // Simplified check: ensure each row, column, and 3x3 box has at least a certain number of numbers

        // Check row
        const rowCount = puzzle[row].filter(cell => cell !== 0).length;
        if (rowCount < 3) return false;

        // Check column
        const colCount = puzzle.map(r => r[col]).filter(cell => cell !== 0).length;
        if (colCount < 3) return false;

        // Check 3x3 box
        const boxRow = Math.floor(row / 3) * 3;
        const boxCol = Math.floor(col / 3) * 3;
        let boxCount = 0;

        for (let r = boxRow; r < boxRow + 3; r++) {
            for (let c = boxCol; c < boxCol + 3; c++) {
                if (puzzle[r][c] !== 0) boxCount++;
            }
        }

        return boxCount >= 3;
    }

    /**
     * The final random empty
     */
    finalRandomRemoval(puzzle, count) {
        const positions = [];
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (puzzle[row][col] !== 0) {
                    positions.push([row, col]);
                }
            }
        }

        this.shuffleArray(positions);

        for (let i = 0; i < Math.min(count, positions.length); i++) {
            const [row, col] = positions[i];
            puzzle[row][col] = 0;
        }
    }
    
    /**
     * Get the number of empty cells based on difficulty
     */
    getRemoveCount(difficulty) {
        const ranges = {
            'easy': { min: 35, max: 42 },      // Easy: 35-42 empty cells
            'medium': { min: 43, max: 52 },    // Medium: 43-52 empty cells
            'hard': { min: 53, max: 62 },      // Hard: 53-62 empty cells
            'expert': { min: 63, max: 72 }     // Expert: 63-72 empty cells
        };

        const range = ranges[difficulty] || ranges['easy'];
        // Randomly select within the range
        return range.min + Math.floor(Math.random() * (range.max - range.min + 1));
    }
    
    /**
     * Simplified unique solution check
     * Note: This is a simplified version, the real unique solution check requires a more complex algorithm
     */
    hasUniqueSolution(puzzle) {
        // Calculate the number of empty cells
        const emptyCount = puzzle.flat().filter(cell => cell === 0).length;
        
        // If there are too many empty cells, there may be no unique solution
        if (emptyCount > 70) return false;
        
        // Check if each row, column, and 3x3 box has at least some numbers
        for (let i = 0; i < 9; i++) {
            // Check row
            const rowCount = puzzle[i].filter(cell => cell !== 0).length;
            if (rowCount < 2) return false;
            
            // Check column
            const colCount = puzzle.map(row => row[i]).filter(cell => cell !== 0).length;
            if (colCount < 2) return false;
        }
        
        // Check 3x3 boxes
        for (let boxRow = 0; boxRow < 9; boxRow += 3) {
            for (let boxCol = 0; boxCol < 9; boxCol += 3) {
                let boxCount = 0;
                for (let row = boxRow; row < boxRow + 3; row++) {
                    for (let col = boxCol; col < boxCol + 3; col++) {
                        if (puzzle[row][col] !== 0) boxCount++;
                    }
                }
                if (boxCount < 2) return false;
            }
        }
        
        return true;
    }
    
    /**
     * Generate preset Sudoku puzzles (for quick testing)
     */
    generatePreset(difficulty = 'easy') {
        const presets = {
            easy: {
                puzzle: [
                    [5, 3, 0, 0, 7, 0, 0, 0, 0],
                    [6, 0, 0, 1, 9, 5, 0, 0, 0],
                    [0, 9, 8, 0, 0, 0, 0, 6, 0],
                    [8, 0, 0, 0, 6, 0, 0, 0, 3],
                    [4, 0, 0, 8, 0, 3, 0, 0, 1],
                    [7, 0, 0, 0, 2, 0, 0, 0, 6],
                    [0, 6, 0, 0, 0, 0, 2, 8, 0],
                    [0, 0, 0, 4, 1, 9, 0, 0, 5],
                    [0, 0, 0, 0, 8, 0, 0, 7, 9]
                ],
                solution: [
                    [5, 3, 4, 6, 7, 8, 9, 1, 2],
                    [6, 7, 2, 1, 9, 5, 3, 4, 8],
                    [1, 9, 8, 3, 4, 2, 5, 6, 7],
                    [8, 5, 9, 7, 6, 1, 4, 2, 3],
                    [4, 2, 6, 8, 5, 3, 7, 9, 1],
                    [7, 1, 3, 9, 2, 4, 8, 5, 6],
                    [9, 6, 1, 5, 3, 7, 2, 8, 4],
                    [2, 8, 7, 4, 1, 9, 6, 3, 5],
                    [3, 4, 5, 2, 8, 6, 1, 7, 9]
                ]
            }
        };
        
        if (presets[difficulty]) {
            return presets[difficulty];
        }
        
        // If no preset, generate a new one
        return this.generatePuzzle(difficulty);
    }
}
