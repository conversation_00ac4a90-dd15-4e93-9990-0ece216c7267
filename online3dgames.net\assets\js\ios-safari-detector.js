/**
 * iOS Safari Detection and Add to Home Screen Prompt
 * Universal script for all game pages
 */

(function() {
    'use strict';

    function detectIOSSafari() {
        const userAgent = navigator.userAgent;
        const isIOS = /iPad|iPhone|iPod/.test(userAgent) ||
                     (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
        const isSafari = /Safari/.test(userAgent) && !/Chrome|CriOS|FxiOS|EdgiOS|OPiOS/.test(userAgent);
        const isStandalone = window.navigator.standalone === true;

        console.log('iOS Detection Debug:', {
            userAgent: userAgent,
            isIOS: isIOS,
            isSafari: isSafari,
            isStandalone: isStandalone,
            platform: navigator.platform,
            maxTouchPoints: navigator.maxTouchPoints,
            shouldShow: isIOS && isSafari && !isStandalone
        });

        return isIOS && isSafari && !isStandalone;
    }

    function createAddToHomeScreenPrompt() {
        const promptHTML = `
            <div id="add-to-home-prompt" class="add-to-home-overlay">
                <div class="add-to-home-content">
                    <div class="add-to-home-header">
                        <h3>🎮 Get the Best Gaming Experience!</h3>
                        <button class="close-prompt" id="close-add-to-home">&times;</button>
                    </div>
                    <div class="add-to-home-body">
                        <div class="instruction-step">
                            <div class="step-icon">📱</div>
                            <div class="step-text">
                                <p><strong>Add to Home Screen for Full Screen Gaming!</strong></p>
                                <p>Enjoy our games in full screen mode without browser bars for the ultimate gaming experience.</p>
                            </div>
                        </div>
                        <div class="instruction-steps">
                            <div class="step">
                                <span class="step-number">1</span>
                                <span class="step-desc">Tap the Share button <span class="share-icon">📤</span> at the bottom of Safari</span>
                            </div>
                            <div class="step">
                                <span class="step-number">2</span>
                                <span class="step-desc">Scroll down and tap "Add to Home Screen" <span class="home-icon">📱</span></span>
                            </div>
                            <div class="step">
                                <span class="step-number">3</span>
                                <span class="step-desc">Tap "Add" to confirm and create the shortcut</span>
                            </div>
                        </div>
                        <div class="benefits">
                            <p><strong>Why Add to Home Screen?</strong></p>
                            <ul>
                                <li>✨ Full screen immersive gaming experience</li>
                                <li>🚀 Faster loading and better performance</li>
                                <li>🎯 No browser distractions or address bar</li>
                                <li>📱 Quick access directly from your home screen</li>
                                <li>🔋 Better battery optimization</li>
                            </ul>
                        </div>
                    </div>
                    <div class="add-to-home-footer">
                        <button class="continue-anyway-btn" id="continue-anyway">Continue in Browser</button>
                        <button class="remind-later-btn" id="remind-later">Remind Me Later</button>
                    </div>
                </div>
            </div>
        `;
        
        return promptHTML;
    }

    function showAddToHomeScreenPrompt() {
        const promptHTML = createAddToHomeScreenPrompt();
        document.body.insertAdjacentHTML('beforeend', promptHTML);
        
        const promptElement = document.getElementById('add-to-home-prompt');
        const closeBtn = document.getElementById('close-add-to-home');
        const continueBtn = document.getElementById('continue-anyway');
        const remindBtn = document.getElementById('remind-later');
        
        function closePrompt() {
            promptElement.style.opacity = '0';
            setTimeout(() => {
                if (promptElement && promptElement.parentNode) {
                    promptElement.parentNode.removeChild(promptElement);
                }
            }, 300);
            localStorage.setItem('addToHomePromptDismissed', 'true');
        }
        
        function remindLater() {
            promptElement.style.opacity = '0';
            setTimeout(() => {
                if (promptElement && promptElement.parentNode) {
                    promptElement.parentNode.removeChild(promptElement);
                }
            }, 300);
            const remindTime = new Date().getTime() + (24 * 60 * 60 * 1000);
            localStorage.setItem('addToHomeRemindTime', remindTime.toString());
        }
        
        if (closeBtn) closeBtn.addEventListener('click', closePrompt);
        if (continueBtn) continueBtn.addEventListener('click', closePrompt);
        if (remindBtn) remindBtn.addEventListener('click', remindLater);
        
        promptElement.style.display = 'flex';
        setTimeout(() => {
            promptElement.style.opacity = '1';
        }, 100);
    }

    function checkAndShowPrompt() {
        console.log('Checking if should show iOS prompt...');

        if (!detectIOSSafari()) {
            console.log('Not iOS Safari, skipping prompt');
            return;
        }

        const dismissed = localStorage.getItem('addToHomePromptDismissed');
        const remindTime = localStorage.getItem('addToHomeRemindTime');
        const currentTime = new Date().getTime();

        console.log('Prompt status:', {
            dismissed: dismissed,
            remindTime: remindTime,
            currentTime: currentTime,
            shouldRemind: !remindTime || currentTime >= parseInt(remindTime)
        });

        if (dismissed === 'true') {
            console.log('Prompt was dismissed, not showing');
            return;
        }

        if (remindTime && currentTime < parseInt(remindTime)) {
            console.log('Still in remind later period, not showing');
            return;
        }

        console.log('Will show prompt in 2 seconds...');
        setTimeout(showAddToHomeScreenPrompt, 2000);
    }

    function addPromptStyles() {
        if (document.getElementById('ios-safari-prompt-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'ios-safari-prompt-styles';
        style.textContent = `
            .add-to-home-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.85);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 10001;
                padding: 20px;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .add-to-home-content {
                background: linear-gradient(135deg, #1a4b3a 0%, #2d5a4a 100%);
                border-radius: 20px;
                max-width: 400px;
                width: 100%;
                max-height: calc(100vh - 40px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                border: 2px solid #ffd700;
                overflow: hidden;
                animation: slideInUp 0.4s ease-out;
                display: flex;
                flex-direction: column;
            }

            .add-to-home-header {
                background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
                color: #1a4b3a;
                padding: 20px;
                text-align: center;
                position: relative;
                flex-shrink: 0;
            }

            .add-to-home-header h3 {
                font-size: 20px;
                font-weight: 700;
                margin: 0;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            .close-prompt {
                position: absolute;
                top: 15px;
                right: 15px;
                background: none;
                border: none;
                font-size: 24px;
                color: #1a4b3a;
                cursor: pointer;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background-color 0.2s;
            }

            .close-prompt:hover {
                background-color: rgba(26, 75, 58, 0.1);
            }

            .add-to-home-body {
                padding: 25px;
                color: white;
                overflow-y: auto;
                flex: 1;
            }

            .instruction-step {
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                padding: 15px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                border-left: 4px solid #ffd700;
            }

            .step-icon {
                font-size: 30px;
                margin-right: 15px;
                flex-shrink: 0;
            }

            .step-text p {
                margin: 0 0 8px 0;
                line-height: 1.4;
            }

            .step-text p:last-child {
                margin-bottom: 0;
                opacity: 0.9;
                font-size: 14px;
            }

            .instruction-steps {
                margin: 20px 0;
            }

            .step {
                display: flex;
                align-items: center;
                margin-bottom: 12px;
                padding: 10px 0;
            }

            .step-number {
                background: #ffd700;
                color: #1a4b3a;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 12px;
                margin-right: 12px;
                flex-shrink: 0;
            }

            .step-desc {
                font-size: 14px;
                line-height: 1.4;
            }

            .share-icon, .home-icon {
                background: rgba(255, 215, 0, 0.2);
                padding: 2px 6px;
                border-radius: 4px;
                font-weight: bold;
            }

            .benefits {
                margin-top: 20px;
                padding: 15px;
                background: rgba(255, 215, 0, 0.1);
                border-radius: 10px;
                border: 1px solid rgba(255, 215, 0, 0.3);
            }

            .benefits p {
                margin: 0 0 10px 0;
                font-weight: 600;
                color: #ffd700;
            }

            .benefits ul {
                margin: 0;
                padding-left: 20px;
            }

            .benefits li {
                margin-bottom: 6px;
                font-size: 13px;
                line-height: 1.4;
            }

            .add-to-home-footer {
                padding: 20px 25px;
                background: rgba(0, 0, 0, 0.2);
                display: flex;
                gap: 10px;
                flex-direction: column;
                flex-shrink: 0;
            }

            .continue-anyway-btn, .remind-later-btn {
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s;
                text-align: center;
            }

            .continue-anyway-btn {
                background: #ffd700;
                color: #1a4b3a;
            }

            .continue-anyway-btn:hover {
                background: #ffed4e;
                transform: translateY(-1px);
            }

            .remind-later-btn {
                background: transparent;
                color: #ffd700;
                border: 2px solid #ffd700;
            }

            .remind-later-btn:hover {
                background: #ffd700;
                color: #1a4b3a;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px) scale(0.95);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            /* Landscape mode optimization */
            @media (orientation: landscape) and (max-height: 600px) {
                .add-to-home-content {
                    max-width: 500px;
                    max-height: calc(100vh - 20px);
                }

                .add-to-home-header {
                    padding: 15px 20px;
                }

                .add-to-home-header h3 {
                    font-size: 18px;
                }

                .add-to-home-body {
                    padding: 20px 25px;
                }

                .instruction-step {
                    padding: 12px;
                    margin-bottom: 15px;
                }

                .step-icon {
                    font-size: 24px;
                }

                .step-text p {
                    font-size: 14px;
                    margin-bottom: 6px;
                }

                .instruction-steps {
                    margin: 15px 0;
                }

                .step {
                    margin-bottom: 8px;
                    padding: 8px 0;
                }

                .step-desc {
                    font-size: 13px;
                }

                .benefits {
                    margin-top: 15px;
                    padding: 12px;
                }

                .benefits li {
                    margin-bottom: 4px;
                    font-size: 12px;
                }

                .add-to-home-footer {
                    padding: 15px 25px;
                }

                .continue-anyway-btn, .remind-later-btn {
                    padding: 10px 18px;
                    font-size: 13px;
                }
            }

            @media (max-width: 480px) {
                .add-to-home-content {
                    margin: 10px;
                    max-width: none;
                    max-height: calc(100vh - 20px);
                }

                .add-to-home-header h3 {
                    font-size: 18px;
                }

                .add-to-home-body {
                    padding: 20px;
                }

                .step-text p {
                    font-size: 14px;
                }

                .benefits li {
                    font-size: 12px;
                }

                .add-to-home-footer {
                    padding: 15px 20px;
                }
            }

            /* Very small screen optimization */
            @media (max-height: 500px) {
                .add-to-home-content {
                    max-height: calc(100vh - 10px);
                }

                .add-to-home-header {
                    padding: 12px 20px;
                }

                .add-to-home-header h3 {
                    font-size: 16px;
                }

                .add-to-home-body {
                    padding: 15px 20px;
                }

                .instruction-step {
                    padding: 10px;
                    margin-bottom: 12px;
                }

                .step-icon {
                    font-size: 20px;
                    margin-right: 12px;
                }

                .step-text p {
                    font-size: 13px;
                    margin-bottom: 4px;
                }

                .instruction-steps {
                    margin: 12px 0;
                }

                .step {
                    margin-bottom: 6px;
                    padding: 6px 0;
                }

                .step-desc {
                    font-size: 12px;
                }

                .benefits {
                    margin-top: 12px;
                    padding: 10px;
                }

                .benefits p {
                    margin-bottom: 8px;
                    font-size: 13px;
                }

                .benefits li {
                    margin-bottom: 3px;
                    font-size: 11px;
                }

                .add-to-home-footer {
                    padding: 12px 20px;
                }

                .continue-anyway-btn, .remind-later-btn {
                    padding: 8px 16px;
                    font-size: 12px;
                }
            }
        `;
        
        document.head.appendChild(style);
    }

    function init() {
        addPromptStyles();
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', checkAndShowPrompt);
        } else {
            checkAndShowPrompt();
        }
    }

    window.IOSSafariDetector = {
        init: init,
        detectIOSSafari: detectIOSSafari,
        showPrompt: showAddToHomeScreenPrompt,
        checkAndShowPrompt: checkAndShowPrompt
    };

    init();
})();
