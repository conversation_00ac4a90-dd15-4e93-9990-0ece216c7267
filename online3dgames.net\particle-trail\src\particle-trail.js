// Particle Trail Game Logic
class ParticleTrail {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        
        // Game state
        this.gameStarted = false;
        this.isPaused = false;
        
        // Particle settings
        this.maxParticles = 50;
        this.particleSize = 3;
        this.trailLength = 0.95; // Alpha decay rate
        
        // Particles array
        this.particles = [];
        
        // Mouse tracking
        this.mouseX = 0;
        this.mouseY = 0;
        this.lastMouseX = 0;
        this.lastMouseY = 0;
        
        // Effect modes
        this.currentMode = 'rainbow';
        this.modes = {
            fireworks: { gravity: 0.1, spread: 5, speed: 8 },
            galaxy: { gravity: 0.02, spread: 2, speed: 3 },
            rainbow: { gravity: 0.05, spread: 3, speed: 5 },
            neon: { gravity: 0.03, spread: 4, speed: 6 }
        };
        
        this.setupEventListeners();
        this.animate();
    }
    
    setupEventListeners() {
        // Mouse events
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseenter', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseleave', () => this.handleMouseLeave());
        
        // Touch events
        this.canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            this.handleTouchMove(e.touches[0]);
        });
        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.handleTouchMove(e.touches[0]);
        });
        
        // Settings controls
        document.getElementById('particle-count').addEventListener('input', (e) => {
            this.maxParticles = parseInt(e.target.value);
            document.getElementById('count-display').textContent = e.target.value;
        });
        
        document.getElementById('particle-size').addEventListener('input', (e) => {
            this.particleSize = parseInt(e.target.value);
            document.getElementById('size-display').textContent = e.target.value + 'px';
        });
        
        document.getElementById('trail-length').addEventListener('input', (e) => {
            this.trailLength = parseFloat(e.target.value);
            document.getElementById('trail-display').textContent = Math.round(e.target.value * 100) + '%';
        });
        
        // Mode buttons
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setMode(e.target.id.replace('-mode', ''));
                this.updateModeButtons();
            });
        });
        
        // Action buttons
        document.getElementById('pause-btn').addEventListener('click', () => this.togglePause());
        document.getElementById('clear-btn').addEventListener('click', () => this.clearParticles());
        document.getElementById('screenshot-btn').addEventListener('click', () => this.takeScreenshot());
    }
    
    handleMouseMove(e) {
        if (!this.gameStarted) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;
        
        this.lastMouseX = this.mouseX;
        this.lastMouseY = this.mouseY;
        this.mouseX = (e.clientX - rect.left) * scaleX;
        this.mouseY = (e.clientY - rect.top) * scaleY;
        
        this.createParticles();
    }
    
    handleTouchMove(touch) {
        if (!this.gameStarted) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;
        
        this.lastMouseX = this.mouseX;
        this.lastMouseY = this.mouseY;
        this.mouseX = (touch.clientX - rect.left) * scaleX;
        this.mouseY = (touch.clientY - rect.top) * scaleY;
        
        this.createParticles();
    }
    
    handleMouseLeave() {
        // Continue particles but stop creating new ones
    }
    
    createParticles() {
        if (this.particles.length >= this.maxParticles) return;
        
        const mode = this.modes[this.currentMode];
        const velocity = Math.sqrt(
            Math.pow(this.mouseX - this.lastMouseX, 2) + 
            Math.pow(this.mouseY - this.lastMouseY, 2)
        ) * 0.1;
        
        for (let i = 0; i < Math.min(3, this.maxParticles - this.particles.length); i++) {
            this.particles.push({
                x: this.mouseX + (Math.random() - 0.5) * mode.spread,
                y: this.mouseY + (Math.random() - 0.5) * mode.spread,
                vx: (Math.random() - 0.5) * mode.speed + velocity * 0.1,
                vy: (Math.random() - 0.5) * mode.speed + velocity * 0.1,
                life: 1,
                decay: 0.01 + Math.random() * 0.02,
                size: this.particleSize + Math.random() * 2,
                color: this.getParticleColor(),
                trail: []
            });
        }
    }
    
    getParticleColor() {
        switch (this.currentMode) {
            case 'fireworks':
                const fireworkColors = ['#ff6b6b', '#ffd93d', '#6bcf7f', '#4ecdc4', '#45b7d1'];
                return fireworkColors[Math.floor(Math.random() * fireworkColors.length)];
            
            case 'galaxy':
                const galaxyColors = ['#9b59b6', '#3498db', '#e74c3c', '#f39c12'];
                return galaxyColors[Math.floor(Math.random() * galaxyColors.length)];
            
            case 'rainbow':
                const hue = (Date.now() * 0.1 + Math.random() * 60) % 360;
                return `hsl(${hue}, 70%, 60%)`;
            
            case 'neon':
                const neonColors = ['#ff0080', '#00ff80', '#8000ff', '#ff8000', '#0080ff'];
                return neonColors[Math.floor(Math.random() * neonColors.length)];
            
            default:
                return '#ffffff';
        }
    }
    
    updateParticles() {
        if (this.isPaused) return;
        
        const mode = this.modes[this.currentMode];
        
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            
            // Update position
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            // Apply gravity
            particle.vy += mode.gravity;
            
            // Apply air resistance
            particle.vx *= 0.99;
            particle.vy *= 0.99;
            
            // Update life
            particle.life -= particle.decay;
            
            // Add to trail
            particle.trail.push({ x: particle.x, y: particle.y, life: particle.life });
            if (particle.trail.length > 10) {
                particle.trail.shift();
            }
            
            // Remove dead particles
            if (particle.life <= 0) {
                this.particles.splice(i, 1);
            }
        }
    }
    
    draw() {
        // Apply trail effect
        this.ctx.fillStyle = `rgba(0, 0, 0, ${1 - this.trailLength})`;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw particles
        for (const particle of this.particles) {
            this.drawParticle(particle);
        }
    }
    
    drawParticle(particle) {
        // Draw trail
        if (particle.trail.length > 1) {
            this.ctx.strokeStyle = particle.color;
            this.ctx.lineWidth = 1;
            this.ctx.globalAlpha = particle.life * 0.3;
            
            this.ctx.beginPath();
            this.ctx.moveTo(particle.trail[0].x, particle.trail[0].y);
            for (let i = 1; i < particle.trail.length; i++) {
                this.ctx.lineTo(particle.trail[i].x, particle.trail[i].y);
            }
            this.ctx.stroke();
        }
        
        // Draw particle
        this.ctx.globalAlpha = particle.life;
        this.ctx.fillStyle = particle.color;
        
        this.ctx.beginPath();
        this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Add glow effect for neon mode
        if (this.currentMode === 'neon') {
            this.ctx.shadowColor = particle.color;
            this.ctx.shadowBlur = 10;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size * 0.5, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.shadowBlur = 0;
        }
        
        this.ctx.globalAlpha = 1;
    }
    
    animate() {
        this.updateParticles();
        this.draw();
        requestAnimationFrame(() => this.animate());
    }
    
    setMode(mode) {
        this.currentMode = mode;
    }
    
    updateModeButtons() {
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(`${this.currentMode}-mode`).classList.add('active');
    }
    
    togglePause() {
        this.isPaused = !this.isPaused;
        const btn = document.getElementById('pause-btn');
        btn.textContent = this.isPaused ? '▶️ Continue' : '⏸️ Pause';
    }
    
    clearParticles() {
        this.particles = [];
        this.ctx.fillStyle = 'rgba(0, 0, 0, 1)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    takeScreenshot() {
        const link = document.createElement('a');
        link.download = `Particle Art_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`;
        link.href = this.canvas.toDataURL();
        link.click();
    }
    
    start() {
        this.gameStarted = true;
        // Hide cursor hint
        document.querySelector('.cursor-hint').style.display = 'none';
    }
}
