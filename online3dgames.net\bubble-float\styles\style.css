/* Bubble Float Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 100vh;
    color: white;
    overflow-x: hidden;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px 25px;
    border-radius: 15px;
    
}

.header h1 {
    font-size: 2.2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin: 0;
}

.home-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.home-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Control Panel */
.control-panel {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 20px;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
    align-items: center;
}

.bubble-settings {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.setting-group label {
    font-size: 0.9rem;
    font-weight: bold;
    opacity: 0.9;
}

.setting-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.3);
    outline: none;
    cursor: pointer;
}

.setting-group span {
    font-size: 0.8rem;
    opacity: 0.8;
    text-align: center;
}

.effect-modes {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.mode-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 20px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid transparent;
}

.mode-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.mode-btn.active {
    background: rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.action-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.control-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 20px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.control-btn.primary {
    background: linear-gradient(135deg, #00b894, #00a085);
    color: white;
}

.control-btn.secondary {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
    color: white;
}

.control-btn.success {
    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Bubble Container */
.bubble-container {
    position: relative;
    height: 500px;
    background: linear-gradient(180deg,
        rgba(116, 185, 255, 0.3) 0%,
        rgba(9, 132, 227, 0.5) 50%,
        rgba(0, 123, 255, 0.7) 100%);
    border-radius: 20px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Fullscreen styles */
.bubble-container:fullscreen {
    height: 100vh;
    width: 100vw;
    border-radius: 0;
    margin: 0;
    background: linear-gradient(180deg,
        rgba(116, 185, 255, 0.4) 0%,
        rgba(9, 132, 227, 0.6) 50%,
        rgba(0, 123, 255, 0.8) 100%);
}

.bubble-container:-webkit-full-screen {
    height: 100vh;
    width: 100vw;
    border-radius: 0;
    margin: 0;
    background: linear-gradient(180deg,
        rgba(116, 185, 255, 0.4) 0%,
        rgba(9, 132, 227, 0.6) 50%,
        rgba(0, 123, 255, 0.8) 100%);
}

.bubble-container:-moz-full-screen {
    height: 100vh;
    width: 100vw;
    border-radius: 0;
    margin: 0;
    background: linear-gradient(180deg,
        rgba(116, 185, 255, 0.4) 0%,
        rgba(9, 132, 227, 0.6) 50%,
        rgba(0, 123, 255, 0.8) 100%);
}

/* Hide other elements when in fullscreen */
body:fullscreen .header,
body:fullscreen .control-panel,
body:fullscreen .instructions {
    display: none;
}

body:-webkit-full-screen .header,
body:-webkit-full-screen .control-panel,
body:-webkit-full-screen .instructions {
    display: none;
}

body:-moz-full-screen .header,
body:-moz-full-screen .control-panel,
body:-moz-full-screen .instructions {
    display: none;
}

#bubble-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.interaction-hint {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.2);
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    opacity: 0.8;
    pointer-events: none;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.8;
        transform: translateX(-50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translateX(-50%) scale(1.05);
    }
}

/* Game Overlays */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20px;
    
    z-index: 1000;
}

.game-overlay.hidden {
    display: none;
}

.game-start-content {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: scale(0.8);
    animation: modalAppear 0.3s ease forwards;
    min-width: 300px;
}

@keyframes modalAppear {
    to {
        transform: scale(1);
    }
}

.game-start-content h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.game-start-content p {
    font-size: 1.1rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.game-start-content .tip {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 25px;
}

/* Bubble Animations - Optimized for 60fps */
.bubble {
    animation: float-up linear infinite;
    opacity: 0.7;
    will-change: transform, opacity;
    transform: translateZ(0); /* Force hardware acceleration */
    backface-visibility: hidden;
}

@keyframes float-up {
    from {
        transform: translate3d(0, 0, 0);
        opacity: 0.7;
    }
    to {
        transform: translate3d(var(--drift, 0px), -100vh, 0);
        opacity: 0;
    }
}

.bubble.colorful {
    animation: float-up linear infinite, color-shift 2s ease-in-out infinite;
    will-change: transform, filter;
}

@keyframes color-shift {
    0%, 100% { filter: hue-rotate(0deg) saturate(1.3) brightness(1.1); }
    50% { filter: hue-rotate(180deg) saturate(1.3) brightness(1.1); }
}

.bubble.neon {
    filter: drop-shadow(0 0 6px currentColor) brightness(1.2);
    animation: float-up linear infinite, neon-glow 1.5s ease-in-out infinite alternate;
    will-change: transform, filter;
}

@keyframes neon-glow {
    from { filter: drop-shadow(0 0 6px currentColor) brightness(1.2); }
    to { filter: drop-shadow(0 0 12px currentColor) brightness(1.4); }
}

.bubble.gentle {
    opacity: 0.3;
    filter: blur(1px);
    animation: float-up linear infinite, gentle-fade 3s ease-in-out infinite;
    will-change: transform, opacity;
}

@keyframes gentle-fade {
    0%, 100% { opacity: 0.2; }
    50% { opacity: 0.4; }
}

/* Instructions */
.instructions {
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 15px;
    
}

.instructions h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.instruction-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
}

.instruction-item .icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.instruction-item .desc {
    font-size: 0.9rem;
    line-height: 1.4;
}

.instruction-item .desc strong {
    display: block;
    margin-bottom: 5px;
}

.tips {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #74b9ff;
}

.tips h4 {
    margin-bottom: 15px;
    color: #74b9ff;
    font-size: 1.2rem;
}

.tips ul {
    list-style: none;
    padding: 0;
}

.tips li {
    padding: 5px 0;
    padding-left: 20px;
    position: relative;
    opacity: 0.9;
}

.tips li:before {
    content: "🫧";
    position: absolute;
    left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
    }
    
    .header h1 {
        font-size: 1.8rem;
    }
    
    .control-panel {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .bubble-settings {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    
    .setting-group {
        flex: 1;
        min-width: 120px;
        margin: 0 5px;
    }
    
    .effect-modes {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .action-controls {
        flex-direction: row;
        justify-content: center;
    }
    
    .bubble-container {
        height: 60vh;
        min-height: 350px;
        max-height: 500px;
    }
    
    .instruction-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .mode-btn, .control-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
    
    .bubble-settings, .effect-modes, .action-controls {
        flex-direction: column;
    }
    
    .bubble-container {
        height: 55vh;
        min-height: 300px;
        max-height: 400px;
    }
}
