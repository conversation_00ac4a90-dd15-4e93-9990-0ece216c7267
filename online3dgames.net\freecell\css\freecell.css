/* FreeCell Specific Styles */

/* Disabled card styles */
.card-disabled {
    filter: grayscale(0.5) brightness(0.8) !important;
    cursor: not-allowed !important;
}

.freecell-board {
    display: grid;
    grid-template-areas:
        "top-area"
        "tableau";
    grid-template-rows: auto 1fr;
    gap: 20px;
    margin-bottom: 30px;
    position: relative;
    min-height: 100vh;
}

/* Top Area: Free Cells and Foundation Piles */
.top-area {
    grid-area: top-area;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
    padding: 10px 0;
}

.freecells-area {
    display: flex;
    gap: 15px;
    justify-content: flex-start;
}

.freecell-pile {
    width: 130px;
    height: 180px;
    border: 2px dashed rgba(255,255,255,0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.1);
    position: relative;
    transition: all 0.2s ease;
    padding: 5px;
    box-sizing: border-box;
}



.foundation-area {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.foundation-pile {
    width: 130px;
    height: 180px;
    border: 2px dashed rgba(255,255,255,0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.1);
    position: relative;
    transition: all 0.2s ease;
    padding: 5px;
    box-sizing: border-box;
}



/* Tableau Area */
.freecell-tableau {
    grid-area: tableau;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
    padding-bottom: 50px;
}

.freecell-tableau .tableau-pile {
    width: 130px;
    min-width: 130px;
    height: 180px;
    border: 2px dashed rgba(255,255,255,0.2);
    border-radius: 8px;
    background: rgba(255,255,255,0.05);
    position: relative;
    transition: all 0.2s ease;
    padding: 5px;
    box-sizing: border-box;
}



/* Card Sequence Styles */
.card.sequence-valid {
    box-shadow: 0 0 0 2px #4CAF50, 0 4px 8px rgba(76, 175, 80, 0.4);
}

.card.sequence-invalid {
    box-shadow: 0 0 0 2px #f44336, 0 4px 8px rgba(244, 67, 54, 0.4);
}

.card.selected {
    box-shadow: 0 0 0 2px #ffeb3b, 0 4px 8px rgba(0,0,0,0.3);
    transform: translateY(-2px);
}

.card.multi-selected {
    box-shadow: 0 0 0 2px #4CAF50, 0 4px 8px rgba(0,0,0,0.3);
    transform: translateY(-1px);
}



/* Dragging Multi-card Sequences */
.card.dragging-multi {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 15px 30px rgba(0,0,0,0.7), 0 0 0 3px #ffeb3b;
    transition: none !important;
    filter: brightness(1.1);
}

.card.dragging-multi + .card.dragging-multi {
    z-index: 9998 !important;
    box-shadow: 0 12px 24px rgba(0,0,0,0.6), 0 0 0 2px #4CAF50;
    filter: brightness(1.05);
}

.card.dragging-multi + .card.dragging-multi + .card.dragging-multi {
    z-index: 9997 !important;
    box-shadow: 0 10px 20px rgba(0,0,0,0.5), 0 0 0 2px #2196F3;
    filter: brightness(1.02);
}

/* Auto-move animations */
.card.auto-moving {
    animation: autoMove 0.5s ease-in-out;
}

@keyframes autoMove {
    0% { transform: scale(1); }
    50% { transform: scale(1.1) rotate(5deg); }
    100% { transform: scale(1); }
}

/* Hint animations */
.card.hint-highlight {
    animation: hintPulse 1s ease-in-out infinite;
}

@keyframes hintPulse {
    0%, 100% {
        box-shadow: 0 0 0 2px transparent;
    }
    50% {
        box-shadow: 0 0 0 2px #ffeb3b, 0 0 10px rgba(255, 235, 59, 0.5);
    }
}

/* Enhanced mobile touch interactions */
@media (max-width: 768px) {
    .card {
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
        touch-action: manipulation;
    }

    .card.draggable-card {
        cursor: pointer;
    }

    .card.draggable-card:active {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 100;
    }

    .card.dragging {
        transform: scale(1.1) rotate(2deg);
        box-shadow: 0 8px 20px rgba(0,0,0,0.4);
        z-index: 1000;
        opacity: 0.9;
        transition: none;
    }

    .card.hint-card {
        animation: hintPulse 1.5s ease-in-out infinite;
        border: 2px solid #ffeb3b;
    }

    .card.touch-active {
        transform: scale(1.02);
        box-shadow: 0 2px 8px rgba(0,0,0,0.25);
        background-color: rgba(255, 255, 255, 0.95);
    }

    /* Improved pile hover states for mobile */
    .freecell-pile.valid-drop,
    .foundation-pile.valid-drop,
    .tableau-pile.valid-drop {
        background-color: rgba(76, 175, 80, 0.2);
        border: 2px solid #4caf50;
        transform: scale(1.02);
    }

    .freecell-pile.invalid-drop,
    .foundation-pile.invalid-drop,
    .tableau-pile.invalid-drop {
        background-color: rgba(244, 67, 54, 0.2);
        border: 2px solid #f44336;
        animation: shake 0.3s ease-in-out;
    }

    /* Shake animation for invalid drops */
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-3px); }
        75% { transform: translateX(3px); }
    }

    /* Pulse animation for hints */
    @keyframes hintPulse {
        0%, 100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(255, 235, 59, 0.7);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 0 0 8px rgba(255, 235, 59, 0);
        }
    }
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .freecell-tableau {
        grid-template-columns: repeat(8, minmax(80px, 1fr));
        gap: 8px;
    }

    .freecell-tableau .tableau-pile {
        min-width: 80px;
        height: calc(100vh - 200px);
        max-width: 100px;
    }

    .freecells-area,
    .foundation-area {
        gap: 8px;
    }

    .freecell-pile,
    .foundation-pile {
        width: 80px;
        height: 110px;
    }
}

@media (max-width: 768px) {
    .freecell-board {
        gap: 4px;
        min-height: 100vh;
        min-height: 100dvh;
    }

    .top-area {
        flex-direction: column;
        gap: 6px;
        align-items: center;
        padding: 2px 0;
    }

    .freecells-area,
    .foundation-area {
        justify-content: center;
        gap: 4px;
        width: 100%;
    }

    .freecell-pile,
    .foundation-pile {
        width: calc((100vw - 32px) / 4 - 4px);
        height: calc((100vw - 32px) / 4 * 1.35);
        max-width: 85px;
        max-height: 115px;
        min-width: 65px;
        min-height: 88px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    /* Enhanced touch feedback for piles */
    .freecell-pile:active,
    .foundation-pile:active {
        transform: scale(0.95);
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    }

    .freecell-tableau {
        grid-template-columns: repeat(8, 1fr);
        gap: 2px;
        padding: 0 4px;
        margin-top: 0;
    }

    .freecell-tableau .tableau-pile {
        width: calc((100vw - 20px) / 8 - 2px);
        min-width: calc((100vw - 20px) / 8 - 2px);
        height: calc(100vh - 180px);
        height: calc(100dvh - 180px);
        max-width: 85px;
        min-height: 140px;
        border-radius: 4px;
    }

    /* Improved card spacing in tableau */
    .freecell-tableau .tableau-pile .card {
        margin-bottom: 20px;
    }

    .freecell-tableau .tableau-pile .card:last-child {
        margin-bottom: 0;
    }
}

@media screen and (max-width: 768px) and (orientation: landscape) {
    .freecell-board {
        gap: 6px;
    }

    .top-area {
        flex-direction: row;
        gap: 8px;
        justify-content: space-between;
        padding: 2px 6px;
    }

    .freecells-area,
    .foundation-area {
        gap: 3px;
        flex: 1;
        max-width: none;
    }

    .freecell-pile,
    .foundation-pile {
        width: calc((100vw - 80px) / 8 - 3px);
        height: calc((100vw - 80px) / 8 * 1.35);
        max-width: 70px;
        max-height: 95px;
        min-width: 50px;
        min-height: 68px;
    }

    .freecell-tableau {
        margin-top: 0;
        gap: 1px;
        padding: 0 2px;
    }

    .freecell-tableau .tableau-pile {
        width: calc((100vw - 16px) / 8 - 1px);
        min-width: calc((100vw - 16px) / 8 - 1px);
        height: calc(100vh - 140px);
        height: calc(100dvh - 140px);
        max-width: 70px;
        min-height: 100px;
        width: calc((100vw - 40px) / 8 - 2px);
        height: calc(100vh - 160px);
        max-width: 55px;
        min-height: 100px;
    }
}

/* Game Messages and Help Panel - inherit from base styles */
.hidden {
    display: none !important;
}

/* Additional FreeCell specific animations */
.card.hint-highlight {
    animation: freecellHint 2s ease-in-out infinite;
    border: 3px solid #ffeb3b !important;
    box-shadow: 0 0 20px rgba(255, 235, 59, 0.8) !important;
}

@keyframes freecellHint {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(255, 235, 59, 0.8);
    }
    50% {
        transform: scale(1.08);
        box-shadow: 0 0 30px rgba(255, 235, 59, 1);
    }
}



/* Card sequence highlighting */
.card.sequence-highlight {
    border: 2px solid #2196F3 !important;
    box-shadow: 0 0 10px rgba(33, 150, 243, 0.6) !important;
}

/* Foundation pile specific styling */
.foundation-pile[data-suit="hearts"] .pile-placeholder {
    color: #dc143c;
    font-size: 3em;
}

.foundation-pile[data-suit="diamonds"] .pile-placeholder {
    color: #dc143c;
    font-size: 3em;
}

.foundation-pile[data-suit="clubs"] .pile-placeholder {
    color: #000;
    font-size: 3em;
}

.foundation-pile[data-suit="spades"] .pile-placeholder {
    color: #000;
    font-size: 3em;
}

/* Enhanced mobile touch feedback */
@media (max-width: 768px) {
    .card.draggable-card:active {
        transition: transform 0.1s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.4);
    }

    .freecell-pile:active,
    .foundation-pile:active,
    .tableau-pile:active {
        background: rgba(255, 255, 255, 0.2) !important;
        transition: all 0.1s ease;
    }
}

/* Improved card stacking in tableau */
.freecell-tableau .tableau-pile .card {
    transition: all 0.3s ease;
}

.freecell-tableau .tableau-pile .card:hover {
    z-index: 100;
}

/* Card returning animation (like spider solitaire) */
.card.card-returning {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    z-index: 9998 !important;
}



/* Card flip animation for auto-complete */
.card.auto-flip {
    animation: cardFlip 0.6s ease-in-out;
}

@keyframes cardFlip {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(90deg); }
    100% { transform: rotateY(0deg); }
}

/* Win celebration animation */
.card.celebration {
    animation: celebrate 2s ease-in-out;
}

@keyframes celebrate {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(5deg); }
    50% { transform: scale(1.2) rotate(-5deg); }
    75% { transform: scale(1.1) rotate(3deg); }
}

/* Game Messages */
.game-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.message-content {
    background: linear-gradient(135deg, #1a3d0a, #2d5016, #4a7c59);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 450px;
    border: 3px solid #7fb069;
    box-shadow:
        0 0 0 1px rgba(255,255,255,0.1),
        0 10px 30px rgba(0,0,0,0.7),
        inset 0 1px 0 rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
}

.message-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #7fb069, transparent);
}

.message-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #7fb069, transparent);
}

.message-content h2 {
    margin-bottom: 20px;
    font-size: 2rem;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.message-content p {
    margin-bottom: 20px;
    font-size: 1.1rem;
    color: rgba(255,255,255,0.9);
}

.message-stats {
    margin: 20px 0;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 10px;
}

.message-stats div {
    background: rgba(255,255,255,0.1);
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid rgba(255,255,255,0.2);
    color: #fff;
    font-weight: 600;
    min-width: 100px;
}

.message-stats span {
    color: #7fb069;
    font-weight: bold;
}

.message-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.message-buttons .btn {
    padding: 12px 24px;
    border-radius: 8px;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.message-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

/* Mobile responsive for game messages */
@media (max-width: 768px) {
    .message-content {
        padding: 15px;
        margin: 10px;
        max-width: 90vw;
    }

    .message-content h2 {
        font-size: 1.3rem;
    }

    .message-stats {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
    }

    .message-stats div {
        flex: 1;
        min-width: 120px;
        text-align: center;
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .message-buttons {
        flex-direction: row;
        gap: 10px;
    }

    .message-buttons .btn {
        flex: 1;
        padding: 10px 16px;
        font-size: 0.9rem;
    }
}

/* Optimize for devices with limited height */
@media (max-height: 500px) {
    .message-content {
        padding: 15px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .message-content h2 {
        font-size: 1.1rem;
        margin-bottom: 8px;
    }

    .message-content p,
    .message-content div {
        font-size: 14px;
        line-height: 1.3;
    }

    .message-stats {
        margin: 8px 0;
        gap: 6px;
    }

    .message-stats div {
        padding: 4px 8px;
        font-size: 11px;
    }


/* Landscape phones/tablets - always apply when landscape */
@media screen and (orientation: landscape) {
    .freecell-board {
        gap: 0;
        padding: 0 4px;
    }

    .top-area {
        padding: 4px 0;
        gap: 8px;
        margin-bottom: 2px;
    }

    .freecells-area, .foundation-area {
        gap: 4px;
    }

    .freecell-tableau {
        margin-top: 0;
        gap: 2px;
        padding: 0 2px;
    }

    /* Optimized piles for landscape */
    .freecell-pile, .foundation-pile {
        width: calc((100vw - 60px) / 8 - 6px);
        height: calc(((100vw - 60px) / 8 - 6px) * 1.35);
        max-width: 70px;
        max-height: 90px;
        min-width: 57px;
        min-height: 77px;
        padding: 3px;
        border-width: 1px;
        border-radius: 6px;
    }

    .freecell-tableau .tableau-pile {
        width: calc((100vw - 32px) / 8 - 2px);
        min-width: calc((100vw - 32px) / 8 - 2px);
        height: calc(100dvh - 120px);
        max-width: 87px;
        min-height: 200px;
        border-radius: 6px;
        padding: 3px;
        border-width: 1px;
    }

    .freecell-tableau .tableau-pile .card {
        margin-bottom: 20px;
    }

    .freecell-tableau .tableau-pile .card:last-child {
        margin-bottom: 0;
    }
}
    .message-buttons .btn {
        padding: 8px 16px;
        font-size: 0.8rem;
    }
}

/* Extra small mobile devices - FreeCell specific */
@media (max-width: 480px) {
    .freecell-board {
        gap: 4px;
    }

    .top-area {
        gap: 3px;
        padding: 1px 0;
    }

    .freecells-area,
    .foundation-area {
        gap: 2px;
    }

    .freecell-pile,
    .foundation-pile {
        width: calc((100vw - 32px) / 4 - 2px);
        height: calc((100vw - 32px) / 4 * 1.35);
        max-width: 75px;
        max-height: 101px;
        min-width: 55px;
        min-height: 74px;
        border-radius: 4px;
    }

    .freecell-tableau {
        gap: 1px;
        padding: 0 2px;
        margin-top: 0;
    }

    .freecell-tableau .tableau-pile {
        width: calc((100vw - 16px) / 8 - 1px);
        min-width: calc((100vw - 16px) / 8 - 1px);
        height: calc(100vh - 200px);
        height: calc(100dvh - 200px);
        max-width: 75px;
        min-height: 100px;
        border-radius: 3px;
    }

    .freecell-tableau .tableau-pile .card {
        margin-bottom: 20px;
    }
}

/* Very small screens - ultra compact */
@media (max-width: 360px) {
    .freecell-pile,
    .foundation-pile {
        width: calc((100vw - 24px) / 4 - 2px);
        height: calc((100vw - 24px) / 4 * 1.35);
        max-width: 70px;
        max-height: 95px;
        min-width: 50px;
        min-height: 68px;
    }

    .freecell-tableau .tableau-pile {
        width: calc((100vw - 12px) / 8 - 1px);
        min-width: calc((100vw - 12px) / 8 - 1px);
        max-width: 70px;
        min-height: 90px;
    }

    .freecell-tableau .tableau-pile .card {
        margin-bottom: 20px;
    }
}
