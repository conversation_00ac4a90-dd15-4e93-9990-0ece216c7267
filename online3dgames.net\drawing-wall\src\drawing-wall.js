// Drawing Wall Game Logic
class DrawingWall {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        
        // Drawing state
        this.isDrawing = false;
        this.gameStarted = false;
        
        // Brush settings
        this.brushSize = 5;
        this.opacity = 1;
        this.currentColor = '#FF6B6B';
        this.rainbowMode = false;
        this.rainbowHue = 0;
        
        // History for undo/redo
        this.history = [];
        this.historyStep = -1;
        this.maxHistory = 50;
        
        // Mouse/touch tracking
        this.lastX = 0;
        this.lastY = 0;
        
        this.setupEventListeners();
        this.setupCanvas();
        this.saveState();
    }
    
    setupCanvas() {
        // Set canvas background to white
        this.ctx.fillStyle = 'white';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Set initial drawing settings
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
        this.ctx.globalCompositeOperation = 'source-over';
    }
    
    setupEventListeners() {
        // Mouse events
        this.canvas.addEventListener('mousedown', (e) => this.startDrawing(e));
        this.canvas.addEventListener('mousemove', (e) => this.draw(e));
        this.canvas.addEventListener('mouseup', () => this.stopDrawing());
        this.canvas.addEventListener('mouseout', () => this.stopDrawing());
        
        // Touch events
        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.startDrawing(e.touches[0]);
        });
        this.canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            this.draw(e.touches[0]);
        });
        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.stopDrawing();
        });
        
        // UI controls
        document.getElementById('brush-size').addEventListener('input', (e) => {
            this.brushSize = e.target.value;
            document.getElementById('size-display').textContent = e.target.value + 'px';
        });
        
        document.getElementById('opacity').addEventListener('input', (e) => {
            this.opacity = e.target.value;
            document.getElementById('opacity-display').textContent = Math.round(e.target.value * 100) + '%';
        });
        
        document.getElementById('color-picker').addEventListener('change', (e) => {
            this.currentColor = e.target.value;
            this.rainbowMode = false;
            this.updateColorPresets();
        });
        
        document.getElementById('rainbow-mode').addEventListener('click', () => {
            this.rainbowMode = !this.rainbowMode;
            const btn = document.getElementById('rainbow-mode');
            btn.style.background = this.rainbowMode ? 
                'linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1)' : 
                'linear-gradient(135deg, #a8e6cf, #7fcdcd)';
        });
        
        // Color presets
        document.querySelectorAll('.color-preset').forEach(preset => {
            preset.addEventListener('click', (e) => {
                this.currentColor = e.target.dataset.color;
                this.rainbowMode = false;
                document.getElementById('color-picker').value = this.currentColor;
                this.updateColorPresets();
            });
        });
        
        // Action buttons
        document.getElementById('clear-btn').addEventListener('click', () => this.clearCanvas());
        document.getElementById('save-btn').addEventListener('click', () => this.saveImage());
        document.getElementById('undo-btn').addEventListener('click', () => this.undo());
        document.getElementById('redo-btn').addEventListener('click', () => this.redo());
    }
    
    getMousePos(e) {
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;
        
        return {
            x: (e.clientX - rect.left) * scaleX,
            y: (e.clientY - rect.top) * scaleY
        };
    }
    
    startDrawing(e) {
        if (!this.gameStarted) return;
        
        this.isDrawing = true;
        const pos = this.getMousePos(e);
        this.lastX = pos.x;
        this.lastY = pos.y;
    }
    
    draw(e) {
        if (!this.isDrawing || !this.gameStarted) return;
        
        const pos = this.getMousePos(e);
        
        this.ctx.globalAlpha = this.opacity;
        this.ctx.lineWidth = this.brushSize;
        
        // Set color
        if (this.rainbowMode) {
            this.rainbowHue = (this.rainbowHue + 2) % 360;
            this.ctx.strokeStyle = `hsl(${this.rainbowHue}, 70%, 60%)`;
        } else {
            this.ctx.strokeStyle = this.currentColor;
        }
        
        // Draw line
        this.ctx.beginPath();
        this.ctx.moveTo(this.lastX, this.lastY);
        this.ctx.lineTo(pos.x, pos.y);
        this.ctx.stroke();
        
        this.lastX = pos.x;
        this.lastY = pos.y;
    }
    
    stopDrawing() {
        if (this.isDrawing) {
            this.isDrawing = false;
            this.saveState();
        }
    }
    
    updateColorPresets() {
        document.querySelectorAll('.color-preset').forEach(preset => {
            preset.classList.remove('active');
            if (preset.dataset.color === this.currentColor) {
                preset.classList.add('active');
            }
        });
    }
    
    saveState() {
        this.historyStep++;
        if (this.historyStep < this.history.length) {
            this.history.length = this.historyStep;
        }
        
        this.history.push(this.canvas.toDataURL());
        
        if (this.history.length > this.maxHistory) {
            this.history.shift();
            this.historyStep--;
        }
        
        this.updateUndoRedoButtons();
    }
    
    undo() {
        if (this.historyStep > 0) {
            this.historyStep--;
            this.restoreState();
        }
    }
    
    redo() {
        if (this.historyStep < this.history.length - 1) {
            this.historyStep++;
            this.restoreState();
        }
    }
    
    restoreState() {
        const img = new Image();
        img.onload = () => {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.drawImage(img, 0, 0);
        };
        img.src = this.history[this.historyStep];
        this.updateUndoRedoButtons();
    }
    
    updateUndoRedoButtons() {
        document.getElementById('undo-btn').disabled = this.historyStep <= 0;
        document.getElementById('redo-btn').disabled = this.historyStep >= this.history.length - 1;
    }
    
    clearCanvas() {
        gameConfirm('Are you sure you want to clear the canvas?', 'Clear confirmation').then(result => {
            if (result) {
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                this.saveState();
                gameSuccess('Canvas cleared!', 'Cleared successfully');
            }
        });
    }
    
    saveImage() {
        const link = document.createElement('a');
        link.download = `Drawing_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`;
        link.href = this.canvas.toDataURL();
        link.click();
    }
    
    start() {
        this.gameStarted = true;
        this.canvas.style.cursor = 'crosshair';
    }
}
