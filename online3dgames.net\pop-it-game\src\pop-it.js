// Pop-it Game Logic
class PopItGame {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');

        // Detect mobile device
        this.isMobile = this.detectMobile();

        // Game settings - adjust for mobile
        if (this.isMobile) {
            this.rows = 6;
            this.cols = 4;
            this.bubbleSize = 60;  // Larger bubbles for mobile
            this.bubbleSpacing = 90;  // Consistent spacing for mobile
        } else {
            this.rows = 10;
            this.cols = 10;
            this.bubbleSize = 35;
            this.bubbleSpacing = 45;
        }
        this.gameStarted = false;

        // Game state
        this.bubbles = [];
        this.poppedCount = 0;
        this.totalBubbles = this.rows * this.cols;
        this.combo = 0;
        this.lastPopTime = 0;
        this.comboTimeout = null;

        // Audio
        this.soundEnabled = true;
        this.audioContext = null;
        this.initAudio();

        // Vibration
        this.vibrationEnabled = this.isMobile && 'vibrate' in navigator;

        // Colors
        this.colors = [
            '#FF6B9D', '#FF8E9B', '#A8E6CF', '#7FCDCD',
            '#FFD93D', '#6BCF7F', '#4ECDC4', '#45B7D1'
        ];

        this.adjustCanvasSize();
        this.initBubbles();
        this.setupEventListeners();
        this.draw();
    }
    
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (window.innerWidth <= 768) ||
               ('ontouchstart' in window);
    }

    adjustCanvasSize() {
        if (this.isMobile) {
            // Calculate ideal canvas size for mobile based on bubble grid
            const totalWidth = this.cols * this.bubbleSpacing + 60; // 60px padding
            const totalHeight = this.rows * this.bubbleSpacing + 60; // 60px padding

            // Get container size
            const container = this.canvas.parentElement;
            const containerRect = container.getBoundingClientRect();

            // Set canvas size to fit the bubble grid while respecting container limits
            const maxWidth = Math.min(containerRect.width, window.innerWidth - 20);
            const maxHeight = Math.min(containerRect.height, window.innerHeight * 0.7);

            this.canvas.width = Math.min(totalWidth, maxWidth);
            this.canvas.height = Math.min(totalHeight, maxHeight);

            // Update canvas style to maintain aspect ratio
            this.canvas.style.width = this.canvas.width + 'px';
            this.canvas.style.height = this.canvas.height + 'px';
        }
    }

    initAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Web Audio API not supported');
            this.soundEnabled = false;
        }
    }
    
    playPopSound() {
        if (!this.soundEnabled || !this.audioContext) return;

        // Create a more realistic pop sound with multiple oscillators
        const now = this.audioContext.currentTime;

        // Main pop sound
        const osc1 = this.audioContext.createOscillator();
        const gain1 = this.audioContext.createGain();
        const filter1 = this.audioContext.createBiquadFilter();

        osc1.connect(filter1);
        filter1.connect(gain1);
        gain1.connect(this.audioContext.destination);

        // Sharp attack with quick decay
        osc1.frequency.setValueAtTime(1200 + Math.random() * 800, now);
        osc1.frequency.exponentialRampToValueAtTime(100, now + 0.05);

        filter1.type = 'lowpass';
        filter1.frequency.setValueAtTime(2000, now);
        filter1.frequency.exponentialRampToValueAtTime(500, now + 0.05);

        gain1.gain.setValueAtTime(0.4, now);
        gain1.gain.exponentialRampToValueAtTime(0.001, now + 0.08);

        osc1.start(now);
        osc1.stop(now + 0.08);

        // Add a subtle click for crispness
        const osc2 = this.audioContext.createOscillator();
        const gain2 = this.audioContext.createGain();

        osc2.connect(gain2);
        gain2.connect(this.audioContext.destination);

        osc2.frequency.setValueAtTime(3000 + Math.random() * 2000, now);
        osc2.frequency.exponentialRampToValueAtTime(1000, now + 0.02);

        gain2.gain.setValueAtTime(0.2, now);
        gain2.gain.exponentialRampToValueAtTime(0.001, now + 0.02);

        osc2.start(now);
        osc2.stop(now + 0.02);
    }

    triggerVibration() {
        if (!this.vibrationEnabled) return;

        // Create different vibration patterns based on combo
        let pattern;
        if (this.combo <= 1) {
            // Single pop - short sharp vibration
            pattern = [0, 50];
        } else if (this.combo <= 5) {
            // Small combo - double tap
            pattern = [0, 30, 20, 30];
        } else if (this.combo <= 10) {
            // Medium combo - triple tap
            pattern = [0, 25, 15, 25, 15, 35];
        } else {
            // High combo - intense burst
            pattern = [0, 40, 10, 30, 10, 30, 10, 50];
        }

        // Trigger vibration
        try {
            navigator.vibrate(pattern);
        } catch (e) {
            console.log('Vibration not supported or failed');
            this.vibrationEnabled = false;
        }
    }

    initBubbles() {
        this.bubbles = [];
        this.poppedCount = 0;
        this.combo = 0;

        // Calculate grid layout - use consistent approach for both mobile and desktop
        const totalWidth = this.cols * this.bubbleSpacing;
        const totalHeight = this.rows * this.bubbleSpacing;

        const startX = (this.canvas.width - totalWidth) / 2 + this.bubbleSpacing / 2;
        const startY = (this.canvas.height - totalHeight) / 2 + this.bubbleSpacing / 2;

        for (let row = 0; row < this.rows; row++) {
            for (let col = 0; col < this.cols; col++) {
                this.bubbles.push({
                    x: startX + col * this.bubbleSpacing,
                    y: startY + row * this.bubbleSpacing,
                    popped: false,
                    color: this.colors[Math.floor(Math.random() * this.colors.length)],
                    animationTime: 0,
                    isAnimating: false
                });
            }
        }

        this.updateScore();
    }
    
    setupEventListeners() {
        // Mouse events for desktop
        this.canvas.addEventListener('click', (e) => {
            if (!this.gameStarted) return;
            this.handleClick(e);
        });

        // Touch events for mobile with improved handling
        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            if (!this.gameStarted) return;
            this.handleTouchStart(e);
        }, { passive: false });

        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            if (!this.gameStarted) return;
            this.handleTouchEnd(e);
        }, { passive: false });

        this.canvas.addEventListener('touchmove', (e) => {
            e.preventDefault(); // Prevent scrolling
        }, { passive: false });

        // Prevent context menu on long press
        this.canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });
    }
    
    handleClick(e) {
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;

        const x = (e.clientX - rect.left) * scaleX;
        const y = (e.clientY - rect.top) * scaleY;

        this.popBubbleAt(x, y);
    }

    handleTouchStart(e) {
        // Store touch start position and time for gesture detection
        this.touchStartTime = Date.now();
        this.touchStartPos = {
            x: e.touches[0].clientX,
            y: e.touches[0].clientY
        };
    }

    handleTouchEnd(e) {

        // Check if this was a tap (not a drag)
        const touchEndTime = Date.now();
        const touchDuration = touchEndTime - this.touchStartTime;

        if (touchDuration > 500) {
            // Long press - ignore to prevent accidental pops
            return;
        }

        // Check if touch moved too much (drag detection)
        if (e.changedTouches && e.changedTouches[0]) {
            const touchEndPos = {
                x: e.changedTouches[0].clientX,
                y: e.changedTouches[0].clientY
            };

            const distance = Math.sqrt(
                Math.pow(touchEndPos.x - this.touchStartPos.x, 2) +
                Math.pow(touchEndPos.y - this.touchStartPos.y, 2)
            );

            if (distance > 20) {
                // Too much movement - ignore
                return;
            }
        }

        // Process the touch as a bubble pop
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;

        const x = (this.touchStartPos.x - rect.left) * scaleX;
        const y = (this.touchStartPos.y - rect.top) * scaleY;

        this.popBubbleAt(x, y);
    }
    
    popBubbleAt(x, y) {
        for (let bubble of this.bubbles) {
            if (bubble.popped) continue;
            
            const distance = Math.sqrt(
                Math.pow(x - bubble.x, 2) + Math.pow(y - bubble.y, 2)
            );
            
            if (distance <= this.bubbleSize) {
                this.popBubble(bubble);
                break;
            }
        }
    }
    
    popBubble(bubble) {
        bubble.popped = true;
        bubble.isAnimating = true;
        bubble.animationTime = 0;

        this.poppedCount++;
        this.playPopSound();
        this.triggerVibration();

        // Combo system
        const currentTime = Date.now();
        if (currentTime - this.lastPopTime < 1000) {
            this.combo++;
        } else {
            this.combo = 1;
        }
        this.lastPopTime = currentTime;

        // Reset combo after 1 second
        if (this.comboTimeout) {
            clearTimeout(this.comboTimeout);
        }
        this.comboTimeout = setTimeout(() => {
            this.combo = 0;
            this.updateScore();
        }, 1000);

        this.updateScore();

        // Check if all bubbles are popped
        if (this.poppedCount === this.totalBubbles) {
            setTimeout(() => {
                this.showCompletionMessage();
            }, 500);
        }
    }
    
    showCompletionMessage() {
        // Create a simple celebration effect
        this.ctx.save();
        this.ctx.fillStyle = 'rgba(255, 107, 157, 0.8)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.ctx.fillStyle = 'white';
        this.ctx.font = 'bold 48px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('All popped!', this.canvas.width / 2, this.canvas.height / 2 - 20);
        
        this.ctx.font = 'bold 24px Arial';
        this.ctx.fillText('Click reset button to continue relieving', this.canvas.width / 2, this.canvas.height / 2 + 30);
        this.ctx.restore();
    }
    
    draw() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw bubbles
        for (let bubble of this.bubbles) {
            this.drawBubble(bubble);
        }
        
        // Update animations
        this.updateAnimations();
        
        requestAnimationFrame(() => this.draw());
    }
    
    drawBubble(bubble) {
        this.ctx.save();
        
        if (bubble.isAnimating) {
            bubble.animationTime += 0.05;
            if (bubble.animationTime >= 1) {
                bubble.isAnimating = false;
            }
            
            // Animation scale
            const scale = bubble.popped ? 
                1 - bubble.animationTime * 0.3 : 
                1 + Math.sin(bubble.animationTime * Math.PI) * 0.2;
            
            this.ctx.translate(bubble.x, bubble.y);
            this.ctx.scale(scale, scale);
            this.ctx.translate(-bubble.x, -bubble.y);
        }
        
        // Draw bubble shadow
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        this.ctx.beginPath();
        this.ctx.arc(bubble.x + 2, bubble.y + 2, this.bubbleSize, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Draw bubble
        if (bubble.popped) {
            // Popped bubble (flattened)
            this.ctx.fillStyle = bubble.color;
            this.ctx.globalAlpha = 0.3;
            this.ctx.beginPath();
            this.ctx.ellipse(bubble.x, bubble.y, this.bubbleSize, this.bubbleSize * 0.3, 0, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.globalAlpha = 1;
        } else {
            // Unpopped bubble (3D effect)
            const gradient = this.ctx.createRadialGradient(
                bubble.x - this.bubbleSize * 0.3, bubble.y - this.bubbleSize * 0.3, 0,
                bubble.x, bubble.y, this.bubbleSize
            );
            gradient.addColorStop(0, this.lightenColor(bubble.color, 40));
            gradient.addColorStop(0.7, bubble.color);
            gradient.addColorStop(1, this.darkenColor(bubble.color, 20));
            
            this.ctx.fillStyle = gradient;
            this.ctx.beginPath();
            this.ctx.arc(bubble.x, bubble.y, this.bubbleSize, 0, Math.PI * 2);
            this.ctx.fill();
            
            // Highlight
            this.ctx.fillStyle = 'rgba(255, 255, 255, 0.4)';
            this.ctx.beginPath();
            this.ctx.arc(bubble.x - this.bubbleSize * 0.3, bubble.y - this.bubbleSize * 0.3, this.bubbleSize * 0.3, 0, Math.PI * 2);
            this.ctx.fill();
        }
        
        this.ctx.restore();
    }
    
    updateAnimations() {
        // Update any ongoing animations
        for (let bubble of this.bubbles) {
            if (bubble.isAnimating) {
                // Animation is handled in drawBubble
            }
        }
    }
    
    lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
    
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
            (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
            (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }
    
    toggleSize() {
        if (this.bubbleSize === 35) {
            this.bubbleSize = 25;
            this.bubbleSpacing = 35;
            this.rows = 12;
            this.cols = 12;
        } else {
            this.bubbleSize = 35;
            this.bubbleSpacing = 45;
            this.rows = 10;
            this.cols = 10;
        }
        
        this.totalBubbles = this.rows * this.cols;
        this.initBubbles();
    }
    
    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        return this.soundEnabled;
    }
    
    reset() {
        this.initBubbles();
    }
    
    start() {
        this.gameStarted = true;
    }
    
    updateScore() {
        document.getElementById('popped-count').textContent = this.poppedCount;
        document.getElementById('total-bubbles').textContent = this.totalBubbles;
        document.getElementById('combo').textContent = this.combo;
    }
}
