/* Breathing Ball Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.2);
    padding: 20px;
    border-radius: 15px;
    
}

.header h1 {
    font-size: 2.5rem;
    color: #667eea;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    margin: 0;
}

.home-btn {
    background: rgba(255, 255, 255, 0.3);
    color: #667eea;
    border: 2px solid rgba(102, 126, 234, 0.3);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.home-btn:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Game Info Panel */
.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
    flex-wrap: wrap;
}

.score-panel {
    display: flex;
    gap: 25px;
    background: rgba(255, 255, 255, 0.2);
    padding: 20px;
    border-radius: 15px;
    
}

.score-item {
    text-align: center;
}

.score-item .label {
    display: block;
    font-size: 0.9rem;
    color: #667eea;
    font-weight: bold;
    margin-bottom: 5px;
}

.score-item .value {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
    color: #333;
}

.score-item .unit {
    display: block;
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 2px;
}

.controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.control-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.control-btn.secondary {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
    color: #333;
}

.control-btn.danger {
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Breathing Container */
.breathing-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 500px;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    
    overflow: hidden;
}

/* Game Overlays */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20px;
    
    z-index: 1000;
}

.game-overlay.hidden {
    display: none;
}

.game-start-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: scale(0.8);
    animation: modalAppear 0.3s ease forwards;
    min-width: 300px;
    color: white;
}

@keyframes modalAppear {
    to {
        transform: scale(1);
    }
}

.game-start-content h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.game-start-content p {
    font-size: 1.1rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.game-start-content .tip {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 25px;
}

/* Breathing Ball */
.breathing-ball {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 50px 0;
    animation: breathe 4s ease-in-out infinite;
}

.ball-inner {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(
        from 0deg,
        #ff6b6b,
        #4ecdc4,
        #45b7d1,
        #96ceb4,
        #ffeaa7,
        #dda0dd,
        #ff6b6b
    );
    animation: rotate 8s linear infinite;
    position: relative;
    overflow: hidden;
}

.ball-inner::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    width: 30%;
    height: 30%;
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
    border-radius: 50%;
    filter: blur(10px);
}

.ball-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    border-radius: 50%;
    background: conic-gradient(
        from 0deg,
        rgba(255,107,107,0.3),
        rgba(78,205,196,0.3),
        rgba(69,183,209,0.3),
        rgba(150,206,180,0.3),
        rgba(255,234,167,0.3),
        rgba(221,160,221,0.3),
        rgba(255,107,107,0.3)
    );
    animation: rotate 8s linear infinite reverse;
    filter: blur(20px);
    z-index: -1;
}

@keyframes breathe {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.3);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Breathing Text */
.breathing-text {
    text-align: center;
    margin: 20px 0;
}

#breath-instruction {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    animation: breatheText 4s ease-in-out infinite;
}

@keyframes breatheText {
    0%, 100% {
        opacity: 0.7;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

/* Breathing Guide */
.breathing-guide {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
}

.guide-text {
    display: flex;
    gap: 40px;
    font-size: 1.2rem;
    font-weight: bold;
}

.inhale-text {
    color: #4ecdc4;
    animation: inhaleGlow 4s ease-in-out infinite;
}

.exhale-text {
    color: #ff6b6b;
    animation: exhaleGlow 4s ease-in-out infinite;
}

@keyframes inhaleGlow {
    0%, 50%, 100% {
        opacity: 0.5;
        text-shadow: none;
    }
    25% {
        opacity: 1;
        text-shadow: 0 0 10px #4ecdc4;
    }
}

@keyframes exhaleGlow {
    0%, 25%, 50%, 100% {
        opacity: 0.5;
        text-shadow: none;
    }
    75% {
        opacity: 1;
        text-shadow: 0 0 10px #ff6b6b;
    }
}

/* Speed Variations */
.breathing-ball.slow {
    animation-duration: 6s;
}

.breathing-ball.fast {
    animation-duration: 2s;
}

.breathing-ball.slow #breath-instruction {
    animation-duration: 6s;
}

.breathing-ball.fast #breath-instruction {
    animation-duration: 2s;
}

/* Instructions */
.instructions {
    background: rgba(255, 255, 255, 0.2);
    padding: 25px;
    border-radius: 15px;
    
}

.instructions h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
    color: #667eea;
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.instruction-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 15px;
}

.instruction-item .icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.instruction-item .desc {
    font-size: 0.9rem;
    line-height: 1.4;
    color: #333;
}

.instruction-item .desc strong {
    color: #667eea;
    display: block;
    margin-bottom: 5px;
}

.tips {
    background: rgba(102, 126, 234, 0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.tips h4 {
    margin-bottom: 15px;
    color: #667eea;
    font-size: 1.2rem;
}

.tips ul {
    list-style: none;
    padding: 0;
}

.tips li {
    padding: 5px 0;
    padding-left: 20px;
    position: relative;
    color: #333;
}

.tips li:before {
    content: "🫁";
    position: absolute;
    left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .game-info {
        flex-direction: column;
        align-items: stretch;
    }
    
    .score-panel {
        justify-content: center;
    }
    
    .controls {
        justify-content: center;
    }
    
    .breathing-ball {
        width: min(60vw, 200px);
        height: min(60vw, 200px);
    }
    
    #breath-instruction {
        font-size: 1.5rem;
    }
    
    .instruction-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .score-panel {
        gap: 15px;
    }
    
    .score-item .value {
        font-size: 1.5rem;
    }
    
    .controls {
        flex-direction: column;
    }
    
    .control-btn {
        width: 100%;
    }
    
    .breathing-ball {
        width: min(55vw, 180px);
        height: min(55vw, 180px);
    }
    
    .guide-text {
        gap: 20px;
        font-size: 1rem;
    }
}
