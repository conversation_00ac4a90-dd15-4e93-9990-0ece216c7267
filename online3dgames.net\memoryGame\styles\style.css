body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  width: 100%;
  background: url('../img/background.jpg') no-repeat;
  background-size: cover;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
}

#memory-board {
  background: #dddddd;
  padding: 40px;
  margin: 20px 0;
  border-radius: 20px;
  box-shadow: inset 0px 0px 20px -6px #615961;
  display: grid;
  gap: 10px;
  justify-content: center;
  align-content: center;
}

/* Level 1: 4x3 grid for 12 cards */
#memory-board.level-1 {
  grid-template-columns: repeat(4, 111px);
  grid-template-rows: repeat(3, 111px);
  width: calc(4 * 111px + 3 * 10px + 80px); /* 4 cards + 3 gaps + padding */
  height: calc(3 * 111px + 2 * 10px + 80px); /* 3 rows + 2 gaps + padding */
}

/* Level 2: 6x4 grid for 24 cards */
#memory-board.level-2 {
  grid-template-columns: repeat(6, 111px);
  grid-template-rows: repeat(4, 111px);
  width: calc(6 * 111px + 5 * 10px + 80px); /* 6 cards + 5 gaps + padding */
  height: calc(4 * 111px + 3 * 10px + 80px); /* 4 rows + 3 gaps + padding */
}

/* Level 3: 6x6 grid for 36 cards */
#memory-board.level-3 {
  grid-template-columns: repeat(6, 111px);
  grid-template-rows: repeat(6, 111px);
  width: calc(6 * 111px + 5 * 10px + 80px); /* 6 cards + 5 gaps + padding */
  height: calc(6 * 111px + 5 * 10px + 80px); /* 6 rows + 5 gaps + padding */
}

.card {
  display: block;
  perspective: 300px;
  transform-style: preserve-3d;
  position: relative;
  width: 111px;
  height: 111px;
}

.card.turned {
  pointer-events: none;
}

.card.turned .front {
  transform: rotateY(0deg);
}
.card.turned .back {
  transform: rotateY(180deg);
}

.card .back,
.card .front {
  width: 111px;
  height: 111px;
  margin: 0;
  padding: 20px;
  font-size: 64px;
  transition: transform 0.6s ease;
  box-sizing: border-box;
}

.card .back {
  background-color: #456783;
}

.card .front {
  position: absolute;
  left: 0;
  top: 0;
  transform: rotateY(-180deg) translateZ(1px);
  backface-visibility: hidden;
}

#score {
  background-color: rgba(215, 44, 44, 0.9);
  border-radius: 12px;
  font-size: 24px;
  font-family: Arial;
  width: 220px;
  padding: 20px;
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 100;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  
}

h1 {
  color: rgba(215, 40, 40, 0.9);
  font-weight: bolder;
  font-size: 67px;
  text-align: center;
  text-shadow: 0px 0px 20px #969696;
}

h2 {
  margin-top: 0;
  color: #456783;
  font-weight: bolder;
  font-size: 35px;
  text-align: center;
  text-shadow: 1px red;
}

#score h2 {
  margin-top: 0;
  margin-bottom: 15px;
  color: white;
  font-weight: bolder;
  font-size: 28px;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

#score p {
  font-weight: bolder;
  font-size: 16px;
  text-align: left;
  color: white;
  margin: 8px 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

p {
  font-weight: bolder;
  font-size: 20px;
  text-align: center;
}

#restart-btn,
#home-btn {
  background-color: #456783;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  width: 100%;
  margin-top: 10px;
  transition: background-color 0.3s ease;
}

#restart-btn:hover,
#home-btn:hover {
  background-color: #5a7a9a;
}

#restart-btn:active,
#home-btn:active {
  background-color: #3a5a73;
}

#home-btn {
  background-color: #d73c44;
  margin-top: 5px;
}

#home-btn:hover {
  background-color: #e55a61;
}

#home-btn:active {
  background-color: #c12832;
}



.modal-btn.primary {
  background-color: rgba(76, 175, 80, 0.8);
  border-color: rgba(76, 175, 80, 1);
}

.modal-btn.primary:hover {
  background-color: rgba(76, 175, 80, 1);
}

.modal-btn.secondary {
  background-color: rgba(255, 193, 7, 0.8);
  border-color: rgba(255, 193, 7, 1);
}

.modal-btn.secondary:hover {
  background-color: rgba(255, 193, 7, 1);
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  body {
    padding: 10px;
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  h1 {
    font-size: 36px;
    margin: 10px 0;
    text-align: center;
  }

  #score {
    position: unset;
    width: auto;
    padding: 15px;
    font-size: 18px;
    z-index: 100;
  }

  #score h2 {
    font-size: 20px;
    margin-bottom: 10px;
  }

  #score p {
    font-size: 14px;
    margin: 5px 0;
  }

  #memory-board {
    margin-top: 120px;
    padding: 20px;
    width: calc(100vw - 40px);
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Level 1: 4x3 grid for mobile (12 cards) */
  #memory-board.level-1 {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, 1fr);
    width: calc(100vw - 40px);
    max-width: 400px;
    height: auto;
    aspect-ratio: 4/3;
    gap: 8px;
  }

  /* Level 2: 6x4 grid for mobile (24 cards) */
  #memory-board.level-2 {
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(4, 1fr);
    width: calc(100vw - 40px);
    max-width: 480px;
    height: auto;
    aspect-ratio: 6/4;
    gap: 6px;
  }

  /* Level 3: 6x6 grid for mobile (36 cards) */
  #memory-board.level-3 {
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(6, 1fr);
    width: calc(100vw - 40px);
    max-width: 480px;
    height: auto;
    aspect-ratio: 1/1;
    gap: 4px;
  }

  .card {
    width: 100%;
    height: 100%;
    min-width: 50px;
    min-height: 50px;
  }

  .card .back,
  .card .front {
    width: 100%;
    height: 100%;
    padding: 8px;
    font-size: clamp(20px, 8vw, 40px);
    box-sizing: border-box;
  }

  #restart-btn,
  #home-btn {
    padding: 8px 16px;
    font-size: 14px;
    margin-top: 8px;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 28px;
    margin: 5px 0;
  }

  #score {
    top: 5px;
    right: 5px;
    left: 5px;
    padding: 12px;
    font-size: 16px;
  }

  #score h2 {
    font-size: 18px;
    margin-bottom: 8px;
  }

  #score p {
    font-size: 12px;
    margin: 4px 0;
  }

  #memory-board {
    margin-top: 110px;
    padding: 15px;
    width: calc(100vw - 30px);
  }

  /* Adjust grid gaps for very small screens */
  #memory-board.level-1 {
    gap: 6px;
    max-width: 320px;
  }

  #memory-board.level-2 {
    gap: 4px;
    max-width: 360px;
  }

  #memory-board.level-3 {
    gap: 3px;
    max-width: 360px;
  }

  .card .back,
  .card .front {
    padding: 4px;
    font-size: clamp(16px, 6vw, 32px);
  }

  #restart-btn,
  #home-btn {
    padding: 6px 12px;
    font-size: 12px;
    margin-top: 6px;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  #score {
    position: fixed;
    top: 5px;
    right: 5px;
    width: 200px;
    padding: 10px;
    font-size: 14px;
  }

  #score h2 {
    font-size: 16px;
    margin-bottom: 8px;
  }

  #score p {
    font-size: 11px;
    margin: 3px 0;
  }

  #memory-board {
    margin-top: 10px;
    margin-right: 220px;
    padding: 15px;
    width: calc(100vw - 250px);
  }

  h1 {
    font-size: 24px;
    margin: 5px 0;
  }

  #restart-btn,
  #home-btn {
    padding: 5px 10px;
    font-size: 11px;
    margin-top: 5px;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .card {
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  .card:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  #restart-btn,
  #home-btn {
    min-height: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  #restart-btn:active,
  #home-btn:active {
    transform: scale(0.95);
  }
}
