// Virtual Pet Game Logic
class VirtualPet {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        
        // Game state
        this.gameStarted = false;
        
        // Pet properties
        this.petName = 'Orange';
        this.petLevel = 1;
        this.happiness = 100;
        this.hunger = 100;
        this.cleanliness = 100;
        this.experience = 0;
        
        // Pet position and animation
        this.petX = this.canvas.width / 2;
        this.petY = this.canvas.height / 2;
        this.petSize = 80;
        this.animationFrame = 0;
        this.currentAnimation = 'idle';
        this.animationSpeed = 0.1;
        
        // Mood system
        this.currentMood = 'happy';
        this.moodEmojis = {
            happy: '😊',
            sad: '😢',
            hungry: '😋',
            dirty: '😷',
            sleepy: '😴',
            playful: '😸'
        };
        
        // Speech system
        this.speechTexts = {
            happy: ['Meow~ I\'m happy!', 'The weather is nice today~', 'Hello, owner!'],
            sad: ['Meow... I\'m not happy', 'I need your care', 'Play with me'],
            hungry: ['I\'m hungry, meow~', 'Do you have any food?', 'Meow, I want to eat fish'],
            dirty: ['I need a bath', 'I feel dirty', 'Help me clean up'],
            sleepy: ['I\'m sleepy...', 'zzZ...', 'I want to sleep'],
            playful: ['Let\'s play!', 'I want to play', 'Play with me']
        };
        
        // Audio context
        this.audioContext = null;
        this.initAudio();
        
        // Timers
        this.lastUpdate = Date.now();
        this.lastSpeech = 0;
        this.statusDecayInterval = null;
        
        this.setupEventListeners();
        this.startStatusDecay();
        this.animate();
        this.updateDisplay();
    }
    
    initAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Web Audio API not supported');
        }
    }
    
    playMeowSound() {
        if (!this.audioContext) return;
        
        const now = this.audioContext.currentTime;
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.connect(gain);
        gain.connect(this.audioContext.destination);
        
        // Create meow-like sound
        osc.frequency.setValueAtTime(400, now);
        osc.frequency.exponentialRampToValueAtTime(800, now + 0.1);
        osc.frequency.exponentialRampToValueAtTime(300, now + 0.3);
        
        gain.gain.setValueAtTime(0.3, now);
        gain.gain.exponentialRampToValueAtTime(0.001, now + 0.4);
        
        osc.start(now);
        osc.stop(now + 0.4);
    }
    
    playPurrSound() {
        if (!this.audioContext) return;
        
        const now = this.audioContext.currentTime;
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        const filter = this.audioContext.createBiquadFilter();
        
        osc.connect(filter);
        filter.connect(gain);
        gain.connect(this.audioContext.destination);
        
        osc.frequency.setValueAtTime(50 + Math.random() * 20, now);
        filter.type = 'lowpass';
        filter.frequency.setValueAtTime(200, now);
        
        gain.gain.setValueAtTime(0.2, now);
        gain.gain.exponentialRampToValueAtTime(0.001, now + 1);
        
        osc.start(now);
        osc.stop(now + 1);
    }
    
    setupEventListeners() {
        // Canvas click for petting
        this.canvas.addEventListener('click', (e) => this.handlePet(e));
        
        // Action buttons
        document.getElementById('feed-btn').addEventListener('click', () => this.feed());
        document.getElementById('play-btn').addEventListener('click', () => this.play());
        document.getElementById('clean-btn').addEventListener('click', () => this.clean());
        document.getElementById('sleep-btn').addEventListener('click', () => this.sleep());
        
        // Rename button
        document.getElementById('rename-btn').addEventListener('click', () => this.renamePet());
        
        // Start button
        document.getElementById('canvas-start-btn').addEventListener('click', () => this.start());
        document.getElementById('home-btn').addEventListener('click', () => this.goHome());
        
        // Close start modal
        document.getElementById('game-start').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.start();
            }
        });
    }
    
    handlePet(e) {
        if (!this.gameStarted) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // Check if click is on pet
        const distance = Math.sqrt(Math.pow(x - this.petX, 2) + Math.pow(y - this.petY, 2));
        if (distance <= this.petSize) {
            this.pet();
        }
    }
    
    pet() {
        this.happiness = Math.min(100, this.happiness + 5);
        this.experience += 2;
        this.currentAnimation = 'happy';
        this.playPurrSound();
        this.showSpeech('Meow~ It\'s so comfortable!');
        this.updateMood();
        this.updateDisplay();
        this.checkLevelUp();
    }
    
    feed() {
        if (this.hunger >= 90) {
            this.showSpeech('I\'m not hungry~');
            return;
        }
        
        this.hunger = Math.min(100, this.hunger + 30);
        this.happiness = Math.min(100, this.happiness + 10);
        this.experience += 5;
        this.currentAnimation = 'eating';
        this.playMeowSound();
        this.showSpeech('Thank you, owner! It\'s so delicious!');
        this.updateMood();
        this.updateDisplay();
        this.checkLevelUp();
    }
    
    play() {
        if (this.happiness >= 90) {
            this.showSpeech('I\'m happy~');
            return;
        }
        
        this.happiness = Math.min(100, this.happiness + 25);
        this.hunger = Math.max(0, this.hunger - 10);
        this.experience += 8;
        this.currentAnimation = 'playing';
        this.playMeowSound();
        this.showSpeech('It\'s so fun! Let\'s play again~');
        this.updateMood();
        this.updateDisplay();
        this.checkLevelUp();
    }
    
    clean() {
        if (this.cleanliness >= 90) {
            this.showSpeech('I\'m clean~');
            return;
        }
        
        this.cleanliness = Math.min(100, this.cleanliness + 40);
        this.happiness = Math.min(100, this.happiness + 15);
        this.experience += 6;
        this.currentAnimation = 'cleaning';
        this.showSpeech('It\'s so comfortable to bathe!');
        this.updateMood();
        this.updateDisplay();
        this.checkLevelUp();
    }
    
    sleep() {
        this.happiness = Math.min(100, this.happiness + 20);
        this.hunger = Math.max(0, this.hunger - 5);
        this.experience += 3;
        this.currentAnimation = 'sleeping';
        this.showSpeech('zzZ... I\'m sleepy...');
        this.updateMood();
        this.updateDisplay();
        this.checkLevelUp();
        
        // Sleep animation lasts longer
        setTimeout(() => {
            this.currentAnimation = 'idle';
        }, 3000);
    }
    
    renamePet() {
        gameModal.show({
            title: 'Rename Pet',
            content: 'Give your cat a new name:',
            type: 'info',
            buttons: [
                { text: 'Cancel', type: 'secondary', value: 'cancel' },
                { text: 'Confirm', type: 'primary', value: 'confirm' }
            ]
        }).then(result => {
            if (result === 'confirm') {
                const newName = prompt('Enter a new name:', this.petName);
                if (newName && newName.trim()) {
                    this.petName = newName.trim();
                    document.getElementById('pet-name').textContent = this.petName;
                    this.showSpeech(`My new name is ${this.petName}!`);
                }
            }
        });
    }
    
    updateMood() {
        if (this.happiness < 30) {
            this.currentMood = 'sad';
        } else if (this.hunger < 30) {
            this.currentMood = 'hungry';
        } else if (this.cleanliness < 30) {
            this.currentMood = 'dirty';
        } else if (this.happiness > 80) {
            this.currentMood = 'happy';
        } else {
            this.currentMood = 'playful';
        }
        
        document.getElementById('mood-emoji').textContent = this.moodEmojis[this.currentMood];
        document.getElementById('mood-text').textContent = this.getMoodText();
    }
    
    getMoodText() {
        const moodTexts = {
            happy: 'Happy',
            sad: 'Sad',
            hungry: 'Hungry',
            dirty: 'Dirty',
            sleepy: 'Sleepy',
            playful: 'Playful'
        };
        return moodTexts[this.currentMood] || 'Normal';
    }
    
    showSpeech(text) {
        const bubble = document.getElementById('speech-bubble');
        const speechText = document.getElementById('speech-text');
        
        speechText.textContent = text;
        bubble.classList.add('show');
        
        setTimeout(() => {
            bubble.classList.remove('show');
        }, 3000);
    }
    
    checkLevelUp() {
        const expNeeded = this.petLevel * 50;
        if (this.experience >= expNeeded) {
            this.petLevel++;
            this.experience = 0;
            this.showSpeech(`Level up! Now ${this.petLevel} level!`);
            this.playMeowSound();
            document.getElementById('pet-level').textContent = this.petLevel;
        }
    }
    
    startStatusDecay() {
        this.statusDecayInterval = setInterval(() => {
            if (!this.gameStarted) return;
            
            // Gradual status decay
            this.happiness = Math.max(0, this.happiness - 0.5);
            this.hunger = Math.max(0, this.hunger - 1);
            this.cleanliness = Math.max(0, this.cleanliness - 0.3);
            
            this.updateMood();
            this.updateDisplay();
            
            // Random speech
            if (Date.now() - this.lastSpeech > 30000 && Math.random() < 0.1) {
                this.randomSpeech();
                this.lastSpeech = Date.now();
            }
        }, 5000);
    }
    
    randomSpeech() {
        const texts = this.speechTexts[this.currentMood];
        const randomText = texts[Math.floor(Math.random() * texts.length)];
        this.showSpeech(randomText);
    }
    
    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.drawPet();
        this.animationFrame += this.animationSpeed;
        requestAnimationFrame(() => this.animate());
    }
    
    drawPet() {
        const centerX = this.petX;
        const centerY = this.petY;
        
        // Simple cat drawing
        this.ctx.save();
        
        // Body
        this.ctx.fillStyle = '#FF8C42';
        this.ctx.beginPath();
        this.ctx.ellipse(centerX, centerY + 20, 40, 30, 0, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Head
        this.ctx.fillStyle = '#FF8C42';
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY - 20, 35, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Ears
        this.ctx.fillStyle = '#FF8C42';
        this.ctx.beginPath();
        this.ctx.moveTo(centerX - 25, centerY - 40);
        this.ctx.lineTo(centerX - 15, centerY - 55);
        this.ctx.lineTo(centerX - 5, centerY - 40);
        this.ctx.fill();
        
        this.ctx.beginPath();
        this.ctx.moveTo(centerX + 5, centerY - 40);
        this.ctx.lineTo(centerX + 15, centerY - 55);
        this.ctx.lineTo(centerX + 25, centerY - 40);
        this.ctx.fill();
        
        // Eyes
        this.ctx.fillStyle = '#000';
        this.ctx.beginPath();
        this.ctx.arc(centerX - 12, centerY - 25, 3, 0, Math.PI * 2);
        this.ctx.fill();
        
        this.ctx.beginPath();
        this.ctx.arc(centerX + 12, centerY - 25, 3, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Nose
        this.ctx.fillStyle = '#FF69B4';
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY - 15, 2, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Mouth
        this.ctx.strokeStyle = '#000';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.arc(centerX - 5, centerY - 10, 3, 0, Math.PI);
        this.ctx.stroke();
        
        this.ctx.beginPath();
        this.ctx.arc(centerX + 5, centerY - 10, 3, 0, Math.PI);
        this.ctx.stroke();
        
        // Tail
        this.ctx.strokeStyle = '#FF8C42';
        this.ctx.lineWidth = 8;
        this.ctx.beginPath();
        const tailWag = Math.sin(this.animationFrame) * 10;
        this.ctx.moveTo(centerX + 35, centerY + 10);
        this.ctx.quadraticCurveTo(centerX + 60 + tailWag, centerY - 10, centerX + 50, centerY - 30);
        this.ctx.stroke();
        
        this.ctx.restore();
    }
    
    updateDisplay() {
        document.getElementById('happiness-bar').style.width = this.happiness + '%';
        document.getElementById('happiness-value').textContent = Math.round(this.happiness);
        
        document.getElementById('hunger-bar').style.width = this.hunger + '%';
        document.getElementById('hunger-value').textContent = Math.round(this.hunger);
        
        document.getElementById('cleanliness-bar').style.width = this.cleanliness + '%';
        document.getElementById('cleanliness-value').textContent = Math.round(this.cleanliness);
    }
    
    start() {
        document.getElementById('game-start').classList.add('hidden');
        this.gameStarted = true;
        this.showSpeech('Hello! I\'m ' + this.petName + '!');
    }
    
    goHome() {
        window.location.href = '/';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('pet-canvas');
    new VirtualPet(canvas);
});
