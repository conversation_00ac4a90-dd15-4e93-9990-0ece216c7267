/**
 * Sudoku game core class
 */
class SudokuGame {
    constructor() {
        // Game state
        this.board = Array(9).fill().map(() => Array(9).fill(0));
        this.solution = Array(9).fill().map(() => Array(9).fill(0));
        this.initialBoard = Array(9).fill().map(() => Array(9).fill(0));
        this.selectedCell = null;
        this.gameStarted = false;
        this.gameCompleted = false;
        this.difficulty = 'easy';
        this.errors = 0;
        this.startTime = null;
        this.elapsedTime = 0;
        this.timerInterval = null;
        
        // History record (for undo/redo)
        this.history = [];
        this.historyIndex = -1;
        this.maxHistorySize = 50;
        
        // DOM elements
        this.boardElement = document.getElementById('sudoku-board');
        this.cells = [];
        
        // Initialize
        this.initializeBoard();
        this.setupEventListeners();
    }
    
    /**
     * Initialize the board DOM
     */
    initializeBoard() {
        this.boardElement.innerHTML = '';
        this.cells = [];
        
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                const cell = document.createElement('div');
                cell.className = 'sudoku-cell';
                cell.dataset.row = row;
                cell.dataset.col = col;
                
                // Add 3x3 box border style
                if (col === 2 || col === 5) {
                    cell.style.borderRight = '3px solid #333';
                }
                if (row === 2 || row === 5) {
                    cell.style.borderBottom = '3px solid #333';
                }
                
                // Click event
                cell.addEventListener('click', () => this.selectCell(row, col));
                
                this.boardElement.appendChild(cell);
                
                if (!this.cells[row]) {
                    this.cells[row] = [];
                }
                this.cells[row][col] = cell;
            }
        }
    }
    
    /**
     * Set event listeners
     */
    setupEventListeners() {
        // Keyboard input
        document.addEventListener('keydown', (e) => {
            if (!this.gameStarted || this.gameCompleted) return;
            
            const key = e.key;
            
            // Number input
            if (key >= '1' && key <= '9') {
                e.preventDefault();
                this.inputNumber(parseInt(key));
            }
            // Delete
            else if (key === 'Delete' || key === 'Backspace') {
                e.preventDefault();
                this.inputNumber(0);
            }
            // Direction key move
            else if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(key)) {
                e.preventDefault();
                this.moveSelection(key);
            }
        });
        
        // Number selector
        const numberButtons = document.querySelectorAll('.number-btn');
        numberButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const number = parseInt(btn.dataset.number);
                this.inputNumber(number);
            });
        });
    }
    
    /**
     * Select cell
     */
    selectCell(row, col) {
        if (!this.gameStarted || this.gameCompleted) return;
        
        // Clear previous selection
        if (this.selectedCell) {
            const [prevRow, prevCol] = this.selectedCell;
            this.cells[prevRow][prevCol].classList.remove('selected');
        }
        
        // Select new cell
        this.selectedCell = [row, col];
        this.cells[row][col].classList.add('selected');
        
        // Highlight related rows and columns and boxes
        this.highlightRelated(row, col);
    }
    
    /**
     * Highlight related cells
     */
    highlightRelated(row, col) {
        // Clear previous highlight
        this.cells.forEach(rowCells => {
            rowCells.forEach(cell => {
                cell.classList.remove('related');
            });
        });

        // Highlight same row, same column, same box
        for (let i = 0; i < 9; i++) {
            // Same row
            if (this.cells[row] && this.cells[row][i]) {
                this.cells[row][i].classList.add('related');
            }
            // Same column
            if (this.cells[i] && this.cells[i][col]) {
                this.cells[i][col].classList.add('related');
            }
        }

        // Same box
        const boxRow = Math.floor(row / 3) * 3;
        const boxCol = Math.floor(col / 3) * 3;
        for (let r = boxRow; r < boxRow + 3; r++) {
            for (let c = boxCol; c < boxCol + 3; c++) {
                if (this.cells[r] && this.cells[r][c]) {
                    this.cells[r][c].classList.add('related');
                }
            }
        }
    }
    
    /**
     * Input number
     */
    inputNumber(number) {
        if (!this.selectedCell || !this.gameStarted || this.gameCompleted) return;

        const [row, col] = this.selectedCell;

        // Check if it is the initial given number
        if (this.initialBoard[row][col] !== 0) return;

        // If the input is the same number, return directly
        if (this.board[row][col] === number) return;

        // Save history state
        this.saveState();

        // Record the previous value for error detection
        const previousValue = this.board[row][col];

        // Update board
        this.board[row][col] = number;
        this.updateCellDisplay(row, col);

        // Check validity
        const hasConflicts = !this.checkConflicts();

        // If the new input number causes a conflict, increase the error count
        if (number !== 0 && hasConflicts && previousValue === 0) {
            this.errors++;
            this.updateErrorDisplay();
        }

        // Update progress
        this.updateProgress();

        // Check if completed
        if (this.isCompleted()) {
            this.completeGame();
        }
    }
    
    /**
     * Update cell display
     */
    updateCellDisplay(row, col) {
        const cell = this.cells[row][col];
        const value = this.board[row][col];
        
        cell.textContent = value === 0 ? '' : value;
        
        // Set style
        cell.classList.remove('given', 'conflict', 'hint');
        
        if (this.initialBoard[row][col] !== 0) {
            cell.classList.add('given');
        }
    }
    
    /**
     * Check conflicts
     */
    checkConflicts() {
        // Clear previous conflict marks
        this.cells.forEach(rowCells => {
            rowCells.forEach(cell => {
                cell.classList.remove('conflict');
            });
        });
        
        let hasConflicts = false;
        
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (this.board[row][col] !== 0 && this.hasConflict(row, col)) {
                    this.cells[row][col].classList.add('conflict');
                    hasConflicts = true;
                }
            }
        }
        
        return !hasConflicts;
    }
    
    /**
     * Check if there is a conflict at the specified position
     */
    hasConflict(row, col) {
        const value = this.board[row][col];
        if (value === 0) return false;
        
        // Check row
        for (let c = 0; c < 9; c++) {
            if (c !== col && this.board[row][c] === value) {
                return true;
            }
        }
        
        // Check column
        for (let r = 0; r < 9; r++) {
            if (r !== row && this.board[r][col] === value) {
                return true;
            }
        }
        
        // Check 3x3 box
        const boxRow = Math.floor(row / 3) * 3;
        const boxCol = Math.floor(col / 3) * 3;
        for (let r = boxRow; r < boxRow + 3; r++) {
            for (let c = boxCol; c < boxCol + 3; c++) {
                if ((r !== row || c !== col) && this.board[r][c] === value) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Move selection with direction keys
     */
    moveSelection(direction) {
        if (!this.selectedCell) {
            this.selectCell(4, 4); // Default selection is the center
            return;
        }
        
        let [row, col] = this.selectedCell;
        
        switch (direction) {
            case 'ArrowUp':
                row = Math.max(0, row - 1);
                break;
            case 'ArrowDown':
                row = Math.min(8, row + 1);
                break;
            case 'ArrowLeft':
                col = Math.max(0, col - 1);
                break;
            case 'ArrowRight':
                col = Math.min(8, col + 1);
                break;
        }
        
        this.selectCell(row, col);
    }
    
    /**
     * Update progress display
     */
    updateProgress() {
        const filled = this.board.flat().filter(cell => cell !== 0).length;
        document.getElementById('progress').textContent = `${filled}/81`;
    }
    
    /**
     * Check if completed
     */
    isCompleted() {
        // Check if all cells are filled
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (this.board[row][col] === 0) {
                    return false;
                }
            }
        }

        // Check if there is a conflict (only completed if there is no conflict)
        const hasNoConflicts = this.checkConflicts();

        // Additional validation: check if each row, column, and 3x3 box contains all numbers 1-9
        if (hasNoConflicts) {
            return this.isValidSolution();
        }

        return false;
    }

    /**
     * Check if it is a valid complete solution
     */
    isValidSolution() {
        // Check each row
        for (let row = 0; row < 9; row++) {
            const rowSet = new Set(this.board[row]);
            if (rowSet.size !== 9 || !this.hasAllNumbers(rowSet)) {
                return false;
            }
        }

        // Check each column
        for (let col = 0; col < 9; col++) {
            const colSet = new Set();
            for (let row = 0; row < 9; row++) {
                colSet.add(this.board[row][col]);
            }
            if (colSet.size !== 9 || !this.hasAllNumbers(colSet)) {
                return false;
            }
        }

        // Check each 3x3 box
        for (let boxRow = 0; boxRow < 3; boxRow++) {
            for (let boxCol = 0; boxCol < 3; boxCol++) {
                const boxSet = new Set();
                for (let r = boxRow * 3; r < (boxRow + 1) * 3; r++) {
                    for (let c = boxCol * 3; c < (boxCol + 1) * 3; c++) {
                        boxSet.add(this.board[r][c]);
                    }
                }
                if (boxSet.size !== 9 || !this.hasAllNumbers(boxSet)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Check if the set contains all numbers 1-9
     */
    hasAllNumbers(numberSet) {
        for (let i = 1; i <= 9; i++) {
            if (!numberSet.has(i)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Complete game
     */
    completeGame() {
        this.gameCompleted = true;
        this.stopTimer();
        
        // Show completion interface
        document.getElementById('final-time').textContent = this.formatTime(this.elapsedTime);
        document.getElementById('final-errors').textContent = this.errors;
        document.getElementById('final-difficulty').textContent = this.getDifficultyText();
        
        setTimeout(() => {
            document.getElementById('game-complete').classList.remove('hidden');
        }, 500);
    }
    
    /**
     * Save state to history
     */
    saveState() {
        // Remove history after the current position
        this.history = this.history.slice(0, this.historyIndex + 1);
        
        // Add new state
        const state = {
            board: this.board.map(row => [...row]),
            selectedCell: this.selectedCell ? [...this.selectedCell] : null,
            errors: this.errors
        };
        
        this.history.push(state);
        this.historyIndex++;
        
        // Limit history size
        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
            this.historyIndex--;
        }
        
        this.updateUndoRedoButtons();
    }
    
    /**
     * Update undo/redo button state
     */
    updateUndoRedoButtons() {
        const undoBtn = document.getElementById('undo-btn');
        const redoBtn = document.getElementById('redo-btn');
        
        undoBtn.disabled = this.historyIndex <= 0;
        redoBtn.disabled = this.historyIndex >= this.history.length - 1;
    }
    
    /**
     * Format time display
     */
    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    
    /**
     * Get difficulty text
     */
    getDifficultyText() {
        const difficultyMap = {
            'easy': 'Easy',
            'medium': 'Medium',
            'hard': 'Hard',
            'expert': 'Expert'
        };
        return difficultyMap[this.difficulty] || 'Easy';
    }

    /**
     * Undo operation
     */
    undo() {
        if (this.historyIndex <= 0) return;

        this.historyIndex--;
        const state = this.history[this.historyIndex];

        this.board = state.board.map(row => [...row]);
        this.selectedCell = state.selectedCell ? [...state.selectedCell] : null;
        this.errors = state.errors;

        this.updateBoard();
        this.updateUndoRedoButtons();
        this.updateProgress();
        this.updateErrorDisplay();
    }

    /**
     * Redo operation
     */
    redo() {
        if (this.historyIndex >= this.history.length - 1) return;

        this.historyIndex++;
        const state = this.history[this.historyIndex];

        this.board = state.board.map(row => [...row]);
        this.selectedCell = state.selectedCell ? [...state.selectedCell] : null;
        this.errors = state.errors;

        this.updateBoard();
        this.updateUndoRedoButtons();
        this.updateProgress();
        this.updateErrorDisplay();
    }

    /**
     * Update the entire board display
     */
    updateBoard() {
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                this.updateCellDisplay(row, col);
            }
        }

        // Restore selection state
        this.cells.forEach(rowCells => {
            rowCells.forEach(cell => {
                cell.classList.remove('selected');
            });
        });

        if (this.selectedCell) {
            const [row, col] = this.selectedCell;
            this.cells[row][col].classList.add('selected');
            this.highlightRelated(row, col);
        }

        this.checkConflicts();
    }

    /**
     * Update error display
     */
    updateErrorDisplay() {
        document.getElementById('errors').textContent = this.errors;
    }

    /**
     * Clear the board
     */
    clearBoard() {
        this.saveState();

        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (this.initialBoard[row][col] === 0) {
                    this.board[row][col] = 0;
                }
            }
        }

        this.updateBoard();
        this.updateProgress();
    }

    /**
     * Check the current board
     */
    checkBoard() {
        const isValid = this.checkConflicts();

        if (isValid) {
            gameSuccess('The board has no conflicts!', 'Check Result');
        } else {
            gameWarning('Conflicts found, please check the red marked cells', 'Check Result');
        }
    }

    /**
     * Start game
     */
    startGame(difficulty = 'easy') {
        this.difficulty = difficulty;
        this.gameStarted = true;
        this.gameCompleted = false;
        this.errors = 0;
        this.history = [];
        this.historyIndex = -1;

        // Update difficulty display
        document.getElementById('difficulty').textContent = this.getDifficultyText();

        // Start timer
        this.startTimer();

        // Update button state
        this.updateUndoRedoButtons();
        this.updateProgress();
        this.updateErrorDisplay();

        // Hide start interface
        document.getElementById('game-start').classList.add('hidden');
    }

    /**
     * Start timer
     */
    startTimer() {
        this.startTime = Date.now();
        this.elapsedTime = 0;

        this.timerInterval = setInterval(() => {
            this.elapsedTime = Math.floor((Date.now() - this.startTime) / 1000);
            document.getElementById('timer').textContent = this.formatTime(this.elapsedTime);
        }, 1000);
    }

    /**
     * Stop timer
     */
    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }

    /**
     * Restart game
     */
    restartGame() {
        this.stopTimer();
        this.gameStarted = false;
        this.gameCompleted = false;
        this.selectedCell = null;

        // Reset board
        this.board = Array(9).fill().map(() => Array(9).fill(0));
        this.initialBoard = Array(9).fill().map(() => Array(9).fill(0));
        this.solution = Array(9).fill().map(() => Array(9).fill(0));

        // Clear display
        this.cells.forEach(rowCells => {
            rowCells.forEach(cell => {
                cell.textContent = '';
                cell.className = 'sudoku-cell';
            });
        });

        // Reset UI
        document.getElementById('timer').textContent = '00:00';
        document.getElementById('progress').textContent = '0/81';
        document.getElementById('errors').textContent = '0';

        // Hide completion interface
        document.getElementById('game-complete').classList.add('hidden');

        // Show start interface
        document.getElementById('game-start').classList.remove('hidden');
    }

    /**
     * Save game progress
     */
    saveGame() {
        const gameState = {
            board: this.board,
            initialBoard: this.initialBoard,
            solution: this.solution,
            difficulty: this.difficulty,
            errors: this.errors,
            elapsedTime: this.elapsedTime,
            startTime: this.startTime,
            gameStarted: this.gameStarted,
            gameCompleted: this.gameCompleted,
            selectedCell: this.selectedCell,
            history: this.history,
            historyIndex: this.historyIndex,
            timestamp: Date.now()
        };

        try {
            localStorage.setItem('sudoku-save', JSON.stringify(gameState));
            gameSuccess('Game progress saved!', 'Save Success');
        } catch (error) {
            gameError('Save failed, please check browser storage space', 'Save Failed');
        }
    }

    /**
     * Load game progress
     */
    loadGame() {
        try {
            const savedGame = localStorage.getItem('sudoku-save');
            if (!savedGame) {
                gameWarning('No saved game progress found', 'Load Failed');
                return false;
            }

            const gameState = JSON.parse(savedGame);

            // Restore game state
            this.board = gameState.board;
            this.initialBoard = gameState.initialBoard;
            this.solution = gameState.solution;
            this.difficulty = gameState.difficulty;
            this.errors = gameState.errors;
            this.elapsedTime = gameState.elapsedTime;
            this.startTime = gameState.startTime;
            this.gameStarted = gameState.gameStarted;
            this.gameCompleted = gameState.gameCompleted;
            this.selectedCell = gameState.selectedCell;
            this.history = gameState.history || [];
            this.historyIndex = gameState.historyIndex || -1;

            // Update display
            this.updateBoard();
            this.updateProgress();
            this.updateErrorDisplay();
            this.updateUndoRedoButtons();

            // Update difficulty display
            document.getElementById('difficulty').textContent = this.getDifficultyText();

            // Restore timer
            if (this.gameStarted && !this.gameCompleted) {
                this.startTimer();
            }

            // Hide start interface
            document.getElementById('game-start').classList.add('hidden');

            gameSuccess('Game progress loaded!', 'Load Success');
            return true;

        } catch (error) {
            gameError('Load failed, the saved file may be damaged', 'Load Failed');
            return false;
        }
    }
}
