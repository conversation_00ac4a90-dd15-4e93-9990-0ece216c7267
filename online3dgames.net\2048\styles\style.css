
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #ED4264 0%, #FFEDBC 100%);
    min-height: 100vh;
    color: #776e65;
    overflow-x: hidden;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
}


.header {
    text-align: center;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 3rem;
    font-weight: bold;
    color: #776e65;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.home-btn {
    background: linear-gradient(135deg, #ED4264, #FFEDBC);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.home-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}


.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
    flex-wrap: wrap;
}

.score-panel {
    display: flex;
    gap: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.score-item {
    text-align: center;
    min-width: 80px;
}

.score-item .label {
    display: block;
    font-size: 0.9rem;
    font-weight: bold;
    color: #8f7a66;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.score-item .value {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
    color: #776e65;
}

.controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.control-btn.primary {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.control-btn.secondary {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: white;
}

.control-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}


.game-container {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.game-board {
    background: #bbada0;
    border-radius: 10px;
    padding: 10px;
    position: relative;
    width: 400px;
    height: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.grid-container {
    position: absolute;
    z-index: 1;
}

.grid-row {
    display: flex;
    margin-bottom: 10px;
}

.grid-row:last-child {
    margin-bottom: 0;
}

.grid-cell {
    width: 87.5px;
    height: 87.5px;
    background: rgba(238, 228, 218, 0.35);
    border-radius: 6px;
    margin-right: 10px;
}

.grid-cell:last-child {
    margin-right: 0;
}

.tile-container {
    position: absolute;
    z-index: 2;
}

.tile {
    position: absolute;
    width: 87.5px;
    height: 87.5px;
    background: #eee4da;
    border-radius: 6px;
    font-weight: bold;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2rem;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.tile.moving {
    transition: all 0.2s ease-in-out;
    z-index: 10;
}


.tile-2 { background: #eee4da; color: #776e65; font-size: 2rem; }
.tile-4 { background: #ede0c8; color: #776e65; font-size: 2rem; }
.tile-8 { background: #f2b179; color: #f9f6f2; font-size: 2rem; }
.tile-16 { background: #f59563; color: #f9f6f2; font-size: 2rem; }
.tile-32 { background: #f67c5f; color: #f9f6f2; font-size: 2rem; }
.tile-64 { background: #f65e3b; color: #f9f6f2; font-size: 2rem; }
.tile-128 { background: #edcf72; color: #f9f6f2; font-size: 1.8rem; }
.tile-256 { background: #edcc61; color: #f9f6f2; font-size: 1.8rem; }
.tile-512 { background: #edc850; color: #f9f6f2; font-size: 1.8rem; }
.tile-1024 { background: #edc53f; color: #f9f6f2; font-size: 1.5rem; }
.tile-2048 { background: #edc22e; color: #f9f6f2; font-size: 1.5rem; box-shadow: 0 0 20px rgba(237, 194, 46, 0.5); }

.tile-super { background: #3c3a32; color: #f9f6f2; font-size: 1.2rem; }


.tile-new {
    animation: appear 0.2s ease-in-out;
}

.tile-merged {
    animation: pop 0.2s ease-in-out;
}

@keyframes appear {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pop {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}


.game-overlay, .game-over {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    
    z-index: 1000;
}

.game-over.hidden, .game-overlay.hidden {
    display: none;
}

.game-over-content, .game-start-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: scale(0.8);
    animation: modalAppear 0.3s ease forwards;
    min-width: 300px;
}

@keyframes modalAppear {
    to {
        transform: scale(1);
    }
}

.game-over-content h2, .game-start-content h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    color: #776e65;
}

.game-over-content p, .game-start-content p {
    font-size: 1.1rem;
    margin-bottom: 20px;
    color: #8f7a66;
}

#high-score-msg {
    color: #f67c5f;
    font-weight: bold;
    animation: glow 1s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 5px #f67c5f; }
    to { text-shadow: 0 0 20px #f67c5f, 0 0 30px #f67c5f; }
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
    flex-wrap: wrap;
}


.instructions {
    background: rgba(255, 255, 255, 0.9);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.instructions h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
    color: #776e65;
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.instruction-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    background: rgba(237, 194, 46, 0.1);
    border-radius: 10px;
}

.instruction-item .key {
    background: #8f7a66;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: bold;
    min-width: 60px;
    text-align: center;
}

.instruction-item .desc {
    font-size: 0.9rem;
    color: #776e65;
}

.game-rules {
    background: rgba(237, 194, 46, 0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #edc22e;
}

.game-rules h4 {
    margin-bottom: 15px;
    color: #776e65;
    font-size: 1.2rem;
}

.game-rules ul {
    list-style: none;
    padding: 0;
}

.game-rules li {
    padding: 5px 0;
    padding-left: 20px;
    position: relative;
    color: #8f7a66;
}

.game-rules li:before {
    content: "▶";
    position: absolute;
    left: 0;
    color: #edc22e;
}


@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
    }
    
    .header h1 {
        font-size: 2.5rem;
    }
    
    .game-info {
        flex-direction: column;
        align-items: stretch;
    }
    
    .score-panel {
        justify-content: center;
    }
    
    .controls {
        justify-content: center;
    }
    
    .game-board {
        width: min(90vw, 350px);
        height: min(90vw, 350px);
        padding: 10px;
        margin: 0 auto;
    }

    .grid-cell,
    .tile {
        width: calc((min(90vw, 350px) - 50px) / 4);
        height: calc((min(90vw, 350px) - 50px) / 4);
    }

    .tile {
        font-size: 1.6rem;
        font-weight: bold;
    }
    
    .tile-128,
    .tile-256,
    .tile-512 {
        font-size: 1.3rem;
    }
    
    .tile-1024,
    .tile-2048 {
        font-size: 1.1rem;
    }
    
    .tile-super {
        font-size: 0.9rem;
    }
    
    .instruction-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .score-panel {
        gap: 10px;
    }
    
    .score-item .value {
        font-size: 1.5rem;
    }
    
    .controls {
        flex-direction: column;
    }
    
    .control-btn {
        width: 100%;
    }
    
    .modal-buttons {
        flex-direction: column;
    }
    
    .game-board {
        width: min(85vw, 320px);
        height: min(85vw, 320px);
        padding: 8px;
    }

    .grid-cell,
    .tile {
        width: calc((min(85vw, 320px) - 40px) / 4);
        height: calc((min(85vw, 320px) - 40px) / 4);
    }

    .tile {
        font-size: 1.4rem;
        font-weight: bold;
    }
    
    .tile-128,
    .tile-256,
    .tile-512 {
        font-size: 1rem;
    }
    
    .tile-1024,
    .tile-2048 {
        font-size: 0.9rem;
    }
    
    .tile-super {
        font-size: 0.7rem;
    }
}
