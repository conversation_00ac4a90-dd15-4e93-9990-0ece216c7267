// Breakout Game Logic
class BreakoutGame {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        
        // Game state
        this.gameRunning = false;
        this.gamePaused = false;
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        
        // Paddle
        this.paddleWidth = 100;
        this.paddleHeight = 15;
        this.paddleX = (canvas.width - this.paddleWidth) / 2;
        this.paddleY = canvas.height - this.paddleHeight - 20;
        this.paddleSpeed = 8;
        
        // Ball
        this.ballRadius = 8;
        this.ballX = canvas.width / 2;
        this.ballY = canvas.height - 50;
        this.ballDX = 4;
        this.ballDY = -4;
        
        // Bricks
        this.brickRowCount = 6;
        this.brickColumnCount = 10;
        this.brickWidth = 70;
        this.brickHeight = 20;
        this.brickPadding = 5;
        this.brickOffsetTop = 60;
        this.brickOffsetLeft = 35;
        
        this.bricks = [];
        this.initBricks();
        
        // Controls
        this.rightPressed = false;
        this.leftPressed = false;
        this.mouseX = 0;
        
        this.setupEventListeners();
        this.loadHighScore();
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case 'd':
                case 'D':
                    this.rightPressed = true;
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    this.leftPressed = true;
                    break;
                case ' ':
                    e.preventDefault();
                    this.togglePause();
                    break;
            }
        });
        
        document.addEventListener('keyup', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case 'd':
                case 'D':
                    this.rightPressed = false;
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    this.leftPressed = false;
                    break;
            }
        });
        
        this.canvas.addEventListener('mousemove', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            const scaleX = this.canvas.width / rect.width;
            this.mouseX = (e.clientX - rect.left) * scaleX;
        });

        // Touch controls for mobile
        this.setupTouchControls();
    }

    setupTouchControls() {
        // Detect mobile device
        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                       (window.innerWidth <= 768) ||
                       ('ontouchstart' in window);

        // Add touch-action CSS to prevent default behaviors
        this.canvas.style.touchAction = 'none';
        this.canvas.style.userSelect = 'none';
        this.canvas.style.webkitUserSelect = 'none';
        this.canvas.style.webkitTouchCallout = 'none';
        this.canvas.style.webkitTapHighlightColor = 'transparent';

        // Touch move for paddle control
        this.canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            const rect = this.canvas.getBoundingClientRect();
            const scaleX = this.canvas.width / rect.width;
            this.mouseX = (e.touches[0].clientX - rect.left) * scaleX;
        }, { passive: false });

        // Touch start/end for game control
        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();

            // If game hasn't started, start it
            if (!this.gameStarted) {
                this.startGame();
                return;
            }

            // If game is paused, resume it
            if (this.gamePaused) {
                this.togglePause();
                return;
            }

            // Update paddle position
            const rect = this.canvas.getBoundingClientRect();
            const scaleX = this.canvas.width / rect.width;
            this.mouseX = (e.touches[0].clientX - rect.left) * scaleX;

            // Add haptic feedback
            if ('vibrate' in navigator) {
                navigator.vibrate(30);
            }
        }, { passive: false });

        // Prevent scrolling when touching the canvas
        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
        }, { passive: false });
    }
    
    initBricks() {
        this.bricks = [];
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
        
        for (let c = 0; c < this.brickColumnCount; c++) {
            this.bricks[c] = [];
            for (let r = 0; r < this.brickRowCount; r++) {
                this.bricks[c][r] = {
                    x: c * (this.brickWidth + this.brickPadding) + this.brickOffsetLeft,
                    y: r * (this.brickHeight + this.brickPadding) + this.brickOffsetTop,
                    status: 1,
                    color: colors[r],
                    points: (this.brickRowCount - r) * 10
                };
            }
        }
    }
    
    collisionDetection() {
        for (let c = 0; c < this.brickColumnCount; c++) {
            for (let r = 0; r < this.brickRowCount; r++) {
                const brick = this.bricks[c][r];
                if (brick.status === 1) {
                    // Check if ball intersects with brick (including radius)
                    if (this.ballX + this.ballRadius > brick.x &&
                        this.ballX - this.ballRadius < brick.x + this.brickWidth &&
                        this.ballY + this.ballRadius > brick.y &&
                        this.ballY - this.ballRadius < brick.y + this.brickHeight) {

                        // Determine collision side for better physics
                        const ballCenterX = this.ballX;
                        const ballCenterY = this.ballY;
                        const brickCenterX = brick.x + this.brickWidth / 2;
                        const brickCenterY = brick.y + this.brickHeight / 2;

                        const deltaX = ballCenterX - brickCenterX;
                        const deltaY = ballCenterY - brickCenterY;

                        // Determine which side was hit
                        if (Math.abs(deltaX / this.brickWidth) > Math.abs(deltaY / this.brickHeight)) {
                            // Hit from left or right
                            this.ballDX = -this.ballDX;
                        } else {
                            // Hit from top or bottom
                            this.ballDY = -this.ballDY;
                        }

                        brick.status = 0;
                        this.score += brick.points;
                        this.updateScore();

                        // Check if all bricks are destroyed
                        let allDestroyed = true;
                        for (let cc = 0; cc < this.brickColumnCount; cc++) {
                            for (let rr = 0; rr < this.brickRowCount; rr++) {
                                if (this.bricks[cc][rr].status === 1) {
                                    allDestroyed = false;
                                    break;
                                }
                            }
                            if (!allDestroyed) break;
                        }

                        if (allDestroyed) {
                            this.nextLevel();
                        }

                        return; // Only handle one collision per frame
                    }
                }
            }
        }
    }
    
    getTotalBrickPoints() {
        let total = 0;
        for (let r = 0; r < this.brickRowCount; r++) {
            total += (this.brickRowCount - r) * 10 * this.brickColumnCount;
        }
        return total;
    }
    
    drawBall() {
        this.ctx.beginPath();
        this.ctx.arc(this.ballX, this.ballY, this.ballRadius, 0, Math.PI * 2);
        this.ctx.fillStyle = '#FFD700';
        this.ctx.fill();
        this.ctx.strokeStyle = '#FFA500';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        this.ctx.closePath();
    }
    
    drawPaddle() {
        const gradient = this.ctx.createLinearGradient(0, this.paddleY, 0, this.paddleY + this.paddleHeight);
        gradient.addColorStop(0, '#4CAF50');
        gradient.addColorStop(1, '#2E7D32');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(this.paddleX, this.paddleY, this.paddleWidth, this.paddleHeight);
        
        this.ctx.strokeStyle = '#1B5E20';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(this.paddleX, this.paddleY, this.paddleWidth, this.paddleHeight);
    }
    
    drawBricks() {
        for (let c = 0; c < this.brickColumnCount; c++) {
            for (let r = 0; r < this.brickRowCount; r++) {
                const brick = this.bricks[c][r];
                if (brick.status === 1) {
                    const gradient = this.ctx.createLinearGradient(0, brick.y, 0, brick.y + this.brickHeight);
                    gradient.addColorStop(0, brick.color);
                    gradient.addColorStop(1, this.darkenColor(brick.color, 20));
                    
                    this.ctx.fillStyle = gradient;
                    this.ctx.fillRect(brick.x, brick.y, this.brickWidth, this.brickHeight);
                    
                    this.ctx.strokeStyle = this.darkenColor(brick.color, 40);
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeRect(brick.x, brick.y, this.brickWidth, this.brickHeight);
                    
                    // Draw points on brick
                    this.ctx.fillStyle = 'white';
                    this.ctx.font = '12px Arial';
                    this.ctx.textAlign = 'center';
                    this.ctx.fillText(
                        brick.points.toString(),
                        brick.x + this.brickWidth / 2,
                        brick.y + this.brickHeight / 2 + 4
                    );
                }
            }
        }
    }
    
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
    
    update() {
        if (!this.gameRunning || this.gamePaused) return;
        
        // Move paddle
        if (this.rightPressed && this.paddleX < this.canvas.width - this.paddleWidth) {
            this.paddleX += this.paddleSpeed;
        } else if (this.leftPressed && this.paddleX > 0) {
            this.paddleX -= this.paddleSpeed;
        }
        
        // Mouse control
        if (this.mouseX > 0) {
            this.paddleX = this.mouseX - this.paddleWidth / 2;
            if (this.paddleX < 0) this.paddleX = 0;
            if (this.paddleX > this.canvas.width - this.paddleWidth) {
                this.paddleX = this.canvas.width - this.paddleWidth;
            }
        }
        
        // Move ball
        this.ballX += this.ballDX;
        this.ballY += this.ballDY;
        
        // Ball collision with walls
        if (this.ballX + this.ballDX > this.canvas.width - this.ballRadius ||
            this.ballX + this.ballDX < this.ballRadius) {
            this.ballDX = -this.ballDX;
        }

        if (this.ballY + this.ballDY < this.ballRadius) {
            this.ballDY = -this.ballDY;
        }

        // Ball collision with paddle
        if (this.ballY + this.ballRadius >= this.paddleY &&
            this.ballY - this.ballRadius <= this.paddleY + this.paddleHeight &&
            this.ballX >= this.paddleX &&
            this.ballX <= this.paddleX + this.paddleWidth &&
            this.ballDY > 0) {

            // Ball hit paddle
            const hitPos = (this.ballX - this.paddleX) / this.paddleWidth;
            this.ballDX = 8 * (hitPos - 0.5); // Angle based on hit position
            this.ballDY = -Math.abs(this.ballDY);
        }

        // Ball hit bottom (missed paddle)
        if (this.ballY > this.canvas.height) {
            this.lives--;
            this.updateScore();

            if (this.lives === 0) {
                this.gameOver();
            } else {
                this.resetBall();
            }
        }
        
        this.collisionDetection();
    }
    
    draw() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw game elements
        this.drawBricks();
        this.drawBall();
        this.drawPaddle();
        
        // Draw pause indicator
        if (this.gamePaused) {
            this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            this.ctx.font = '48px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('Pause', this.canvas.width / 2, this.canvas.height / 2);
        }
    }
    
    resetBall() {
        this.ballX = this.canvas.width / 2;
        this.ballY = this.canvas.height - 50;
        this.ballDX = 4 * (Math.random() > 0.5 ? 1 : -1);
        this.ballDY = -4;
    }
    
    nextLevel() {
        this.level++;
        this.initBricks();
        this.resetBall();
        
        // Increase ball speed
        const speedMultiplier = 1 + (this.level - 1) * 0.1;
        this.ballDX = this.ballDX > 0 ? 4 * speedMultiplier : -4 * speedMultiplier;
        this.ballDY = -4 * speedMultiplier;
        
        this.updateScore();
        
        // Show level complete message
        document.getElementById('game-over-title').textContent = `Level ${this.level - 1} Complete!`;
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('final-level').textContent = this.level - 1;
        document.getElementById('next-level-btn').classList.remove('hidden');
        document.getElementById('game-over').classList.remove('hidden');
        
        this.gameRunning = false;
    }
    
    start() {
        this.gameRunning = true;
        this.gamePaused = false;
        this.gameLoop();
    }
    
    pause() {
        this.gamePaused = true;
    }
    
    resume() {
        this.gamePaused = false;
        this.gameLoop();
    }
    
    togglePause() {
        if (this.gamePaused) {
            this.resume();
        } else {
            this.pause();
        }
    }
    
    reset() {
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        this.gameRunning = false;
        this.gamePaused = false;
        this.initBricks();
        this.resetBall();
        this.paddleX = (this.canvas.width - this.paddleWidth) / 2;
        this.updateScore();
        this.draw();
    }
    
    gameOver() {
        this.gameRunning = false;
        this.gamePaused = false;
        
        // Check for high score
        const highScore = this.getHighScore();
        if (this.score > highScore) {
            this.saveHighScore(this.score);
            document.getElementById('high-score-msg').classList.remove('hidden');
        } else {
            document.getElementById('high-score-msg').classList.add('hidden');
        }
        
        document.getElementById('game-over-title').textContent = 'Game Over';
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('final-level').textContent = this.level;
        document.getElementById('next-level-btn').classList.add('hidden');
        document.getElementById('game-over').classList.remove('hidden');
    }
    
    gameLoop() {
        if (!this.gameRunning) return;
        
        this.update();
        this.draw();
        
        if (this.gameRunning) {
            requestAnimationFrame(() => this.gameLoop());
        }
    }
    
    updateScore() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('lives').textContent = this.lives;
        document.getElementById('level').textContent = this.level;
        document.getElementById('high-score').textContent = this.getHighScore();
    }
    
    getHighScore() {
        return parseInt(localStorage.getItem('breakoutHighScore') || '0');
    }
    
    saveHighScore(score) {
        localStorage.setItem('breakoutHighScore', score.toString());
    }
    
    loadHighScore() {
        document.getElementById('high-score').textContent = this.getHighScore();
    }
}
