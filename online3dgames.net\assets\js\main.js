
const gameCategories = {
    featured: {
        name: 'Featured Games',
        games: ['blackjack', 'hearts', 'sudoku']
    },
    blackjack: {
        name: 'Blackjack Zone',
        games: ['blackjack', 'blackjack-practice', 'free-bet-blackjack', 'pontoon']
    },
    'card-games': {
        name: 'Card Games',
        games: ['hearts', 'solitaire', 'spider-solitaire', 'texas-holdem', 'spades', 'chess']
    },
    puzzle: {
        name: 'Puzzle Games',
        games: ['sudoku', 'puzzle2048', 'tetris', 'memory']
    },
    arcade: {
        name: 'Classic Arcade',
        games: ['snake', 'breakout', 'reaction']
    },
    relaxation: {
        name: 'Relaxation Games',
        games: ['pop-it-game', 'fidget-spinner', 'breathing-ball', 'drawing-wall', 'bubble-float', 'particle-trail', 'typing-machine', 'virtual-pet']
    }
};

// Game Data for Honeycomb Grid
const gameData = {
    'blackjack': { 
        name: 'Blackjack', 
        icon: '🎰', 
        rating: '4.9', 
        difficulty: '⭐⭐⭐',
        description: 'Master the classic 21-point card game with professional strategies and realistic casino atmosphere.',
        tags: ['Casino', 'Strategy', 'Cards'],
        url: '/blackjack',
        bgClass: 'blackjack-bg'
    },
    'blackjack-practice': { 
        name: 'Practice', 
        icon: '🎯', 
        rating: '4.8', 
        difficulty: '⭐⭐⭐⭐',
        description: 'Learn basic strategy, practice card counting, and master optimal play decisions.',
        tags: ['Training', 'Strategy', 'Practice'],
        url: '/blackjack-practice',
        bgClass: 'practice-bg'
    },
    'free-bet-blackjack': { 
        name: 'Free Bet', 
        icon: '🆓', 
        rating: '4.7', 
        difficulty: '⭐⭐⭐⭐⭐',
        description: 'Advanced Blackjack variant with free double downs, free splits, and special dealer 22 push rules.',
        tags: ['Advanced', 'Free Bets', 'Casino'],
        url: '/freeBetBlackjack',
        bgClass: 'freebet-bg'
    },
    'pontoon': { 
        name: 'Pontoon', 
        icon: '🚤', 
        rating: '4.6', 
        difficulty: '⭐⭐⭐⭐',
        description: 'Experience the British version of Blackjack with unique rules and terminology.',
        tags: ['British', 'Cards', 'Casino'],
        url: '/pontoon-game',
        bgClass: 'pontoon-bg'
    },
    'hearts': { 
        name: 'Hearts', 
        icon: '♥️', 
        rating: '4.8', 
        difficulty: '⭐⭐⭐',
        description: 'Experience the classic trick-taking card game with advanced AI opponents and strategic gameplay.',
        tags: ['Cards', 'Strategy', 'Trick-taking'],
        url: '/hearts',
        bgClass: 'hearts-bg'
    },
    'solitaire': {
        name: 'Solitaire',
        icon: '🃏',
        rating: '4.6',
        difficulty: '⭐⭐⭐',
        description: 'Classic single-player card game with multiple variations and difficulty levels.',
        tags: ['Cards', 'Single Player', 'Classic'],
        url: '/solitaire',
        bgClass: 'solitaire-bg'
    },
    'spider-solitaire': {
        name: 'Spider Solitaire',
        icon: '🕷️',
        rating: '4.5',
        difficulty: '⭐⭐⭐⭐',
        description: 'Master the challenging Spider Solitaire with multiple difficulty levels and strategic gameplay.',
        tags: ['Cards', 'Strategy', 'Solitaire'],
        url: '/spider-solitaire',
        bgClass: 'spider-solitaire-bg'
    },
    'texas-holdem': { 
        name: 'Texas Hold\'em', 
        icon: '🎲', 
        rating: '4.5', 
        difficulty: '⭐⭐⭐⭐⭐',
        description: 'Challenge AI opponents in this classic poker game and master poker strategy.',
        tags: ['Poker', 'AI', 'Strategy'],
        url: '/texas-holdem-game',
        bgClass: 'poker-bg'
    },
    'spades': { 
        name: 'Spades', 
        icon: '♠️', 
        rating: '4.9', 
        difficulty: '⭐⭐⭐⭐',
        description: 'Classic partnership trick-taking card game with strategic bidding and play.',
        tags: ['Cards', 'Partnership', 'Strategy'],
        url: '/spades',
        bgClass: 'spades-bg'
    },
    'sudoku': { 
        name: 'Sudoku', 
        icon: '🧩', 
        rating: '4.8', 
        difficulty: '⭐⭐⭐⭐',
        description: 'Classic number puzzle game with multiple difficulty levels for brain training.',
        tags: ['Logic', 'Brain Training', 'Puzzle'],
        url: '/sudoku-game',
        bgClass: 'sudoku-bg'
    },
    'puzzle2048': { 
        name: '2048', 
        icon: '🔢', 
        rating: '4.7', 
        difficulty: '⭐⭐⭐⭐',
        description: 'Slide and merge number tiles to reach 2048 in this addictive puzzle game.',
        tags: ['Numbers', 'Strategy', 'Puzzle'],
        url: '/2048',
        bgClass: 'puzzle2048-bg'
    },
    'tetris': { 
        name: 'Tetris', 
        icon: '🟦', 
        rating: '4.9', 
        difficulty: '⭐⭐⭐⭐⭐',
        description: 'Rotate and arrange falling blocks to complete lines in this timeless puzzle classic.',
        tags: ['Classic', 'Blocks', 'Puzzle'],
        url: '/tetris-game',
        bgClass: 'tetris-bg'
    },
    'memory': { 
        name: 'Memory', 
        icon: '🧠', 
        rating: '4.6', 
        difficulty: '⭐⭐⭐',
        description: 'Test your memory skills with this superhero-themed card matching game.',
        tags: ['Memory', 'Superhero', 'Cards'],
        url: '/memoryGame',
        bgClass: 'memory-bg'
    },
    'snake': { 
        name: 'Snake', 
        icon: '🐍', 
        rating: '4.7', 
        difficulty: '⭐⭐⭐',
        description: 'Classic snake game where you grow longer by eating food while avoiding walls and yourself.',
        tags: ['Classic', 'Arcade', 'Snake'],
        url: '/snake-game',
        bgClass: 'snake-bg'
    },
    'breakout': { 
        name: 'Breakout', 
        icon: '🧱', 
        rating: '4.6', 
        difficulty: '⭐⭐⭐⭐',
        description: 'Break all the bricks with your paddle and ball in this classic arcade game.',
        tags: ['Arcade', 'Classic', 'Action'],
        url: '/breakout-game',
        bgClass: 'breakout-bg'
    },
    'reaction': { 
        name: 'Reaction Test', 
        icon: '⚡', 
        rating: '4.5', 
        difficulty: '⭐⭐⭐',
        description: 'Test your reaction time and reflexes in this fast-paced arcade game.',
        tags: ['Reaction', 'Speed', 'Arcade'],
        url: '/reaction-test',
        bgClass: 'reaction-bg'
    },
    'popit': { 
        name: 'Pop-it', 
        icon: '🫧', 
        rating: '4.4', 
        difficulty: '⭐',
        description: 'Pop virtual bubbles for stress relief and relaxation in this calming game.',
        tags: ['Relaxation', 'Stress Relief', 'Bubbles'],
        url: '/pop-it-game',
        bgClass: 'popit-bg'
    },
    'spinner': { 
        name: 'Spinner', 
        icon: '🌀', 
        rating: '4.3', 
        difficulty: '⭐',
        description: 'Spin the virtual fidget spinner and watch mesmerizing patterns unfold.',
        tags: ['Fidget', 'Relaxing', 'Meditation'],
        url: '/fidget-spinner',
        bgClass: 'spinner-bg'
    },
    'breathing': { 
        name: 'Breathing Ball', 
        icon: '🌈', 
        rating: '4.5', 
        difficulty: '⭐',
        description: 'Follow the breathing ball for meditation and relaxation exercises.',
        tags: ['Meditation', 'Breathing', 'Relaxation'],
        url: '/breathing-ball',
        bgClass: 'breathing-bg'
    },
    'drawing': { 
        name: 'Drawing Wall', 
        icon: '🎨', 
        rating: '4.4', 
        difficulty: '⭐⭐',
        description: 'Create digital art on a virtual drawing wall with various tools and colors.',
        tags: ['Art', 'Creative', 'Drawing'],
        url: '/drawing-wall',
        bgClass: 'drawing-bg'
    },
    'particles': { 
        name: 'Particle Trail', 
        icon: '✨', 
        rating: '4.3', 
        difficulty: '⭐',
        description: 'Create beautiful particle trails with your mouse movements in this interactive experience.',
        tags: ['Interactive', 'Particles', 'Visual'],
        url: '/particle-trail',
        bgClass: 'particles-bg'
    },
    'typing': { 
        name: 'Typing Machine', 
        icon: '⌨️', 
        rating: '4.2', 
        difficulty: '⭐⭐',
        description: 'Practice typing skills with a vintage typewriter interface and sound effects.',
        tags: ['Typing', 'Practice', 'Vintage'],
        url: '/typing-machine',
        bgClass: 'typing-bg'
    },
    'bubbles': { 
        name: 'Bubbles', 
        icon: '💭', 
        rating: '4.4', 
        difficulty: '⭐',
        description: 'Pop floating bubbles in a peaceful environment for ultimate relaxation.',
        tags: ['Bubbles', 'Peaceful', 'Relaxing'],
        url: '/bubble-float',
        bgClass: 'bubbles-bg'
    },
    'pet': { 
        name: 'Virtual Pet', 
        icon: '🐱', 
        rating: '4.5', 
        difficulty: '⭐⭐',
        description: 'Take care of your virtual pet with feeding, playing, and nurturing activities.',
        tags: ['Pet', 'Care', 'Virtual'],
        url: '/virtual-pet',
        bgClass: 'pet-bg'
    }
};

// Loading Screen Management
class LoadingManager {
    constructor() {
        this.$loadingScreen = $('#loadingScreen');
        this.minLoadTime = 1500;
        this.startTime = Date.now();
    }

    hide() {
        const elapsed = Date.now() - this.startTime;
        const remainingTime = Math.max(0, this.minLoadTime - elapsed);

        setTimeout(() => {
            if (this.$loadingScreen.length) {
                this.$loadingScreen.addClass('hidden');
                setTimeout(() => {
                    this.$loadingScreen.css('display', 'none');
                    this.triggerEntranceAnimations();
                }, 500);
            }
        }, remainingTime);
    }

    triggerEntranceAnimations() {
        // Animate hero content
        const $heroContent = $('.swiper-slide-active .slide-content');
        if ($heroContent.length) {
            $heroContent.css('animation', 'slideInLeft 0.8s ease-out');
        }

        // Animate featured games
        $('.featured-games .game-card').each((index, card) => {
            setTimeout(() => {
                $(card).css('animation', 'fadeInUp 0.6s ease-out');
            }, 500 + index * 150);
        });
    }
}

// Epic Hero Carousel with Swiper
class EpicHeroCarousel {
    constructor() {
        this.swiper = null;
        this.init();
    }
    
    init() {
        // Wait for Swiper to be available
        if (typeof Swiper === 'undefined') {
            console.warn('Swiper not loaded, retrying...');
            setTimeout(() => this.init(), 100);
            return;
        }
        
        // Initialize Swiper
        this.swiper = new Swiper('.hero-swiper', {
            direction: 'horizontal',
            loop: false,
            slidesPerView: 1,
            spaceBetween: 0,
            centeredSlides: true,

            // Touch settings
            touchRatio: 1,
            touchAngle: 45,
            grabCursor: true,
            allowTouchMove: true,
            simulateTouch: true,
            followFinger: true,
            shortSwipes: true,
            longSwipes: true,
            touchStartPreventDefault: false,
            touchMoveStopPropagation: false,

            // Autoplay settings
            autoplay: {
                delay: 6000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
                reverseDirection: false,
            },

            // Transition settings
            speed: 1000,
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            },
            slideToClickedSlide: true,

            // Navigation
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },

            // Pagination
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                dynamicBullets: true,
            },

            // Responsive breakpoints
            breakpoints: {
                320: {
                    touchRatio: 1.2,
                },
                768: {
                    touchRatio: 1,
                },
                1024: {
                    touchRatio: 0.8,
                }
            },

            // Events
            on: {
                slideChange: () => {
                    this.onSlideChange();
                },
                init: () => {
                    setTimeout(() => this.updateHotGameActive(0), 100);
                    // Ensure all slides are properly initialized
                    this.ensureAllSlidesVisible();
                },
                slideChangeTransitionEnd: () => {
                    this.ensureAllSlidesVisible();
                }
            }
        });
        
        // Handle CTA buttons with category navigation
        $('.btn-primary[data-category]').on('click', (e) => {
            e.preventDefault();
            const category = $(e.currentTarget).attr('data-category');
            if (category) {
                this.switchToCategory(category);
            }
        });

        // Handle hot game items hover to switch slides
        this.initHotGameHover();

        // Handle category game card clicks
        this.initCategoryCardClicks();
    }
    
    onSlideChange() {
        // Add slide change animations
        const $activeSlide = $('.swiper-slide-active .slide-content');
        if ($activeSlide.length) {
            $activeSlide.css('animation', 'none');
            setTimeout(() => {
                $activeSlide.css('animation', 'slideInLeft 0.8s ease-out');
            }, 50);
        }

        // Update hot game active state
        const currentIndex = this.swiper?.activeIndex ?? 0;
        this.updateHotGameActive(currentIndex);
    }

    ensureAllSlidesVisible() {
        // Ensure all slides are properly initialized for fade effect
        $('.hero-swiper .swiper-slide').each((index, slide) => {
            const $slide = $(slide);
            $slide.css('visibility', 'visible');
            // Only the active slide should be visible with fade effect
            if (index === 0) {
                $slide.css('opacity', '1').addClass('swiper-slide-active');
            } else {
                $slide.css('opacity', '0').removeClass('swiper-slide-active');
            }
        });

        // Force update Swiper
        if (this.swiper) {
            this.swiper.update();
        }
    }

    initHotGameHover() {
        $('.hot-game-item[data-slide]').each((index, item) => {
            const $item = $(item);
            const slideIndex = parseInt($item.attr('data-slide'));

            $item.on('mouseenter', () => {
                if (!isNaN(slideIndex) && this.swiper) {
                    // Pause autoplay temporarily
                    if (this.swiper.autoplay) {
                        this.swiper.autoplay.stop();
                    }
                    // Go to specific slide - onSlideChange will handle the active state update
                    this.swiper.slideTo(slideIndex, 500);
                }
            });

            $item.on('mouseleave', () => {
                // Resume autoplay after a delay
                setTimeout(() => {
                    if (this.swiper && this.swiper.autoplay) {
                        this.swiper.autoplay.start();
                    }
                }, 1500);
            });
        });

        // Initialize first active state
        this.updateHotGameActive(0);
    }

    updateHotGameActive(activeIndex = null) {
        const currentIndex = activeIndex !== null ? activeIndex : (this.swiper?.activeIndex ?? 0);

        $('.hot-game-item[data-slide]').each((index, item) => {
            const $item = $(item);
            const slideIndex = parseInt($item.attr('data-slide'));
            const isActive = slideIndex === currentIndex;
            $item.toggleClass('active', isActive);
        });
    }

    initCategoryCardClicks() {
        // Use event delegation to handle dynamically generated cards
        $(document).on('click', '.category-game-card', (e) => {
            const $card = $(e.currentTarget);

            // Don't trigger if clicking the play button directly
            if ($(e.target).hasClass('play-btn')) {
                return;
            }

            // Find the play button and get its href
            const href = $card.find('.play-btn').attr('href');

            if (href) {
                window.location.href = href;
            }
        });
    }

    switchToCategory(category) {
        // Switch to category tab
        const $categoryTab = $(`[data-tab="${category}"]`);
        if ($categoryTab.length) {
            $categoryTab.trigger('click');
        }

        // Scroll to games section
        const $gamesSection = $('.games-section');
        if ($gamesSection.length) {
            $gamesSection[0].scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
    
    destroy() {
        if (this.swiper) {
            this.swiper.destroy(true, true);
        }
    }
}

// Category Tabs Management
class CategoryTabs {
    constructor() {
        this.activeTab = 'featured';
        this.$tabButtons = $('.tab-btn');
        this.$tabPanels = $('.tab-panel');
        this.init();
    }

    init() {
        // Add click listeners to tab buttons
        this.$tabButtons.on('click', (e) => {
            const tabId = $(e.currentTarget).attr('data-tab');
            this.switchTab(tabId);
        });

        // Tab content is now static HTML for SEO - no need to load dynamically
        // Keep tab switching functionality for user interaction
        this.updateTabCounts();
        this.filterGamesGrid(this.activeTab);
    }

    updateTabCounts() {
        // Count actual games for each category from tab-panel HTML
        this.$tabButtons.each((index, btn) => {
            const $btn = $(btn);
            const tabId = $btn.attr('data-tab');

            // Count games directly from the tab-panel
            const $tabPanel = $(`.tab-panel[data-panel="${tabId}"]`);
            const count = $tabPanel.find('.category-game-card').length;

            const $countElement = $btn.find('.tab-count');
            if ($countElement.length) {
                $countElement.text(count);
            }
        });
    }
    
    switchTab(tabId) {
        if (tabId === this.activeTab) return;

        // Update button states
        this.$tabButtons.each((index, btn) => {
            const $btn = $(btn);
            $btn.toggleClass('active', $btn.attr('data-tab') === tabId);
        });

        // Update panel states
        this.$tabPanels.each((index, panel) => {
            const $panel = $(panel);
            $panel.toggleClass('active', $panel.attr('data-panel') === tabId);
        });

        this.activeTab = tabId;
        
        // Static HTML is already loaded - no need to load content dynamically
        // Filter games in main grid
        this.filterGamesGrid(tabId);
    }
    
    loadTabContent(tabId = null) {
        // Skip dynamic loading since we now have static HTML for SEO
        // All tab content is pre-loaded in the HTML for better SEO
        console.log('Using static tab content for SEO optimization');
    }

    getGamesForCategory(category) {
        // Check if category exists in gameCategories
        if (!gameCategories[category]) {
            console.warn(`Category '${category}' not found in gameCategories`);
            return [];
        }

        const categoryGames = gameCategories[category].games || [];
        const games = [];

        $('.games-grid .game-card').each((index, card) => {
            const $card = $(card);
            const gameId = $card.attr('data-game');
            const cardCategories = $card.attr('data-categories');

            if (category === 'featured') {
                // Featured games logic
                if (['blackjack', 'hearts', 'sudoku'].includes(gameId)) {
                    games.push(this.extractGameInfo($card[0]));
                }
            } else if (categoryGames.includes(gameId) || cardCategories?.includes(category)) {
                games.push(this.extractGameInfo(card));
            }
        });

        return games;
    }

    extractGameInfo(card) {
        const $card = $(card);
        const title = $card.find('.game-name').text() || 'Unknown Game';
        const description = $card.find('.game-description').text() || '';
        const href = $card.attr('href') || '#';
        const gameId = $card.attr('data-game') || '';
        const genre = $card.find('.game-genre').text() || '';
        const rating = $card.find('.rating-text').text() || '4.5';

        return {
            id: gameId,
            title,
            description: description.length > 80 ? description.substring(0, 80) + '...' : description,
            href,
            genre,
            rating,
            icon: this.getGameIcon(gameId)
        };
    }
    
    getGameIcon(gameId) {
        const iconMap = {
            'blackjack': '🎰',
            'blackjack-practice': '🎯',
            'free-bet-blackjack': '🆓',
            'pontoon': '🚤',
            'solitaire': '🃏',
            'texas-holdem': '🎲',
            'hearts': '♥️',
            'sudoku': '🧩',
            'memory': '🧠',
            'puzzle2048': '🔢',
            'tetris': '🟦',
            'snake': '🐍',
            'breakout': '🧱',
            'reaction': '⚡',
            'popit': '🫧',
            'spinner': '🌀',
            'breathing': '🌈',
            'drawing': '🎨',
            'particles': '✨',
            'typing': '⌨️',
            'bubbles': '💭',
            'pet': '🐱'
        };

        return iconMap[gameId] || '🎮';
    }
    
    generateTabContent(games) {
        // Method preserved for compatibility but not used
        // Static HTML content is now used for SEO optimization
        return '<div class="static-content-placeholder">Content is now static for SEO</div>';
    }
    
    filterGamesGrid(category) {
        // All Games section should always show all games
        // Only filter the category tab content, not the main games grid
        console.log('Category switched to:', category, '- All Games section remains unfiltered');

        // The main games grid (.games-grid) should always show all games
        // No filtering applied to maintain the complete hexagon pattern
    }
}

// Search Functionality
class SearchManager {
    constructor() {
        this.$searchInput = $('.search-input');
        this.$searchBtn = $('.search-btn');
        this.init();
    }

    init() {
        if (!this.$searchInput.length) return;

        this.$searchInput.on('input', (e) => {
            this.handleSearch($(e.target).val());
        });

        this.$searchBtn.on('click', () => {
            this.handleSearch(this.$searchInput.val());
        });

        this.$searchInput.on('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSearch(this.$searchInput.val());
            }
        });
    }

    handleSearch(query) {
        const trimmedQuery = query.trim().toLowerCase();

        if (!trimmedQuery) {
            $('.games-grid .game-card').removeClass('hidden');
            return;
        }

        $('.games-grid .game-card').each((index, card) => {
            const $card = $(card);
            const title = $card.find('.game-name').text().toLowerCase() || '';
            const description = $card.find('.game-description').text().toLowerCase() || '';
            const tags = $card.find('.tag').map((i, tag) => $(tag).text().toLowerCase()).get().join(' ');

            const matches = title.includes(trimmedQuery) ||
                          description.includes(trimmedQuery) ||
                          tags.includes(trimmedQuery);

            $card.toggleClass('hidden', !matches);
        });

        const $gamesSection = $('.games-section');
        if ($gamesSection.length) {
            $gamesSection[0].scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
}

// Mobile Menu Management
class MobileMenu {
    constructor() {
        this.$menuToggle = $('#menuToggle');
        this.isOpen = false;
        this.init();
    }

    init() {
        if (!this.$menuToggle.length) return;

        this.$menuToggle.on('click', () => {
            this.toggle();
        });
    }

    toggle() {
        this.isOpen = !this.isOpen;
        this.$menuToggle.toggleClass('active', this.isOpen);

        this.$menuToggle.find('.hamburger').each((index, line) => {
            const $line = $(line);
            if (this.isOpen) {
                if (index === 0) $line.css('transform', 'rotate(45deg) translate(5px, 5px)');
                if (index === 1) $line.css('opacity', '0');
                if (index === 2) $line.css('transform', 'rotate(-45deg) translate(7px, -6px)');
            } else {
                $line.css({
                    'transform': '',
                    'opacity': ''
                });
            }
        });
    }
}

// Scroll Effects
class ScrollEffects {
    constructor() {
        this.$nav = $('.top-nav');
        this.init();
    }

    init() {
        $(window).on('scroll', () => {
            this.handleScroll();
        });
    }

    handleScroll() {
        const scrollY = $(window).scrollTop();

        if (this.$nav.length) {
            if (scrollY > 100) {
                this.$nav.css({
                    'background': 'rgba(10, 10, 10, 0.98)',
                    'box-shadow': '0 4px 20px rgba(0, 0, 0, 0.3)'
                });
            } else {
                this.$nav.css({
                    'background': 'rgba(10, 10, 10, 0.95)',
                    'box-shadow': 'none'
                });
            }
        }
    }
}

// Honeycomb Grid Manager
class HoneycombGridManager {
    constructor() {
        this.gamesGrid = document.getElementById('gamesGrid');
        this.allGames = Object.keys(gameData);
    }

    init() {
        if (!this.gamesGrid) {
            console.warn('Games grid not found');
            return;
        }

        // Skip generating honeycomb grid since we now have static HTML for SEO
        // The static HTML is already present in the index.html file
        console.log('Using static game grid for SEO optimization');
    }

    // Keep these methods for backward compatibility but don't use them
    arrangeInHoneycombRows(games) {
        // Method preserved for compatibility but not used
        return [];
    }

    generateGameCard(gameId) {
        // Method preserved for compatibility but not used
        return '';
    }

    getGameCategory(gameId) {
        // Return all categories that contain this game
        const categories = [];
        for (const [categoryId, category] of Object.entries(gameCategories)) {
            if (category.games.includes(gameId)) {
                categories.push(categoryId);
            }
        }
        return categories.length > 0 ? categories : ['other'];
    }

    generateHoneycombGrid() {
        // Skip generation since we now use static HTML for SEO
        // The static HTML provides better SEO while JavaScript handles interactions
        console.log('Static honeycomb grid already loaded for SEO');
    }
}

// Initialize Application
class GameHubApp {
    constructor() {
        this.loadingManager = new LoadingManager();
        this.heroCarousel = null;
        this.categoryTabs = null;
        this.searchManager = null;
        this.mobileMenu = null;
        this.scrollEffects = null;
        this.honeycombGridManager = null; // Added honeycomb grid manager

        this.init();
    }

    init() {
        this.initializeComponents();
    }

    initializeComponents() {
        this.loadingManager.hide();

        this.heroCarousel = new EpicHeroCarousel();
        this.honeycombGridManager = new HoneycombGridManager(); // 先初始化honeycomb grid
        this.honeycombGridManager.init();
        this.categoryTabs = new CategoryTabs(); // 再初始化tab
        this.searchManager = new SearchManager();
        this.mobileMenu = new MobileMenu();
        this.scrollEffects = new ScrollEffects();

        this.initSmoothScrolling();
        this.initScrollAnimations();
        this.initFooterLinks();
    }

    initSmoothScrolling() {
        $('a[href^="#"]').on('click', function (e) {
            e.preventDefault();
            const target = $($(this).attr('href'));
            if (target.length) {
                target[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }

    initScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        $('.game-card, .section-header').each((index, el) => {
            observer.observe(el);
        });
    }

    initFooterLinks() {
        $('.footer-links a[data-category]').on('click', (e) => {
            e.preventDefault();
            const category = $(e.currentTarget).attr('data-category');
            if (category && this.categoryTabs) {
                this.categoryTabs.switchTab(category);

                const $heroSection = $('.hero-section');
                if ($heroSection.length) {
                    $heroSection[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    }
}

// Initialize the application
const gameHubApp = new GameHubApp();

// Export for potential future use
window.GameHub = {
    gameCategories,
    LoadingManager,
    EpicHeroCarousel,
    CategoryTabs,
    SearchManager,
    MobileMenu,
    ScrollEffects,
    GameHubApp
};
