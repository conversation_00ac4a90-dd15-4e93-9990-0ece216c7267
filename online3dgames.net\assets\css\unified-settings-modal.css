/* Unified Settings Modal Styles for Blackjack Games */

.settings-modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    justify-content: center;
    align-items: center;
    animation: modalFadeIn 0.3s ease-out;
}

.settings-modal-content {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 16px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 2px solid rgba(255, 215, 0, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.settings-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    padding: 16px 20px;
}

.settings-modal-title {
    color: #ffd700;
    font-size: 20px;
    margin: 0;
    font-weight: 600;
}

.settings-modal-close {
    background: none;
    border: none;
    color: #ffd700;
    font-size: 28px;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.settings-modal-close:hover {
    background: rgba(255, 215, 0, 0.1);
    transform: scale(1.1);
}

.settings-modal-body {
    padding: 20px;
}

.settings-section {
    margin-bottom: 24px;
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section-title {
    color: #ffd700;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-control-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.settings-control-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-control-label {
    color: #e2e8f0;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
}

.settings-toggle-button {
    background: linear-gradient(145deg, #dc2626, #b91c1c);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    width: 100%;
}

.settings-toggle-button.active {
    background: linear-gradient(145deg, #16a34a, #15803d);
}

.settings-toggle-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.settings-volume-control {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    align-items: center;
}

.settings-volume-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
    -webkit-appearance: none;
}

.settings-volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #ffd700;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.settings-volume-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #ffd700;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.settings-volume-value {
    color: #ffd700;
    font-size: 12px;
    font-weight: 600;
    min-width: 35px;
    text-align: right;
}

.settings-test-button {
    background: linear-gradient(145deg, #7c3aed, #6d28d9);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 16px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    width: 100%;
}

.settings-test-button:hover {
    background: linear-gradient(145deg, #8b5cf6, #7c3aed);
    transform: translateY(-1px);
}

.settings-language-switcher {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.settings-language-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #e2e8f0;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    width: 100%;
}

.settings-language-button.active {
    background: linear-gradient(145deg, #ffd700, #f59e0b);
    color: #1a1a2e;
    border-color: #ffd700;
}

.settings-language-button:hover {
    background: rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.4);
}

.settings-game-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.settings-option-row {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.settings-option-buttons {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
}

.settings-option-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #e2e8f0;
    padding: 8px 12px;
    border-radius: 16px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    width: 100%;
    text-align: center;
}

.settings-option-button.active {
    background: linear-gradient(145deg, #3b82f6, #2563eb);
    border-color: #3b82f6;
    color: white;
}

.settings-option-button:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.4);
}

.settings-rules-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: linear-gradient(145deg, #059669, #047857);
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    width: 100%;
}

.settings-rules-link:hover {
    background: linear-gradient(145deg, #10b981, #059669);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.settings-navigation-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.settings-single-button {
    display: grid;
    grid-template-columns: 1fr;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .settings-modal-content {
        width: 95%;
        max-height: 85vh;
        margin: 20px;
    }
    
    .settings-modal-header {
        padding: 12px 16px;
    }
    
    .settings-modal-title {
        font-size: 18px;
    }
    
    .settings-modal-body {
        padding: 16px;
    }
    
    .settings-section {
        margin-bottom: 20px;
    }
    
    .settings-control-item {
        padding: 12px;
    }
    
    .settings-language-switcher {
        justify-content: flex-start;
    }
    
    .settings-option-buttons {
        grid-template-columns: repeat(5, 1fr);
        gap: 6px;
    }

    .settings-option-button {
        padding: 6px 8px;
        font-size: 12px;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .settings-toggle-button,
    .settings-test-button,
    .settings-language-button,
    .settings-option-button,
    .settings-rules-link {
        min-height: 44px;
        min-width: 44px;
    }
    
    .settings-volume-slider::-webkit-slider-thumb {
        width: 24px;
        height: 24px;
    }
    
    .settings-volume-slider::-moz-range-thumb {
        width: 24px;
        height: 24px;
    }
}

/* Share Section Styles */
.share-section {
    border-top: 1px solid rgba(255, 215, 0, 0.2);
    padding-top: 16px;
    margin-top: 16px;
}

.share-text {
    text-align: center;
    margin-bottom: 16px;
}

.share-text p {
    color: #ffd700;
    font-size: 14px;
    margin: 0;
    font-weight: 500;
}

.share-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.share-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 50%;
    background: rgba(255, 215, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    padding: 0;
}

.share-button img {
    width: 24px;
    height: 24px;
    filter: brightness(0) saturate(100%) invert(85%) sepia(100%) saturate(348%) hue-rotate(15deg) brightness(101%) contrast(103%);
    transition: filter 0.3s ease;
}

.share-button:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.share-button:active {
    transform: translateY(0) scale(1.05);
}

/* Specific platform colors */
.share-button.facebook:hover {
    background: rgba(24, 119, 242, 0.2);
    border-color: #1877f2;
}

.share-button.facebook:hover img {
    filter: brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(1458%) hue-rotate(213deg) brightness(97%) contrast(98%);
}

.share-button.twitter:hover {
    background: rgba(29, 161, 242, 0.2);
    border-color: #1da1f2;
}

.share-button.twitter:hover img {
    filter: brightness(0) saturate(100%) invert(58%) sepia(96%) saturate(1458%) hue-rotate(182deg) brightness(97%) contrast(98%);
}

.share-button.whatsapp:hover {
    background: rgba(37, 211, 102, 0.2);
    border-color: #25d366;
}

.share-button.whatsapp:hover img {
    filter: brightness(0) saturate(100%) invert(64%) sepia(98%) saturate(1458%) hue-rotate(120deg) brightness(97%) contrast(98%);
}

.share-button.telegram:hover {
    background: rgba(0, 136, 204, 0.2);
    border-color: #0088cc;
}

.share-button.telegram:hover img {
    filter: brightness(0) saturate(100%) invert(58%) sepia(96%) saturate(1458%) hue-rotate(182deg) brightness(80%) contrast(98%);
}

.share-button.copy:hover {
    background: rgba(108, 117, 125, 0.2);
    border-color: #6c757d;
}

.share-button.copy:hover img {
    filter: brightness(0) saturate(100%) invert(50%) sepia(8%) saturate(1458%) hue-rotate(182deg) brightness(97%) contrast(98%);
}

.share-button.copied {
    background: rgba(40, 167, 69, 0.2) !important;
    border-color: #28a745 !important;
}

.share-button.copied img {
    filter: brightness(0) saturate(100%) invert(42%) sepia(96%) saturate(1458%) hue-rotate(120deg) brightness(97%) contrast(98%) !important;
}

/* Mobile responsive adjustments */
@media (max-width: 480px) {
    .share-buttons {
        gap: 10px;
    }

    .share-button {
        width: 36px;
        height: 36px;
    }

    .share-button img {
        width: 20px;
        height: 20px;
    }

    .share-text p {
        font-size: 13px;
    }
}

/* Touch device optimizations for share buttons */
@media (hover: none) and (pointer: coarse) {
    .share-button {
        width: 44px;
        height: 44px;
    }

    .share-button img {
        width: 26px;
        height: 26px;
    }
}

/* Copy Success Message */
.copy-success-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(40, 167, 69, 0.95);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
    transition: all 0.3s ease;
    pointer-events: none;
    white-space: nowrap;
}

.copy-success-message.show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.copy-success-message::before {
    content: '✓';
    margin-right: 8px;
    font-weight: bold;
}

/* Ensure message appears above modal content */
.settings-modal {
    position: relative;
}

/* Feedback Section Styles */
.feedback-section {
    border-top: 1px solid rgba(255, 215, 0, 0.2);
    padding-top: 20px;
    margin-top: 20px;
}

.feedback-text {
    text-align: center;
    margin-bottom: 15px;
}

.feedback-text p {
    color: #ffffff;
    font-size: 14px;
    margin: 0;
    opacity: 0.9;
}

.feedback-button-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.feedback-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 25px;
    background: linear-gradient(145deg, rgba(255, 107, 0, 0.1), rgba(255, 140, 66, 0.1));
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.feedback-button:hover {
    background: linear-gradient(145deg, rgba(255, 107, 0, 0.2), rgba(255, 140, 66, 0.2));
    border-color: rgba(255, 107, 0, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 0, 0.3);
    color: #ffffff;
    text-decoration: none;
}

.feedback-button:active {
    transform: translateY(0);
}

.feedback-button span:first-child {
    font-size: 16px;
}

/* Mobile responsive adjustments for feedback */
@media (max-width: 480px) {
    .feedback-button {
        padding: 10px 20px;
        font-size: 13px;
    }

    .feedback-button span:first-child {
        font-size: 14px;
    }
}
