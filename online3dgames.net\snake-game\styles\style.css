/* Snake Game Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
    min-height: 100vh;
    color: white;
    overflow-x: hidden;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
}

.header h1 {
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin: 0;
}

.home-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.home-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Game Info Panel */
.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
    flex-wrap: wrap;
}

.score-panel {
    display: flex;
    gap: 30px;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
}

.score-item {
    text-align: center;
}

.score-item .label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.score-item .value {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.control-btn.primary {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.control-btn.secondary {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: white;
}

.control-btn.danger {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
}

.control-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Game Container */
.game-container {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

#game-canvas {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    background: rgba(0, 0, 0, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    touch-action: none;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
}

/* Game Overlays */
.game-overlay, .game-over {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
    
}

.game-over.hidden, .game-overlay.hidden {
    display: none;
}

.game-over-content, .game-start-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: scale(0.8);
    animation: modalAppear 0.3s ease forwards;
}

@keyframes modalAppear {
    to {
        transform: scale(1);
    }
}

.game-over-content h2, .game-start-content h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.game-over-content p, .game-start-content p {
    font-size: 1.2rem;
    margin-bottom: 20px;
}

#high-score-msg {
    color: #FFD700;
    font-weight: bold;
    animation: glow 1s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 5px #FFD700; }
    to { text-shadow: 0 0 20px #FFD700, 0 0 30px #FFD700; }
}

/* Instructions */
.instructions {
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 15px;
    
}

.instructions h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.instruction-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.instruction-item .key {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: bold;
    min-width: 60px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.instruction-item .desc {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header {
        flex-direction: column;
        gap: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .game-info {
        flex-direction: column;
        align-items: stretch;
    }

    .score-panel {
        justify-content: center;
    }

    .controls {
        justify-content: center;
    }

    #game-canvas {
        width: 100%;
        max-width: 350px;
        height: auto;
    }

    .mobile-controls {
        display: flex;
    }

    .instruction-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .score-panel {
        gap: 15px;
    }

    .score-item .value {
        font-size: 1.5rem;
    }

    .controls {
        flex-direction: column;
    }

    .control-btn {
        width: 100%;
    }
}

/* Snake and Food Styles (for canvas) */
.snake-segment {
    fill: #4CAF50;
    stroke: #2E7D32;
    stroke-width: 1;
}

.snake-head {
    fill: #66BB6A;
    stroke: #2E7D32;
    stroke-width: 2;
}

.food {
    fill: #FF5722;
    stroke: #D84315;
    stroke-width: 1;
}

/* Mobile Controls */
.mobile-controls {
    display: none;
    justify-content: center;
    margin: 20px 0;
}

.direction-pad {
    display: grid;
    grid-template-rows: 1fr 1fr 1fr;
    gap: 10px;
    align-items: center;
    justify-items: center;
}

.direction-row {
    display: flex;
    gap: 10px;
    align-items: center;
}

.direction-btn {
    width: 60px;
    height: 60px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

.direction-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.direction-btn:active {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(0.95);
}

.direction-btn.pause {
    background: rgba(255, 193, 7, 0.3);
    border-color: rgba(255, 193, 7, 0.5);
}

.direction-btn.pause:hover {
    background: rgba(255, 193, 7, 0.4);
}
