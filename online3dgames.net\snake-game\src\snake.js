
class SnakeGame {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.gridSize = 20;
        this.tileCount = canvas.width / this.gridSize;
        
        this.snake = [
            { x: Math.floor(this.tileCount / 2), y: Math.floor(this.tileCount / 2) }
        ];
        this.food = this.generateFood();
        this.dx = 0;
        this.dy = 0;
        this.score = 0;
        this.gameRunning = false;
        this.gamePaused = false;
        
        this.setupEventListeners();
        this.loadHighScore();
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (e) => {

            if (e.key === ' ') {
                e.preventDefault();
                if (this.gameRunning) {
                    this.togglePause();
                }
                return;
            }

            // Handle movement - only when game is running and not paused
            if (!this.gameRunning || this.gamePaused) {
                return;
            }
            
            switch(e.key) {
                case 'ArrowUp':
                case 'w':
                case 'W':
                    if (this.dy !== 1) {
                        this.dx = 0;
                        this.dy = -1;
                    }
                    break;
                case 'ArrowDown':
                case 's':
                case 'S':
                    if (this.dy !== -1) {
                        this.dx = 0;
                        this.dy = 1;
                    }
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    if (this.dx !== 1) {
                        this.dx = -1;
                        this.dy = 0;
                    }
                    break;
                case 'ArrowRight':
                case 'd':
                case 'D':
                    if (this.dx !== -1) {
                        this.dx = 1;
                        this.dy = 0;
                    }
                    break;
                case ' ':
                    e.preventDefault();
                    this.togglePause();
                    break;
                case 'r':
                case 'R':
                    this.reset();
                    break;
            }
        });
    }
    
    generateFood() {
        let food;
        do {
            food = {
                x: Math.floor(Math.random() * this.tileCount),
                y: Math.floor(Math.random() * this.tileCount)
            };
        } while (this.snake.some(segment => segment.x === food.x && segment.y === food.y));
        
        return food;
    }
    
    update() {
        if (!this.gameRunning || this.gamePaused) return;
        
        const head = { x: this.snake[0].x + this.dx, y: this.snake[0].y + this.dy };
        
        // Check wall collision
        if (head.x < 0 || head.x >= this.tileCount || head.y < 0 || head.y >= this.tileCount) {
            this.gameOver();
            return;
        }
        
        // Check self collision
        if (this.snake.some(segment => segment.x === head.x && segment.y === head.y)) {
            this.gameOver();
            return;
        }
        
        this.snake.unshift(head);
        
        // Check food collision
        if (head.x === this.food.x && head.y === this.food.y) {
            this.score += 10;
            this.food = this.generateFood();
            this.updateScore();
        } else {
            this.snake.pop();
        }
    }
    
    draw() {
        // Clear canvas completely
        this.ctx.fillStyle = '#000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw grid
        this.drawGrid();
        
        // Draw snake
        this.snake.forEach((segment, index) => {
            if (index === 0) {
                // Snake head
                this.ctx.fillStyle = '#66BB6A';
                this.ctx.strokeStyle = '#2E7D32';
                this.ctx.lineWidth = 2;
            } else {
                // Snake body
                this.ctx.fillStyle = '#4CAF50';
                this.ctx.strokeStyle = '#2E7D32';
                this.ctx.lineWidth = 1;
            }
            
            this.ctx.fillRect(
                segment.x * this.gridSize + 1,
                segment.y * this.gridSize + 1,
                this.gridSize - 2,
                this.gridSize - 2
            );
            this.ctx.strokeRect(
                segment.x * this.gridSize + 1,
                segment.y * this.gridSize + 1,
                this.gridSize - 2,
                this.gridSize - 2
            );
        });
        
        // Draw food
        this.ctx.fillStyle = '#FF5722';
        this.ctx.strokeStyle = '#D84315';
        this.ctx.lineWidth = 1;
        
        const foodX = this.food.x * this.gridSize + 1;
        const foodY = this.food.y * this.gridSize + 1;
        const foodSize = this.gridSize - 2;
        
        // Draw food as circle
        this.ctx.beginPath();
        this.ctx.arc(
            foodX + foodSize / 2,
            foodY + foodSize / 2,
            foodSize / 2 - 1,
            0,
            2 * Math.PI
        );
        this.ctx.fill();
        this.ctx.stroke();
    }
    
    drawGrid() {
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        
        for (let i = 0; i <= this.tileCount; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(i * this.gridSize, 0);
            this.ctx.lineTo(i * this.gridSize, this.canvas.height);
            this.ctx.stroke();
            
            this.ctx.beginPath();
            this.ctx.moveTo(0, i * this.gridSize);
            this.ctx.lineTo(this.canvas.width, i * this.gridSize);
            this.ctx.stroke();
        }
    }
    
    start() {
        this.gameRunning = true;
        this.gamePaused = false;
        // Set initial direction if not set
        if (this.dx === 0 && this.dy === 0) {
            this.dx = 1; // Start moving right
            this.dy = 0;
        }
        this.gameLoop();
    }
    
    pause() {
        this.gamePaused = true;
    }
    
    resume() {
        this.gamePaused = false;
        if (this.gameRunning) {
            this.gameLoop();
        }
    }
    
    togglePause() {
        if (this.gamePaused) {
            this.resume();
        } else {
            this.pause();
        }
    }
    
    reset() {
        this.snake = [{ x: Math.floor(this.tileCount / 2), y: Math.floor(this.tileCount / 2) }];
        this.food = this.generateFood();
        this.dx = 0;
        this.dy = 0;
        this.score = 0;
        this.gameRunning = false;
        this.gamePaused = false;
        this.updateScore();
        this.draw();
    }
    
    gameOver() {
        this.gameRunning = false;
        this.gamePaused = false;
        
        // Check for high score
        const highScore = this.getHighScore();
        if (this.score > highScore) {
            this.saveHighScore(this.score);
            document.getElementById('high-score-msg').classList.remove('hidden');
        } else {
            document.getElementById('high-score-msg').classList.add('hidden');
        }
        
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('game-over').classList.remove('hidden');
    }
    
    gameLoop() {
        if (!this.gameRunning || this.gamePaused) return;

        this.update();
        this.draw();

        if (this.gameRunning && !this.gamePaused) {
            setTimeout(() => this.gameLoop(), 150);
        }
    }
    
    updateScore() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('length').textContent = this.snake.length;
        document.getElementById('high-score').textContent = this.getHighScore();
    }
    
    getHighScore() {
        return parseInt(localStorage.getItem('snakeHighScore') || '0');
    }
    
    saveHighScore(score) {
        localStorage.setItem('snakeHighScore', score.toString());
    }
    
    loadHighScore() {
        document.getElementById('high-score').textContent = this.getHighScore();
    }
}
