// Reaction Test Game Logic
class ReactionTest {
    constructor() {
        this.testArea = document.getElementById('test-area');
        this.testMessage = document.getElementById('test-message');
        this.testInstruction = document.getElementById('test-instruction');
        
        this.state = 'waiting'; // waiting, ready, go, result
        this.startTime = 0;
        this.reactionTime = 0;
        this.timeout = null;
        
        this.results = [];
        this.loadResults();
        
        this.setupEventListeners();
        this.updateDisplay();
    }
    
    setupEventListeners() {
        this.testArea.addEventListener('click', (e) => {
            // 如果点击的是开始按钮，不处理测试区域的点击
            if (e.target.id === 'start-btn' || e.target.closest('#start-btn')) {
                return;
            }
            this.handleClick();
        });

        document.getElementById('start-btn').addEventListener('click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            this.startTest();
        });

        document.getElementById('reset-btn').addEventListener('click', () => {
            this.resetResults();
        });

        document.getElementById('toggle-results').addEventListener('click', () => {
            this.toggleResults();
        });
    }
    
    startTest() {
        if (this.state !== 'waiting') return;

        // Hide start button
        const startBtn = document.getElementById('start-btn');
        startBtn.classList.add('hidden');

        this.state = 'ready';
        this.testArea.className = 'test-area ready';
        this.testMessage.textContent = 'Get Ready...';
        this.testInstruction.textContent = 'Wait for green, don\'t click early!';

        // Random delay between 2-6 seconds
        const delay = 2000 + Math.random() * 4000;

        this.timeout = setTimeout(() => {
            this.showGo();
        }, delay);
    }
    
    showGo() {
        this.state = 'go';
        this.testArea.className = 'test-area go';
        this.testMessage.textContent = 'Click!';
        this.testInstruction.textContent = 'Click immediately!';
        this.startTime = Date.now();
    }
    
    handleClick() {
        switch (this.state) {
            case 'waiting':
                // 在waiting状态下，只有当开始按钮隐藏时才允许点击开始新测试
                const startBtn = document.getElementById('start-btn');
                if (startBtn.classList.contains('hidden')) {
                    this.startTest();
                }
                break;

            case 'ready':
                // Too early
                this.tooEarly();
                break;

            case 'go':
                // Good reaction
                this.recordReaction();
                break;

            case 'result':
                // Start new test
                this.resetTest();
                break;
        }
    }
    
    tooEarly() {
        if (this.timeout) {
            clearTimeout(this.timeout);
            this.timeout = null;
        }

        this.state = 'result';
        this.testArea.className = 'test-area too-early';
        this.testMessage.textContent = 'Too Early!';
        this.testInstruction.textContent = 'Wait for green next time! Click anywhere to try again.';
    }

    toggleResults() {
        const resultsSection = document.getElementById('results-section');
        const toggleBtn = document.getElementById('toggle-results');

        if (resultsSection.classList.contains('hidden')) {
            resultsSection.classList.remove('hidden');
            toggleBtn.textContent = 'Hide History';
        } else {
            resultsSection.classList.add('hidden');
            toggleBtn.textContent = 'Show History';
        }
    }
    
    recordReaction() {
        this.reactionTime = Date.now() - this.startTime;
        this.results.push({
            time: this.reactionTime,
            timestamp: new Date().toLocaleTimeString()
        });
        
        this.saveResults();
        this.showResult();
        this.updateDisplay();
        this.updateResultsList();
    }
    
    showResult() {
        this.state = 'result';
        this.testArea.className = 'test-area result';

        const rating = this.getRating(this.reactionTime);
        this.testMessage.textContent = `${this.reactionTime}ms`;
        this.testInstruction.textContent = `${rating.text}! Click anywhere to test again.`;
    }

    resetTest() {
        this.state = 'waiting';
        this.testArea.className = 'test-area waiting';
        this.testMessage.textContent = 'Ready to Test Your Reflexes?';
        this.testInstruction.textContent = 'Click the button below to start, then click when the area turns green!';

        // Show start button again
        const startBtn = document.getElementById('start-btn');
        startBtn.classList.remove('hidden');

        if (this.timeout) {
            clearTimeout(this.timeout);
            this.timeout = null;
        }
    }
    
    getRating(time) {
        if (time < 200) {
            return { text: 'Excellent', class: 'rating-excellent' };
        } else if (time < 300) {
            return { text: 'Good', class: 'rating-good' };
        } else if (time < 400) {
            return { text: 'Average', class: 'rating-average' };
        } else {
            return { text: 'Need to practice', class: 'rating-poor' };
        }
    }
    
    updateDisplay() {
        const currentTimeEl = document.getElementById('current-time');
        const averageTimeEl = document.getElementById('average-time');
        const bestTimeEl = document.getElementById('best-time');
        const testCountEl = document.getElementById('test-count');
        
        if (this.results.length === 0) {
            currentTimeEl.textContent = '-';
            averageTimeEl.textContent = '-';
            bestTimeEl.textContent = '-';
            testCountEl.textContent = '0';
        } else {
            const lastResult = this.results[this.results.length - 1];
            currentTimeEl.textContent = `${lastResult.time}ms`;
            
            const average = Math.round(this.results.reduce((sum, r) => sum + r.time, 0) / this.results.length);
            averageTimeEl.textContent = `${average}ms`;
            
            const best = Math.min(...this.results.map(r => r.time));
            bestTimeEl.textContent = `${best}ms`;
            
            testCountEl.textContent = this.results.length.toString();
        }
    }
    
    updateResultsList() {
        const resultsList = document.getElementById('results-list');
        
        if (this.results.length === 0) {
            resultsList.innerHTML = '<p class="no-results">No test records yet</p>';
            return;
        }
        
        const recentResults = this.results.slice(-10).reverse(); // Show last 10 results
        
        resultsList.innerHTML = recentResults.map(result => {
            const rating = this.getRating(result.time);
            return `
                <div class="result-item">
                    <span class="result-time">${result.time}ms</span>
                    <span class="result-rating ${rating.class}">${rating.text}</span>
                    <span class="result-timestamp">${result.timestamp}</span>
                </div>
            `;
        }).join('');
    }
    
    resetResults() {
        gameConfirm('Are you sure you want to clear all test history?', 'Clear History').then(result => {
            if (result) {
                this.results = [];
                this.saveResults();
                this.updateDisplay();
                this.updateResultsList();
                this.resetTest();
                gameSuccess('All test history has been cleared!', 'History Cleared');
            }
        });
    }
    
    saveResults() {
        localStorage.setItem('reactionTestResults', JSON.stringify(this.results));
    }
    
    loadResults() {
        const saved = localStorage.getItem('reactionTestResults');
        if (saved) {
            this.results = JSON.parse(saved);
        }
        this.updateResultsList();
    }
}
