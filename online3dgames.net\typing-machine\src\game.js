// Typing Machine UI Controller - This file is included for consistency
// The main logic is handled in typing-machine.js

// Additional keyboard shortcuts and UI enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + S to save
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            // Trigger save button click
            document.getElementById('save-btn').click();
        }
        
        // Ctrl/Cmd + A to select all (default behavior, but ensure it works)
        if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
            // Let default behavior handle this
        }
        
        // Escape to clear (with confirmation)
        if (e.key === 'Escape') {
            document.getElementById('clear-btn').click();
        }
        
        // F1 for new prompt
        if (e.key === 'F1') {
            e.preventDefault();
            document.getElementById('new-prompt-btn').click();
        }
    });
    
    // Add visual feedback for typing
    const typingArea = document.getElementById('typing-area');
    
    typingArea.addEventListener('focus', function() {
        this.style.boxShadow = '0 0 20px rgba(255, 107, 157, 0.3)';
    });
    
    typingArea.addEventListener('blur', function() {
        this.style.boxShadow = 'none';
    });
    
    // Auto-resize textarea
    typingArea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 400) + 'px';
    });
});
