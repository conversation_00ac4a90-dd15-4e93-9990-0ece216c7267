/**
 * Sudoku solver class
 * Uses backtracking to implement automatic solving and hinting
 */
class SudokuSolver {
    constructor() {
        this.board = null;
        this.solutions = [];
        this.maxSolutions = 2; // Used to check for unique solutions
    }
    
    /**
     * Solve Sudoku
     */
    solve(board) {
        // Deep copy input board
        this.board = board.map(row => [...row]);
        this.solutions = [];

        // First validate the validity of the input board
        if (!this.isValidBoard()) {
            return null;
        }

        // Use timeout mechanism to prevent infinite loop
        this.startTime = Date.now();
        this.maxSolveTime = 5000; // 5 seconds timeout, enough for Sudoku

        if (this.backtrackWithTimeout(0, 0)) {
            return this.board;
        }

        return null; // No solution or timeout
    }

    /**
     * Check the basic validity of the board
     */
    isValidBoard() {
        if (!this.board || this.board.length !== 9) return false;

        for (let row = 0; row < 9; row++) {
            if (!this.board[row] || this.board[row].length !== 9) return false;

            for (let col = 0; col < 9; col++) {
                const value = this.board[row][col];
                if (value !== 0) {
                    // Temporarily remove this value, check if it is valid
                    this.board[row][col] = 0;
                    const isValid = this.isValid(row, col, value);
                    this.board[row][col] = value;

                    if (!isValid) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * Backtracking algorithm with timeout
     */
    backtrackWithTimeout(row, col) {
        // Check timeout
        if (Date.now() - this.startTime > this.maxSolveTime) {
            console.warn('Sudoku solving timeout');
            return false;
        }

        // If we reach the next row, the solving is complete
        if (row === 9) {
            return true;
        }

        // If we reach the end of the row, move to the next row
        if (col === 9) {
            return this.backtrackWithTimeout(row + 1, 0);
        }

        // If the current cell already has a number, skip it
        if (this.board[row][col] !== 0) {
            return this.backtrackWithTimeout(row, col + 1);
        }

        // Try numbers 1-9
        for (let num = 1; num <= 9; num++) {
            if (this.isValid(row, col, num)) {
                this.board[row][col] = num;

                if (this.backtrackWithTimeout(row, col + 1)) {
                    return true;
                }

                this.board[row][col] = 0; // Backtrack
            }
        }

        return false;
    }
    
    /**
     * Backtracking algorithm core
     */
    backtrack(row, col) {
        // If we reach the next row, the solving is complete
        if (row === 9) {
            return true;
        }
        
        // If we reach the end of the row, move to the next row
        if (col === 9) {
            return this.backtrack(row + 1, 0);
        }
        
        // If the current cell already has a number, skip it
        if (this.board[row][col] !== 0) {
            return this.backtrack(row, col + 1);
        }
        
        // Try numbers 1-9
        for (let num = 1; num <= 9; num++) {
            if (this.isValid(row, col, num)) {
                this.board[row][col] = num;
                
                if (this.backtrack(row, col + 1)) {
                    return true;
                }
                
                this.board[row][col] = 0; // Backtrack
            }
        }
        
        return false;
    }
    
    /**
     * Check if placing a number in the specified position is valid
     */
    isValid(row, col, num) {
        // Ensure the board exists and is valid
        if (!this.board || !Array.isArray(this.board) || this.board.length !== 9) {
            return false;
        }

        // Check row
        for (let c = 0; c < 9; c++) {
            if (!this.board[row] || this.board[row][c] === num) {
                return false;
            }
        }

        // Check column
        for (let r = 0; r < 9; r++) {
            if (!this.board[r] || this.board[r][col] === num) {
                return false;
            }
        }

        // Check 3x3 box
        const boxRow = Math.floor(row / 3) * 3;
        const boxCol = Math.floor(col / 3) * 3;

        for (let r = boxRow; r < boxRow + 3; r++) {
            for (let c = boxCol; c < boxCol + 3; c++) {
                if (!this.board[r] || this.board[r][c] === num) {
                    return false;
                }
            }
        }

        return true;
    }
    
    /**
     * Get hint (find a number that can be filled in)
     */
    getHint(board) {
        try {
            // Deep copy board
            this.board = board.map(row => [...row]);

            // Find all empty cells
            const emptyCells = [];
            for (let row = 0; row < 9; row++) {
                for (let col = 0; col < 9; col++) {
                    if (this.board[row][col] === 0) {
                        emptyCells.push([row, col]);
                    }
                }
            }

            if (emptyCells.length === 0) {
                return null; // No empty cells
            }

            // Directly use the solver to get the complete solution, then select a hint from it
            const solutionBoard = this.board.map(row => [...row]);
            const solution = this.solve(solutionBoard);

            if (!solution) {
                console.log('Cannot solve current board');
                return null; // No solution
            }

            // Prioritize the position with the strongest constraints (position with the fewest possible values)
            let bestHint = null;
            let minOptions = 10;

            for (const [row, col] of emptyCells) {
                const options = this.getPossibleNumbers(row, col);
                if (options.length > 0 && options.length < minOptions) {
                    minOptions = options.length;
                    bestHint = {
                        row: row,
                        col: col,
                        number: solution[row][col]
                    };
                }
            }

            // If no best hint is found, return the answer of the first empty cell
            if (!bestHint) {
                const [row, col] = emptyCells[0];
                bestHint = {
                    row: row,
                    col: col,
                    number: solution[row][col]
                };
            }

            return bestHint;

        } catch (error) {
            console.error('Error getting hint:', error);
            return null;
        }
    }
    
    /**
     * Get possible numbers at the specified position
     */
    getPossibleNumbers(row, col) {
        // If the position already has a number, return an empty array
        if (!this.board || !this.board[row] || this.board[row][col] !== 0) {
            return [];
        }

        const possible = [];

        for (let num = 1; num <= 9; num++) {
            if (this.isValid(row, col, num)) {
                possible.push(num);
            }
        }

        console.log(`Possible numbers at (${row}, ${col}):`, possible);
        return possible;
    }
    
    /**
     * Check if Sudoku has a unique solution
     */
    hasUniqueSolution(board) {
        this.board = board.map(row => [...row]);
        this.solutions = [];
        
        this.findAllSolutions(0, 0);
        
        return this.solutions.length === 1;
    }
    
    /**
     * Find all solutions (used to check uniqueness)
     */
    findAllSolutions(row, col) {
        if (this.solutions.length >= this.maxSolutions) {
            return; // Enough solutions found
        }
        
        // If we reach the next row, a solution is found
        if (row === 9) {
            this.solutions.push(this.board.map(row => [...row]));
            return;
        }
        
        // If we reach the end of the row, move to the next row
        if (col === 9) {
            this.findAllSolutions(row + 1, 0);
            return;
        }
        
        // If the current cell already has a number, skip it
        if (this.board[row][col] !== 0) {
            this.findAllSolutions(row, col + 1);
            return;
        }
        
        // Try numbers 1-9
        for (let num = 1; num <= 9; num++) {
            if (this.isValid(row, col, num)) {
                this.board[row][col] = num;
                this.findAllSolutions(row, col + 1);
                this.board[row][col] = 0; // Backtrack
            }
        }
    }
    
    /**
     * Check if Sudoku is correctly completed
     */
    isValidSolution(board) {
        // Check if all cells are filled
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (board[row][col] === 0) {
                    return false;
                }
            }
        }
        
        // Check row
        for (let row = 0; row < 9; row++) {
            const seen = new Set();
            for (let col = 0; col < 9; col++) {
                const num = board[row][col];
                if (seen.has(num)) {
                    return false;
                }
                seen.add(num);
            }
        }
        
        // Check column
        for (let col = 0; col < 9; col++) {
            const seen = new Set();
            for (let row = 0; row < 9; row++) {
                const num = board[row][col];
                if (seen.has(num)) {
                    return false;
                }
                seen.add(num);
            }
        }
        
        // Check 3x3 box
        for (let boxRow = 0; boxRow < 9; boxRow += 3) {
            for (let boxCol = 0; boxCol < 9; boxCol += 3) {
                const seen = new Set();
                for (let row = boxRow; row < boxRow + 3; row++) {
                    for (let col = boxCol; col < boxCol + 3; col++) {
                        const num = board[row][col];
                        if (seen.has(num)) {
                            return false;
                        }
                        seen.add(num);
                    }
                }
            }
        }
        
        return true;
    }
    
    /**
     * Get Sudoku difficulty assessment
     */
    getDifficulty(board) {
        const emptyCells = board.flat().filter(cell => cell === 0).length;
        
        if (emptyCells <= 35) return 'easy';
        if (emptyCells <= 45) return 'medium';
        if (emptyCells <= 55) return 'hard';
        return 'expert';
    }
    
    /**
     * Step by step solving (for demonstration)
     */
    solveStep(board) {
        this.board = board.map(row => [...row]);
        
        // Find the first cell that can be determined
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (this.board[row][col] === 0) {
                    const possible = this.getPossibleNumbers(row, col);
                    if (possible.length === 1) {
                        return {
                            row: row,
                            col: col,
                            number: possible[0],
                            reason: 'The only possible number'
                        };
                    }
                }
            }
        }
        
        // If no uniquely determined cell, use a more complex strategy
        return this.getHint(board);
    }
    
    /**
     * Check if the current state has errors
     */
    hasErrors(board) {
        // Ensure the board exists and is valid
        if (!board || !Array.isArray(board) || board.length !== 9) {
            return true;
        }

        // Set the current board
        this.board = board.map(row => [...row]);

        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 9; col++) {
                if (this.board[row] && this.board[row][col] !== 0) {
                    const backup = this.board[row][col];
                    this.board[row][col] = 0;

                    if (!this.isValid(row, col, backup)) {
                        this.board[row][col] = backup;
                        return true;
                    }

                    this.board[row][col] = backup;
                }
            }
        }
        return false;
    }
}
