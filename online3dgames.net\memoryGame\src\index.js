const allCards = [
  { name: 'aquaman', img: 'aquaman.jpg' },
  { name: 'batman', img: 'batman.jpg' },
  { name: 'captain america', img: 'captain-america.jpg' },
  { name: 'fantastic four', img: 'fantastic-four.jpg' },
  { name: 'flash', img: 'flash.jpg' },
  { name: 'green arrow', img: 'green-arrow.jpg' },
  { name: 'green lantern', img: 'green-lantern.jpg' },
  { name: 'ironman', img: 'ironman.jpg' },
  { name: 'spiderman', img: 'spiderman.jpg' },
  { name: 'superman', img: 'superman.jpg' },
  { name: 'the avengers', img: 'the-avengers.jpg' },
  { name: 'thor', img: 'thor.jpg' }
];

const memoryGame = new MemoryGame(allCards);


function showModal(title, message, showNextButton = true) {
  let buttons;

  if (showNextButton && memoryGame.canAdvanceToNextLevel()) {
    buttons = [
      { text: 'Try Again', type: 'secondary', value: 'retry' },
      { text: 'Next Level', type: 'primary', value: 'next' }
    ];
  } else if (!showNextButton || memoryGame.currentLevel === memoryGame.maxLevel) {
    buttons = [
      { text: 'Try Again', type: 'secondary', value: 'retry' },
      { text: 'Restart', type: 'primary', value: 'restart' }
    ];
  } else {
    buttons = [
      { text: 'Try Again', type: 'secondary', value: 'retry' },
      { text: 'Next Level', type: 'primary', value: 'next' }
    ];
  }

  gameModal.show({
    title: title,
    content: message,
    type: 'success',
    buttons: buttons
  }).then(result => {
    if (result === 'retry') {
      retryCurrentLevel();
    } else if (result === 'next') {
      proceedToNext();
    } else if (result === 'restart') {
      memoryGame.resetGame();
      initializeGame();
    }
  });
}

function retryCurrentLevel() {
  memoryGame.resetLevelStats();
  memoryGame.setLevel(memoryGame.currentLevel);
  initializeGame();
}

function proceedToNext() {
  if (memoryGame.canAdvanceToNextLevel()) {
    memoryGame.advanceToNextLevel();
    initializeGame();
  } else {
    // Game completed or restart from beginning
    memoryGame.resetGame();
    initializeGame();
  }
}

// Function to update UI display
function updateUI() {
  document.getElementById('current-level').textContent = memoryGame.currentLevel;
  document.getElementById('pairs-clicked').textContent = memoryGame.pairsClicked;
  document.getElementById('pairs-guessed').textContent = memoryGame.pairsGuessed;
  document.getElementById('target-pairs').textContent = memoryGame.levelConfig[memoryGame.currentLevel].pairs;
}

// Function to initialize/reset the game board
function initializeGame() {
  // Set up the current level
  memoryGame.setLevel(memoryGame.currentLevel);

  // Update memory board class for current level
  const memoryBoard = document.querySelector('#memory-board');
  memoryBoard.className = ''; // Clear existing classes
  memoryBoard.classList.add(`level-${memoryGame.currentLevel}`);

  let html = '';
  memoryGame.cards.forEach((pic) => {
    html += `
      <div class="card" data-card-name="${pic.name}">
        <div class="back" name="${pic.img}"></div>
        <div class="front" style="background: url(/memoryGame/img/${pic.img}) no-repeat;background-size: 100%;"></div>
      </div>
    `;
  });

  // Add all the divs to the HTML
  memoryBoard.innerHTML = html;

  // Update UI display
  updateUI();

  // Bind the click event of each element to a function
  document.querySelectorAll('.card').forEach((card) => {
    card.addEventListener('click', handleCardClick);
  });
}

// Function to handle card clicks
function handleCardClick() {
  const card = this;

  // Prevent clicking on already turned cards or when 2 cards are already picked
  if (card.classList.contains('turned') || memoryGame.pickedCards.length >= 2) {
    return;
  }

  // Turn the card
  card.classList.add('turned');

  // Add card to picked cards
  memoryGame.pickedCards.push(card);

  // Check if we have 2 cards picked
  if (memoryGame.pickedCards.length === 2) {
    const card1 = memoryGame.pickedCards[0];
    const card2 = memoryGame.pickedCards[1];

    const card1Name = card1.getAttribute('data-card-name');
    const card2Name = card2.getAttribute('data-card-name');

    // Check if it's a pair
    const isMatch = memoryGame.checkIfPair(card1Name, card2Name);

    // Update UI display
    updateUI();

    if (!isMatch) {
      // If not a match, turn cards back after a delay
      setTimeout(() => {
        card1.classList.remove('turned');
        card2.classList.remove('turned');
        // Clear picked cards after turning them back
        memoryGame.pickedCards = [];
      }, 1000);
    } else {
      // If it's a match, clear picked cards immediately
      memoryGame.pickedCards = [];

      // Check if current level is finished
      if (memoryGame.checkIfFinished()) {
        setTimeout(() => {
          if (memoryGame.canAdvanceToNextLevel()) {
            // Can advance to next level
            const currentLevel = memoryGame.currentLevel;
            const levelName = memoryGame.levelConfig[currentLevel].description;
            const title = `🎉 Level ${currentLevel} Complete!`;
            const message = `Congratulations! You completed Level ${currentLevel} (${levelName})!\n\nLevel Stats:\n• Total clicks: ${memoryGame.pairsClicked}\n• Pairs matched: ${memoryGame.pairsGuessed}\n\nReady for the next challenge?`;
            showModal(title, message, true);
          } else {
            // Game completely finished
            const title = `🏆 Game Complete!`;
            const message = `Congratulations! You completed all levels!\n\nFinal Stats:\n• Total clicks: ${memoryGame.pairsClicked}\n• Pairs matched: ${memoryGame.pairsGuessed}\n\nYou are a true Memory Master!`;
            showModal(title, message, false);
          }
        }, 500);
      }
    }
  }
}

// Function to restart the game
function restartGame() {
  memoryGame.resetGame();
  initializeGame();
}

function goHome() {
  window.location.href = '/';
}

window.addEventListener('load', (event) => {
  // Initialize the game
  initializeGame();

  // Add restart button event listener
  document.getElementById('restart-btn').addEventListener('click', restartGame);

  // Add home button event listener
  document.getElementById('home-btn').addEventListener('click', goHome);


});
