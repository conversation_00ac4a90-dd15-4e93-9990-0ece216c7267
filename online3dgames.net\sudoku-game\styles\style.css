/* Sudoku Game Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
    margin-bottom: 20px;
}

.header h1 {
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin: 0;
}

.home-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.home-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.header-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.timer {
    font-size: 1.2rem;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.2);
    padding: 10px 15px;
    border-radius: 10px;
}

/* Game Info */
.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
    margin-bottom: 20px;
}

.score-panel {
    display: flex;
    gap: 30px;
}

.score-item {
    text-align: center;
}

.score-item .label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.score-item .value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
}

.controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.control-btn.primary {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.control-btn.secondary {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: white;
}

.control-btn.success {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
}

.control-btn.warning {
    background: linear-gradient(135deg, #9C27B0, #7B1FA2);
    color: white;
}

.control-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Difficulty Panel */
.difficulty-panel {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
    margin-bottom: 20px;
    text-align: center;
    display: none; /* Hidden by default */
}

.difficulty-panel h3 {
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.difficulty-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.difficulty-btn {
    padding: 10px 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.difficulty-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.difficulty-btn.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.6);
}

/* Game Container */
.game-container {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 30px;
    margin-bottom: 20px;
}

/* Sudoku Board */
.sudoku-board {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    display: grid;
    grid-template-columns: repeat(9, 1fr);
    grid-template-rows: repeat(9, 1fr);
    gap: 3px;
    aspect-ratio: 1;
    max-width: 600px;
    min-width: 450px;
    justify-self: center;
}

.sudoku-cell {
    background: white;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: bold;
    color: #333;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    min-height: 50px;
}

.sudoku-cell:hover {
    background: #f0f8ff;
}

.sudoku-cell.selected {
    background: #e3f2fd;
    border-color: #2196F3;
    box-shadow: 0 0 0 2px #2196F3;
}

.sudoku-cell.given {
    background: #f5f5f5;
    color: #000;
    font-weight: bold;
    cursor: default;
}

.sudoku-cell.conflict {
    background: #ffebee;
    color: #d32f2f;
    border-color: #d32f2f;
}

.sudoku-cell.hint {
    background: #e8f5e8;
    color: #2e7d32;
    animation: hintPulse 0.5s ease;
}

@keyframes hintPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 3x3 Box Borders */
.sudoku-cell:nth-child(3n) {
    border-right: 3px solid #333;
}

.sudoku-cell:nth-child(n+19):nth-child(-n+27),
.sudoku-cell:nth-child(n+46):nth-child(-n+54) {
    border-bottom: 3px solid #333;
}

/* Number Selector */
.number-selector {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    
    min-width: 200px;
}

.number-selector h3 {
    text-align: center;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.number-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.number-btn {
    aspect-ratio: 1;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.number-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.number-btn:active {
    transform: scale(0.95);
}

.number-btn.erase {
    background: rgba(244, 67, 54, 0.3);
    border-color: rgba(244, 67, 54, 0.5);
}

.number-btn.erase:hover {
    background: rgba(244, 67, 54, 0.4);
}

/* Action Controls */
.action-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
}

.action-btn {
    padding: 12px 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.action-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Game Overlays */
.game-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    
    cursor: pointer; /* Indicates clickable */
}

/* Modal content areas should not have click cursor */
.game-start-content,
.game-complete-content,
.game-failed-content,
.difficulty-modal-content {
    cursor: default;
}

/* Modal Header */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.modal-header h2 {
    margin: 0;
    flex: 1;
}

/* Close Button */
.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    line-height: 1;
    padding: 0;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

.close-btn:active {
    transform: scale(0.95);
}

.game-overlay.hidden {
    display: none;
}

.game-start-content, .game-complete-content, .game-failed-content, .difficulty-modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
}

.game-failed-content {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.difficulty-modal-content {
    max-width: 800px;
    width: 95%;
}

/* Difficulty Selection Modal */
.difficulty-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.difficulty-option {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    
}

.difficulty-option:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.difficulty-option.selected {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.7);
    transform: scale(1.05);
}

.difficulty-icon {
    font-size: 3rem;
    margin-bottom: 10px;
}

.difficulty-option h3 {
    margin: 10px 0;
    font-size: 1.3rem;
}

.difficulty-option p {
    margin: 5px 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.time-estimate {
    font-style: italic;
    opacity: 0.8 !important;
}

.difficulty-stars {
    margin-top: 10px;
    font-size: 1.2rem;
}

.modal-actions {
    margin-top: 20px;
}

.game-start-content h2, .game-complete-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.game-start-content p, .game-complete-content p {
    font-size: 1.2rem;
    margin-bottom: 20px;
    opacity: 0.9;
}

.tip {
    font-size: 1rem !important;
    opacity: 0.8 !important;
    margin-bottom: 30px !important;
}

/* Completion Stats */
.completion-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 30px 0;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
}

.completion-actions, .failed-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

.completion-actions .control-btn,
.failed-actions .control-btn {
    flex: 1;
    min-width: 100px;
    max-width: 150px;
}

/* Failed Game Styles */
.failed-tips {
    margin: 30px 0;
    text-align: left;
}

.tip-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
}

.tip-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.tip-text {
    font-size: 1rem;
    opacity: 0.9;
}

/* Instructions */
.instructions {
    background: rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 15px;
    
}

.instructions h3 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.5rem;
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.instruction-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
}

.instruction-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.instruction-text h4 {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.instruction-text p {
    opacity: 0.9;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header {
        flex-direction: column;
        gap: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .game-info {
        flex-direction: column;
        gap: 20px;
    }

    .score-panel {
        justify-content: center;
        gap: 20px;
    }

    .controls {
        flex-wrap: wrap;
        justify-content: center;
    }

    .difficulty-buttons {
        flex-wrap: wrap;
        gap: 8px;
    }

    .difficulty-btn {
        flex: 1;
        min-width: 80px;
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .game-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .sudoku-board {
        max-width: 100%;
        min-width: auto;
        padding: 15px;
    }

    .sudoku-cell {
        font-size: 1.4rem;
        min-height: 40px;
    }

    .difficulty-modal-content {
        max-width: 95%;
        padding: 30px 20px;
    }

    .difficulty-options {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .close-btn {
        width: 35px;
        height: 35px;
        font-size: 20px;
    }

    .number-selector {
        order: -1;
        min-width: auto;
    }

    .number-grid {
        grid-template-columns: repeat(5, 1fr);
    }

    .action-controls {
        flex-wrap: wrap;
        gap: 10px;
    }

    .action-btn {
        flex: 1;
        min-width: 100px;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .instruction-grid {
        grid-template-columns: 1fr;
    }

    .completion-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .completion-actions, .failed-actions {
        flex-direction: column;
        gap: 10px;
    }

    .completion-actions .control-btn,
    .failed-actions .control-btn {
        flex: none;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.8rem;
    }

    .score-panel {
        flex-direction: column;
        gap: 10px;
    }

    .score-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(255, 255, 255, 0.1);
        padding: 10px 15px;
        border-radius: 8px;
    }

    .score-item .label {
        margin-bottom: 0;
    }

    .score-item .value {
        font-size: 1.2rem;
    }

    .controls {
        grid-template-columns: repeat(2, 1fr);
        display: grid;
        gap: 8px;
    }

    .control-btn {
        padding: 10px 15px;
        font-size: 0.8rem;
    }

    .difficulty-buttons {
        grid-template-columns: repeat(2, 1fr);
        display: grid;
    }

    .sudoku-board {
        padding: 10px;
        gap: 1px;
    }

    .sudoku-cell {
        font-size: 1.2rem;
        min-height: 35px;
    }

    .difficulty-options {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .difficulty-option {
        padding: 15px;
    }

    .difficulty-icon {
        font-size: 2rem;
    }

    .number-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 8px;
    }

    .number-btn {
        font-size: 1.2rem;
        min-height: 45px;
    }

    .action-controls {
        grid-template-columns: repeat(2, 1fr);
        display: grid;
    }

    .game-start-content, .game-complete-content {
        padding: 30px 20px;
    }

    .game-start-content h2, .game-complete-content h2 {
        font-size: 2rem;
    }
}

/* Touch Optimizations */
@media (hover: none) and (pointer: coarse) {
    .sudoku-cell {
        min-height: 40px;
        font-size: 1.3rem;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    .number-btn {
        min-height: 50px;
        font-size: 1.4rem;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    .control-btn, .action-btn, .difficulty-btn, .close-btn {
        min-height: 44px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    .close-btn {
        min-width: 44px;
    }

    /* Increase touch target size */
    .sudoku-cell:hover {
        background: #e3f2fd;
    }

    .number-btn:hover {
        background: rgba(255, 255, 255, 0.3);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .sudoku-cell {
        border: 2px solid #000;
    }

    .sudoku-cell.selected {
        border-color: #0066cc;
        background: #e6f3ff;
    }

    .sudoku-cell.conflict {
        border-color: #cc0000;
        background: #ffe6e6;
    }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .sudoku-cell {
        transition: none;
    }

    .control-btn, .action-btn, .difficulty-btn, .number-btn {
        transition: none;
    }
}
