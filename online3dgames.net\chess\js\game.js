// Chess Game Logic
class ChessGame {
    constructor() {
        this.board = this.initializeBoard();
        this.currentPlayer = 'white';
        this.gameMode = 'vs-ai';
        this.aiDifficulty = 'medium';
        this.playerColor = 'white';
        this.selectedSquare = null;
        this.gameHistory = [];
        this.capturedPieces = { white: [], black: [] };
        this.gameOver = false;
        this.checkState = null;
        this.boardFlipped = false; // 添加棋盘翻转状态
        
        this.pieces = {
            white: {
                king: '♔', queen: '♕', rook: '♖', 
                bishop: '♗', knight: '♘', pawn: '♙'
            },
            black: {
                king: '♚', queen: '♛', rook: '♜',
                bishop: '♝', knight: '♞', pawn: '♟'
            }
        };

        // 棋子价值表
        this.pieceValues = {
            'p': 100, 'n': 320, 'b': 330, 'r': 500, 'q': 900, 'k': 20000
        };

        // 位置评估表
        this.positionTables = this.initializePositionTables();
        
        this.initializeGame();
    }
    
    initializeBoard() {
        // Standard chess starting position
        return [
            ['r', 'n', 'b', 'q', 'k', 'b', 'n', 'r'],
            ['p', 'p', 'p', 'p', 'p', 'p', 'p', 'p'],
            [null, null, null, null, null, null, null, null],
            [null, null, null, null, null, null, null, null],
            [null, null, null, null, null, null, null, null],
            [null, null, null, null, null, null, null, null],
            ['P', 'P', 'P', 'P', 'P', 'P', 'P', 'P'],
            ['R', 'N', 'B', 'Q', 'K', 'B', 'N', 'R']
        ];
    }

    initializePositionTables() {
        return {
            'p': [ // 兵的位置评估
                [0,  0,  0,  0,  0,  0,  0,  0],
                [50, 50, 50, 50, 50, 50, 50, 50],
                [10, 10, 20, 30, 30, 20, 10, 10],
                [5,  5, 10, 25, 25, 10,  5,  5],
                [0,  0,  0, 20, 20,  0,  0,  0],
                [5, -5,-10,  0,  0,-10, -5,  5],
                [5, 10, 10,-20,-20, 10, 10,  5],
                [0,  0,  0,  0,  0,  0,  0,  0]
            ],
            'n': [ // 马的位置评估
                [-50,-40,-30,-30,-30,-30,-40,-50],
                [-40,-20,  0,  0,  0,  0,-20,-40],
                [-30,  0, 10, 15, 15, 10,  0,-30],
                [-30,  5, 15, 20, 20, 15,  5,-30],
                [-30,  0, 15, 20, 20, 15,  0,-30],
                [-30,  5, 10, 15, 15, 10,  5,-30],
                [-40,-20,  0,  5,  5,  0,-20,-40],
                [-50,-40,-30,-30,-30,-30,-40,-50]
            ],
            'b': [ // 象的位置评估
                [-20,-10,-10,-10,-10,-10,-10,-20],
                [-10,  0,  0,  0,  0,  0,  0,-10],
                [-10,  0,  5, 10, 10,  5,  0,-10],
                [-10,  5,  5, 10, 10,  5,  5,-10],
                [-10,  0, 10, 10, 10, 10,  0,-10],
                [-10, 10, 10, 10, 10, 10, 10,-10],
                [-10,  5,  0,  0,  0,  0,  5,-10],
                [-20,-10,-10,-10,-10,-10,-10,-20]
            ],
            'r': [ // 车的位置评估
                [0,  0,  0,  0,  0,  0,  0,  0],
                [5, 10, 10, 10, 10, 10, 10,  5],
                [-5,  0,  0,  0,  0,  0,  0, -5],
                [-5,  0,  0,  0,  0,  0,  0, -5],
                [-5,  0,  0,  0,  0,  0,  0, -5],
                [-5,  0,  0,  0,  0,  0,  0, -5],
                [-5,  0,  0,  0,  0,  0,  0, -5],
                [0,  0,  0,  5,  5,  0,  0,  0]
            ],
            'q': [ // 后的位置评估
                [-20,-10,-10, -5, -5,-10,-10,-20],
                [-10,  0,  0,  0,  0,  0,  0,-10],
                [-10,  0,  5,  5,  5,  5,  0,-10],
                [-5,  0,  5,  5,  5,  5,  0, -5],
                [0,  0,  5,  5,  5,  5,  0, -5],
                [-10,  5,  5,  5,  5,  5,  0,-10],
                [-10,  0,  5,  0,  0,  0,  0,-10],
                [-20,-10,-10, -5, -5,-10,-10,-20]
            ],
            'k': [ // 王的位置评估（中局）
                [-30,-40,-40,-50,-50,-40,-40,-30],
                [-30,-40,-40,-50,-50,-40,-40,-30],
                [-30,-40,-40,-50,-50,-40,-40,-30],
                [-30,-40,-40,-50,-50,-40,-40,-30],
                [-20,-30,-30,-40,-40,-30,-30,-20],
                [-10,-20,-20,-20,-20,-20,-20,-10],
                [20, 20,  0,  0,  0,  0, 20, 20],
                [20, 30, 10,  0,  0, 10, 30, 20]
            ]
        };
    }
    
    initializeGame() {
        this.setupEventListeners();
        // Don't create chessboard until game starts
    }
    
    createChessboard() {
        const chessboard = document.getElementById('chessboard');
        chessboard.innerHTML = '';
        
        // 根据玩家颜色决定是否翻转棋盘
        this.boardFlipped = this.playerColor === 'black';
        
        // 添加或移除翻转类
        if (this.boardFlipped) {
            chessboard.classList.add('flipped');
        } else {
            chessboard.classList.remove('flipped');
        }
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const square = document.createElement('div');
                square.className = `chess-square ${(row + col) % 2 === 0 ? 'white' : 'black'}`;
                square.dataset.row = row;
                square.dataset.col = col;
                square.addEventListener('click', (e) => this.handleSquareClick(e));
                
                const piece = this.board[row][col];
                if (piece) {
                    const pieceElement = document.createElement('div');
                    pieceElement.className = `chess-piece ${this.getPieceColor(piece)}`;
                    pieceElement.textContent = this.getPieceSymbol(piece);
                    square.appendChild(pieceElement);
                }
                
                chessboard.appendChild(square);
            }
        }
    }
    
    getPieceColor(piece) {
        return piece === piece.toUpperCase() ? 'white' : 'black';
    }
    
    getPieceSymbol(piece) {
        const color = this.getPieceColor(piece);
        const type = piece.toLowerCase();
        
        const pieceMap = {
            'k': 'king', 'q': 'queen', 'r': 'rook',
            'b': 'bishop', 'n': 'knight', 'p': 'pawn'
        };
        
        return this.pieces[color][pieceMap[type]] || '';
    }
    
    handleSquareClick(event) {
        if (this.gameOver) return;
        
        const square = event.currentTarget;
        const row = parseInt(square.dataset.row);
        const col = parseInt(square.dataset.col);
        
        if (this.selectedSquare) {
            // Try to make a move
            const fromRow = this.selectedSquare.row;
            const fromCol = this.selectedSquare.col;
            
            if (this.isValidMove(fromRow, fromCol, row, col)) {
                this.makeMove(fromRow, fromCol, row, col);
                this.clearSelection();
            } else {
                this.clearSelection();
                this.selectSquare(row, col);
            }
        } else {
            this.selectSquare(row, col);
        }
    }
    
    selectSquare(row, col) {
        const piece = this.board[row][col];
        if (piece && this.getPieceColor(piece) === this.currentPlayer) {
            this.selectedSquare = { row, col };
            this.highlightSquare(row, col);
            this.showPossibleMoves(row, col);
        }
    }
    
    clearSelection() {
        this.selectedSquare = null;
        this.clearHighlights();
    }
    
    highlightSquare(row, col) {
        const square = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        square.classList.add('selected');
    }
    
    clearHighlights() {
        document.querySelectorAll('.chess-square').forEach(square => {
            square.classList.remove('selected', 'possible-move', 'capture-move');
        });
    }
    
    showPossibleMoves(row, col) {
        const moves = this.getPossibleMoves(row, col);
        moves.forEach(move => {
            const square = document.querySelector(`[data-row="${move.row}"][data-col="${move.col}"]`);
            if (this.board[move.row][move.col]) {
                square.classList.add('capture-move');
            } else {
                square.classList.add('possible-move');
            }
        });
    }
    
    getPossibleMoves(row, col) {
        const piece = this.board[row][col];
        if (!piece) return [];
        
        const moves = [];
        const pieceType = piece.toLowerCase();
        const color = this.getPieceColor(piece);
        
        switch (pieceType) {
            case 'p':
                moves.push(...this.getPawnMoves(row, col, color));
                break;
            case 'r':
                moves.push(...this.getRookMoves(row, col));
                break;
            case 'n':
                moves.push(...this.getKnightMoves(row, col));
                break;
            case 'b':
                moves.push(...this.getBishopMoves(row, col));
                break;
            case 'q':
                moves.push(...this.getQueenMoves(row, col));
                break;
            case 'k':
                moves.push(...this.getKingMoves(row, col));
                break;
        }
        
        // Filter moves based on check state
        return moves.filter(move => {
            if (!this.isBasicValidMove(row, col, move.row, move.col)) return false;
            
            // If the current player is in check, only allow moves that get out of check
            if (this.isKingInCheck(this.board, this.currentPlayer)) {
                return this.isMoveValidInCheck(this.board, row, col, move.row, move.col);
            }
            
            return true;
        });
    }
    
    getPawnMoves(row, col, color) {
        const moves = [];
        const direction = color === 'white' ? -1 : 1;
        const startRow = color === 'white' ? 6 : 1;
        
        // Forward move
        if (this.isInBounds(row + direction, col) && !this.board[row + direction][col]) {
            moves.push({ row: row + direction, col });
            
            // Double forward from starting position
            if (row === startRow && !this.board[row + 2 * direction][col]) {
                moves.push({ row: row + 2 * direction, col });
            }
        }
        
        // Capture diagonally
        [-1, 1].forEach(dcol => {
            const newRow = row + direction;
            const newCol = col + dcol;
            if (this.isInBounds(newRow, newCol)) {
                const target = this.board[newRow][newCol];
                if (target && this.getPieceColor(target) !== color) {
                    moves.push({ row: newRow, col: newCol });
                }
            }
        });
        
        return moves;
    }
    
    getRookMoves(row, col) {
        const moves = [];
        const directions = [[0, 1], [0, -1], [1, 0], [-1, 0]];
        
        directions.forEach(([drow, dcol]) => {
            for (let i = 1; i < 8; i++) {
                const newRow = row + i * drow;
                const newCol = col + i * dcol;
                
                if (!this.isInBounds(newRow, newCol)) break;
                
                const target = this.board[newRow][newCol];
                if (!target) {
                    moves.push({ row: newRow, col: newCol });
                } else {
                    if (this.getPieceColor(target) !== this.getPieceColor(this.board[row][col])) {
                        moves.push({ row: newRow, col: newCol });
                    }
                    break;
                }
            }
        });
        
        return moves;
    }
    
    getKnightMoves(row, col) {
        const moves = [];
        const knightMoves = [
            [-2, -1], [-2, 1], [-1, -2], [-1, 2],
            [1, -2], [1, 2], [2, -1], [2, 1]
        ];
        
        knightMoves.forEach(([drow, dcol]) => {
            const newRow = row + drow;
            const newCol = col + dcol;
            
            if (this.isInBounds(newRow, newCol)) {
                const target = this.board[newRow][newCol];
                if (!target || this.getPieceColor(target) !== this.getPieceColor(this.board[row][col])) {
                    moves.push({ row: newRow, col: newCol });
                }
            }
        });
        
        return moves;
    }
    
    getBishopMoves(row, col) {
        const moves = [];
        const directions = [[1, 1], [1, -1], [-1, 1], [-1, -1]];
        
        directions.forEach(([drow, dcol]) => {
            for (let i = 1; i < 8; i++) {
                const newRow = row + i * drow;
                const newCol = col + i * dcol;
                
                if (!this.isInBounds(newRow, newCol)) break;
                
                const target = this.board[newRow][newCol];
                if (!target) {
                    moves.push({ row: newRow, col: newCol });
                } else {
                    if (this.getPieceColor(target) !== this.getPieceColor(this.board[row][col])) {
                        moves.push({ row: newRow, col: newCol });
                    }
                    break;
                }
            }
        });
        
        return moves;
    }
    
    getQueenMoves(row, col) {
        return [...this.getRookMoves(row, col), ...this.getBishopMoves(row, col)];
    }
    
    getKingMoves(row, col) {
        const moves = [];
        const directions = [
            [-1, -1], [-1, 0], [-1, 1],
            [0, -1],           [0, 1],
            [1, -1],  [1, 0],  [1, 1]
        ];
        
        directions.forEach(([drow, dcol]) => {
            const newRow = row + drow;
            const newCol = col + dcol;
            
            if (this.isInBounds(newRow, newCol)) {
                const target = this.board[newRow][newCol];
                if (!target || this.getPieceColor(target) !== this.getPieceColor(this.board[row][col])) {
                    moves.push({ row: newRow, col: newCol });
                }
            }
        });
        
        return moves;
    }
    
    isInBounds(row, col) {
        return row >= 0 && row < 8 && col >= 0 && col < 8;
    }
    
    isBasicValidMove(fromRow, fromCol, toRow, toCol) {
        // Basic bounds check
        if (!this.isInBounds(toRow, toCol)) return false;
        
        const piece = this.board[fromRow][fromCol];
        if (!piece) return false;
        
        const pieceColor = this.getPieceColor(piece);
        const target = this.board[toRow][toCol];
        if (target && this.getPieceColor(target) === pieceColor) return false;
        
        return true;
    }
    
    isValidMove(fromRow, fromCol, toRow, toCol) {
        // Basic bounds check
        if (!this.isInBounds(toRow, toCol)) return false;
        
        const piece = this.board[fromRow][fromCol];
        if (!piece) return false;
        
        const pieceColor = this.getPieceColor(piece);
        if (pieceColor !== this.currentPlayer) return false;
        
        const target = this.board[toRow][toCol];
        if (target && this.getPieceColor(target) === pieceColor) return false;
        
        // Check if move follows piece movement rules
        return this.isValidPieceMove(fromRow, fromCol, toRow, toCol);
    }
    
    isValidPieceMove(fromRow, fromCol, toRow, toCol) {
        const piece = this.board[fromRow][fromCol];
        const pieceType = piece.toLowerCase();
        const color = this.getPieceColor(piece);
        
        switch (pieceType) {
            case 'p':
                return this.isValidPawnMove(fromRow, fromCol, toRow, toCol, color);
            case 'r':
                return this.isValidRookMove(fromRow, fromCol, toRow, toCol);
            case 'n':
                return this.isValidKnightMove(fromRow, fromCol, toRow, toCol);
            case 'b':
                return this.isValidBishopMove(fromRow, fromCol, toRow, toCol);
            case 'q':
                return this.isValidQueenMove(fromRow, fromCol, toRow, toCol);
            case 'k':
                return this.isValidKingMove(fromRow, fromCol, toRow, toCol);
            default:
                return false;
        }
    }
    
    isValidPawnMove(fromRow, fromCol, toRow, toCol, color) {
        const direction = color === 'white' ? -1 : 1;
        const startRow = color === 'white' ? 6 : 1;
        const rowDiff = toRow - fromRow;
        const colDiff = Math.abs(toCol - fromCol);
        
        // Forward move
        if (colDiff === 0) {
            if (rowDiff === direction && !this.board[toRow][toCol]) return true;
            if (fromRow === startRow && rowDiff === 2 * direction && !this.board[toRow][toCol]) return true;
        }
        // Diagonal capture
        else if (colDiff === 1 && rowDiff === direction && this.board[toRow][toCol]) {
            return true;
        }
        
        return false;
    }
    
    isValidRookMove(fromRow, fromCol, toRow, toCol) {
        if (fromRow !== toRow && fromCol !== toCol) return false;
        return this.isPathClear(fromRow, fromCol, toRow, toCol);
    }
    
    isValidKnightMove(fromRow, fromCol, toRow, toCol) {
        const rowDiff = Math.abs(toRow - fromRow);
        const colDiff = Math.abs(toCol - fromCol);
        return (rowDiff === 2 && colDiff === 1) || (rowDiff === 1 && colDiff === 2);
    }
    
    isValidBishopMove(fromRow, fromCol, toRow, toCol) {
        if (Math.abs(toRow - fromRow) !== Math.abs(toCol - fromCol)) return false;
        return this.isPathClear(fromRow, fromCol, toRow, toCol);
    }
    
    isValidQueenMove(fromRow, fromCol, toRow, toCol) {
        return this.isValidRookMove(fromRow, fromCol, toRow, toCol) || 
               this.isValidBishopMove(fromRow, fromCol, toRow, toCol);
    }
    
    isValidKingMove(fromRow, fromCol, toRow, toCol) {
        const rowDiff = Math.abs(toRow - fromRow);
        const colDiff = Math.abs(toCol - fromCol);
        return rowDiff <= 1 && colDiff <= 1;
    }
    
    isPathClear(fromRow, fromCol, toRow, toCol) {
        const rowDir = toRow > fromRow ? 1 : toRow < fromRow ? -1 : 0;
        const colDir = toCol > fromCol ? 1 : toCol < fromCol ? -1 : 0;
        
        let currentRow = fromRow + rowDir;
        let currentCol = fromCol + colDir;
        
        while (currentRow !== toRow || currentCol !== toCol) {
            if (this.board[currentRow][currentCol]) return false;
            currentRow += rowDir;
            currentCol += colDir;
        }
        
        return true;
    }
    
    makeMove(fromRow, fromCol, toRow, toCol, isAnimated = true) {
        const piece = this.board[fromRow][fromCol];
        const capturedPiece = this.board[toRow][toCol];
        
        if (isAnimated) {
            this.animateMove(fromRow, fromCol, toRow, toCol, () => {
                this.completeMoveLogic(fromRow, fromCol, toRow, toCol, piece, capturedPiece);
            });
        } else {
            this.completeMoveLogic(fromRow, fromCol, toRow, toCol, piece, capturedPiece);
        }
    }
    
    animateMove(fromRow, fromCol, toRow, toCol, callback) {
        const fromSquare = document.querySelector(`[data-row="${fromRow}"][data-col="${fromCol}"]`);
        const toSquare = document.querySelector(`[data-row="${toRow}"][data-col="${toCol}"]`);
        const pieceElement = fromSquare.querySelector('.chess-piece');
        
        if (!pieceElement) {
            callback();
            return;
        }
        
        // Calculate positions relative to the chessboard container
        const chessboard = document.getElementById('chessboard');
        const boardRect = chessboard.getBoundingClientRect();
        const fromRect = fromSquare.getBoundingClientRect();
        const toRect = toSquare.getBoundingClientRect();
        
        // Create a flying piece for animation
        const flyingPiece = pieceElement.cloneNode(true);
        flyingPiece.style.position = 'fixed';
        flyingPiece.style.left = (fromRect.left + fromRect.width/2 - pieceElement.offsetWidth/2) + 'px';
        flyingPiece.style.top = (fromRect.top + fromRect.height/2 - pieceElement.offsetHeight/2) + 'px';
        flyingPiece.style.zIndex = '1000';
        flyingPiece.style.pointerEvents = 'none';
        flyingPiece.style.transition = 'all 0.3s ease-out';
        flyingPiece.classList.remove('moving'); // Remove to avoid CSS conflicts
        
        document.body.appendChild(flyingPiece);
        
        // Hide original piece
        pieceElement.style.opacity = '0';
        
        // Calculate final position
        const finalX = toRect.left + toRect.width/2 - pieceElement.offsetWidth/2;
        const finalY = toRect.top + toRect.height/2 - pieceElement.offsetHeight/2;
        
        // Animate the flying piece
        requestAnimationFrame(() => {
            flyingPiece.style.left = finalX + 'px';
            flyingPiece.style.top = finalY + 'px';
            flyingPiece.style.transform = 'scale(1.1)';
        });
        
        // Complete the move after animation
        setTimeout(() => {
            if (document.body.contains(flyingPiece)) {
                document.body.removeChild(flyingPiece);
            }
            pieceElement.style.opacity = '1';
            pieceElement.classList.remove('moving');
            callback();
        }, 300);
    }
    
    completeMoveLogic(fromRow, fromCol, toRow, toCol, piece, capturedPiece) {
        // Record move in history
        this.gameHistory.push({
            from: { row: fromRow, col: fromCol },
            to: { row: toRow, col: toCol },
            piece,
            capturedPiece,
            board: this.board.map(row => [...row])
        });
        
        // Handle captured piece
        if (capturedPiece) {
            const capturedColor = this.getPieceColor(capturedPiece);
            const oppositeColor = capturedColor === 'white' ? 'black' : 'white';
            this.capturedPieces[oppositeColor].push(capturedPiece);
            this.updateCapturedPieces();
        }
        
        // Make the move
        this.board[toRow][toCol] = piece;
        this.board[fromRow][fromCol] = null;
        
        // Check for pawn promotion
        if (piece.toLowerCase() === 'p') {
            if ((this.getPieceColor(piece) === 'white' && toRow === 0) ||
                (this.getPieceColor(piece) === 'black' && toRow === 7)) {
                this.board[toRow][toCol] = this.getPieceColor(piece) === 'white' ? 'Q' : 'q';
            }
        }
        
        // Switch turns
        this.currentPlayer = this.currentPlayer === 'white' ? 'black' : 'white';
        
        // Update check state
        this.updateCheckState();
        
        // Update display
        this.createChessboard();
        this.updateGameStatus();
        this.checkGameEnd();
        
        // Check if AI should move next
        if (this.gameMode === 'vs-ai' && this.currentPlayer !== this.playerColor && !this.gameOver) {
            setTimeout(() => this.makeAIMove(), 400);
        }
    }
    
    updateCapturedPieces() {
        ['white', 'black'].forEach(color => {
            const container = document.getElementById(`${color}-captured`);
            container.innerHTML = '';
            this.capturedPieces[color].forEach(piece => {
                const pieceElement = document.createElement('span');
                pieceElement.className = 'captured-piece';
                pieceElement.textContent = this.getPieceSymbol(piece);
                container.appendChild(pieceElement);
            });
        });
    }
    
    makeAIMove() {
        if (this.gameOver || this.currentPlayer === this.playerColor) return;
        
        // 显示AI思考提示
        this.showAIThinking(true);
        
        // 使用setTimeout让界面有时间更新
        setTimeout(() => {
            const move = this.getAIMove();
            this.showAIThinking(false);
            
            if (move) {
                this.makeMove(move.from.row, move.from.col, move.to.row, move.to.col);
            }
        }, 100);
    }

    showAIThinking(isThinking) {
        const status = document.getElementById('game-status');
        if (isThinking) {
            status.textContent = 'AI is thinking...';
            status.style.background = 'rgba(255, 165, 0, 0.6)';
        } else {
            this.updateGameStatus();
        }
    }
    
    getAIMove() {
        const depth = this.getSearchDepth();
        const result = this.minimax(this.board, depth, -Infinity, Infinity, this.currentPlayer === 'white');
        return result.move;
    }

    getSearchDepth() {
        switch (this.aiDifficulty) {
            case 'easy': return 2;
            case 'medium': return 3;
            case 'hard': return 4;
            default: return 3;
        }
    }

    minimax(board, depth, alpha, beta, isMaximizing) {
        if (depth === 0) {
            return { score: this.evaluateBoard(board), move: null };
        }

        const allMoves = this.getAllPossibleMoves(board, isMaximizing ? 'white' : 'black');
        
        if (allMoves.length === 0) {
            // 如果没有可行移动，返回极值
            return { score: isMaximizing ? -Infinity : Infinity, move: null };
        }

        let bestMove = null;
        let bestScore = isMaximizing ? -Infinity : Infinity;

        for (const move of allMoves) {
            // 模拟移动
            const originalPiece = board[move.to.row][move.to.col];
            board[move.to.row][move.to.col] = board[move.from.row][move.from.col];
            board[move.from.row][move.from.col] = null;

            // 递归搜索
            const result = this.minimax(board, depth - 1, alpha, beta, !isMaximizing);
            const score = result.score;

            // 撤销移动
            board[move.from.row][move.from.col] = board[move.to.row][move.to.col];
            board[move.to.row][move.to.col] = originalPiece;

            if (isMaximizing) {
                if (score > bestScore) {
                    bestScore = score;
                    bestMove = move;
                }
                alpha = Math.max(alpha, score);
            } else {
                if (score < bestScore) {
                    bestScore = score;
                    bestMove = move;
                }
                beta = Math.min(beta, score);
            }

            // Alpha-beta剪枝
            if (beta <= alpha) {
                break;
            }
        }

        return { score: bestScore, move: bestMove };
    }

    getAllPossibleMoves(board, color) {
        const allMoves = [];
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const piece = board[row][col];
                if (piece && this.getPieceColor(piece) === color) {
                    const moves = this.getPossibleMovesForBoard(board, row, col);
                    moves.forEach(move => {
                        allMoves.push({
                            from: { row, col },
                            to: { row: move.row, col: move.col },
                            piece,
                            target: board[move.row][move.col]
                        });
                    });
                }
            }
        }
        
        return allMoves;
    }

    getPossibleMovesForBoard(board, row, col) {
        const piece = board[row][col];
        if (!piece) return [];
        
        const moves = [];
        const pieceType = piece.toLowerCase();
        const color = this.getPieceColor(piece);
        
        switch (pieceType) {
            case 'p':
                moves.push(...this.getPawnMovesForBoard(board, row, col, color));
                break;
            case 'r':
                moves.push(...this.getRookMovesForBoard(board, row, col));
                break;
            case 'n':
                moves.push(...this.getKnightMovesForBoard(board, row, col));
                break;
            case 'b':
                moves.push(...this.getBishopMovesForBoard(board, row, col));
                break;
            case 'q':
                moves.push(...this.getQueenMovesForBoard(board, row, col));
                break;
            case 'k':
                moves.push(...this.getKingMovesForBoard(board, row, col));
                break;
        }
        
        return moves.filter(move => this.isBasicValidMoveForBoard(board, row, col, move.row, move.col));
    }

    evaluateBoard(board) {
        let score = 0;
        let whitePieces = 0;
        let blackPieces = 0;
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const piece = board[row][col];
                if (piece) {
                    const color = this.getPieceColor(piece);
                    const pieceType = piece.toLowerCase();
                    const pieceValue = this.pieceValues[pieceType] || 0;
                    
                    if (color === 'white') whitePieces++;
                    else blackPieces++;
                    
                    // 基础棋子价值
                    let pieceScore = pieceValue;
                    
                    // 位置奖励
                    if (this.positionTables[pieceType]) {
                        const positionValue = color === 'white' ? 
                            this.positionTables[pieceType][row][col] :
                            this.positionTables[pieceType][7-row][col];
                        pieceScore += positionValue;
                    }
                    
                    // 中心控制奖励
                    if ((row >= 3 && row <= 4) && (col >= 3 && col <= 4)) {
                        pieceScore += 10;
                    }
                    
                    // 根据颜色调整分数
                    if (color === 'white') {
                        score += pieceScore;
                    } else {
                        score -= pieceScore;
                    }
                }
            }
        }
        
        // 材料优势奖励
        const materialDifference = whitePieces - blackPieces;
        score += materialDifference * 20;
        
        // Check状态评估
        const whiteInCheck = this.isKingInCheck(board, 'white');
        const blackInCheck = this.isKingInCheck(board, 'black');
        
        if (whiteInCheck) {
            score -= 500; // 白棋被将军，黑棋优势
        }
        if (blackInCheck) {
            score += 500; // 黑棋被将军，白棋优势
        }
        
        // Checkmate评估
        if (whiteInCheck && this.isCheckmate(board, 'white')) {
            score = -Infinity; // 白棋被将死
        }
        if (blackInCheck && this.isCheckmate(board, 'black')) {
            score = Infinity; // 黑棋被将死
        }
        
        // 根据AI难度调整评估的准确性
        if (this.aiDifficulty === 'easy') {
            // 简单模式：添加随机性
            score += (Math.random() - 0.5) * 200;
        } else if (this.aiDifficulty === 'medium') {
            // 中等模式：轻微随机性
            score += (Math.random() - 0.5) * 50;
        }
        // 困难模式：完全准确的评估
        
        return score;
    }
    
    // 为AI算法添加的专用移动生成函数
    getPawnMovesForBoard(board, row, col, color) {
        const moves = [];
        const direction = color === 'white' ? -1 : 1;
        const startRow = color === 'white' ? 6 : 1;
        
        // Forward move
        if (this.isInBounds(row + direction, col) && !board[row + direction][col]) {
            moves.push({ row: row + direction, col });
            
            // Double forward from starting position
            if (row === startRow && !board[row + 2 * direction][col]) {
                moves.push({ row: row + 2 * direction, col });
            }
        }
        
        // Capture diagonally
        [-1, 1].forEach(dcol => {
            const newRow = row + direction;
            const newCol = col + dcol;
            if (this.isInBounds(newRow, newCol)) {
                const target = board[newRow][newCol];
                if (target && this.getPieceColor(target) !== color) {
                    moves.push({ row: newRow, col: newCol });
                }
            }
        });
        
        return moves;
    }
    
    getRookMovesForBoard(board, row, col) {
        const moves = [];
        const directions = [[0, 1], [0, -1], [1, 0], [-1, 0]];
        
        directions.forEach(([drow, dcol]) => {
            for (let i = 1; i < 8; i++) {
                const newRow = row + i * drow;
                const newCol = col + i * dcol;
                
                if (!this.isInBounds(newRow, newCol)) break;
                
                const target = board[newRow][newCol];
                if (!target) {
                    moves.push({ row: newRow, col: newCol });
                } else {
                    if (this.getPieceColor(target) !== this.getPieceColor(board[row][col])) {
                        moves.push({ row: newRow, col: newCol });
                    }
                    break;
                }
            }
        });
        
        return moves;
    }
    
    getKnightMovesForBoard(board, row, col) {
        const moves = [];
        const knightMoves = [
            [-2, -1], [-2, 1], [-1, -2], [-1, 2],
            [1, -2], [1, 2], [2, -1], [2, 1]
        ];
        
        knightMoves.forEach(([drow, dcol]) => {
            const newRow = row + drow;
            const newCol = col + dcol;
            
            if (this.isInBounds(newRow, newCol)) {
                const target = board[newRow][newCol];
                if (!target || this.getPieceColor(target) !== this.getPieceColor(board[row][col])) {
                    moves.push({ row: newRow, col: newCol });
                }
            }
        });
        
        return moves;
    }
    
    getBishopMovesForBoard(board, row, col) {
        const moves = [];
        const directions = [[1, 1], [1, -1], [-1, 1], [-1, -1]];
        
        directions.forEach(([drow, dcol]) => {
            for (let i = 1; i < 8; i++) {
                const newRow = row + i * drow;
                const newCol = col + i * dcol;
                
                if (!this.isInBounds(newRow, newCol)) break;
                
                const target = board[newRow][newCol];
                if (!target) {
                    moves.push({ row: newRow, col: newCol });
                } else {
                    if (this.getPieceColor(target) !== this.getPieceColor(board[row][col])) {
                        moves.push({ row: newRow, col: newCol });
                    }
                    break;
                }
            }
        });
        
        return moves;
    }
    
    getQueenMovesForBoard(board, row, col) {
        return [...this.getRookMovesForBoard(board, row, col), ...this.getBishopMovesForBoard(board, row, col)];
    }
    
    getKingMovesForBoard(board, row, col) {
        const moves = [];
        const directions = [
            [-1, -1], [-1, 0], [-1, 1],
            [0, -1],           [0, 1],
            [1, -1],  [1, 0],  [1, 1]
        ];
        
        directions.forEach(([drow, dcol]) => {
            const newRow = row + drow;
            const newCol = col + dcol;
            
            if (this.isInBounds(newRow, newCol)) {
                const target = board[newRow][newCol];
                if (!target || this.getPieceColor(target) !== this.getPieceColor(board[row][col])) {
                    moves.push({ row: newRow, col: newCol });
                }
            }
        });
        
        return moves;
    }
    
    isBasicValidMoveForBoard(board, fromRow, fromCol, toRow, toCol) {
        // Basic bounds check
        if (!this.isInBounds(toRow, toCol)) return false;
        
        const piece = board[fromRow][fromCol];
        if (!piece) return false;
        
        const pieceColor = this.getPieceColor(piece);
        const target = board[toRow][toCol];
        if (target && this.getPieceColor(target) === pieceColor) return false;
        
        return true;
    }

    // Check detection methods
    isKingInCheck(board, color) {
        // Find the king's position
        const kingPiece = color === 'white' ? 'K' : 'k';
        let kingRow = -1, kingCol = -1;
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                if (board[row][col] === kingPiece) {
                    kingRow = row;
                    kingCol = col;
                    break;
                }
            }
            if (kingRow !== -1) break;
        }
        
        if (kingRow === -1) return false; // King not found
        
        // Check if any opponent piece can attack the king
        const opponentColor = color === 'white' ? 'black' : 'white';
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const piece = board[row][col];
                if (piece && this.getPieceColor(piece) === opponentColor) {
                    if (this.canPieceAttackSquare(board, row, col, kingRow, kingCol)) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    canPieceAttackSquare(board, fromRow, fromCol, toRow, toCol) {
        const piece = board[fromRow][fromCol];
        if (!piece) return false;
        
        const pieceType = piece.toLowerCase();
        const color = this.getPieceColor(piece);
        
        switch (pieceType) {
            case 'p':
                return this.canPawnAttackSquare(board, fromRow, fromCol, toRow, toCol, color);
            case 'r':
                return this.canRookAttackSquare(board, fromRow, fromCol, toRow, toCol);
            case 'n':
                return this.canKnightAttackSquare(board, fromRow, fromCol, toRow, toCol);
            case 'b':
                return this.canBishopAttackSquare(board, fromRow, fromCol, toRow, toCol);
            case 'q':
                return this.canQueenAttackSquare(board, fromRow, fromCol, toRow, toCol);
            case 'k':
                return this.canKingAttackSquare(board, fromRow, fromCol, toRow, toCol);
            default:
                return false;
        }
    }
    
    canPawnAttackSquare(board, fromRow, fromCol, toRow, toCol, color) {
        const direction = color === 'white' ? -1 : 1;
        const rowDiff = toRow - fromRow;
        const colDiff = Math.abs(toCol - fromCol);
        
        // Pawns attack diagonally
        return rowDiff === direction && colDiff === 1;
    }
    
    canRookAttackSquare(board, fromRow, fromCol, toRow, toCol) {
        if (fromRow !== toRow && fromCol !== toCol) return false;
        return this.isPathClearForAttack(board, fromRow, fromCol, toRow, toCol);
    }
    
    canKnightAttackSquare(board, fromRow, fromCol, toRow, toCol) {
        const rowDiff = Math.abs(toRow - fromRow);
        const colDiff = Math.abs(toCol - fromCol);
        return (rowDiff === 2 && colDiff === 1) || (rowDiff === 1 && colDiff === 2);
    }
    
    canBishopAttackSquare(board, fromRow, fromCol, toRow, toCol) {
        if (Math.abs(toRow - fromRow) !== Math.abs(toCol - fromCol)) return false;
        return this.isPathClearForAttack(board, fromRow, fromCol, toRow, toCol);
    }
    
    canQueenAttackSquare(board, fromRow, fromCol, toRow, toCol) {
        return this.canRookAttackSquare(board, fromRow, fromCol, toRow, toCol) || 
               this.canBishopAttackSquare(board, fromRow, fromCol, toRow, toCol);
    }
    
    canKingAttackSquare(board, fromRow, fromCol, toRow, toCol) {
        const rowDiff = Math.abs(toRow - fromRow);
        const colDiff = Math.abs(toCol - fromCol);
        return rowDiff <= 1 && colDiff <= 1;
    }
    
    isPathClearForAttack(board, fromRow, fromCol, toRow, toCol) {
        const rowDir = toRow > fromRow ? 1 : toRow < fromRow ? -1 : 0;
        const colDir = toCol > fromCol ? 1 : toCol < fromCol ? -1 : 0;
        
        let currentRow = fromRow + rowDir;
        let currentCol = fromCol + colDir;
        
        while (currentRow !== toRow || currentCol !== toCol) {
            if (board[currentRow][currentCol]) return false;
            currentRow += rowDir;
            currentCol += colDir;
        }
        
        return true;
    }
    
    isMoveValidInCheck(board, fromRow, fromCol, toRow, toCol) {
        // Make a temporary move
        const tempBoard = board.map(row => [...row]);
        const capturedPiece = tempBoard[toRow][toCol];
        tempBoard[toRow][toCol] = tempBoard[fromRow][fromCol];
        tempBoard[fromRow][fromCol] = null;
        
        // Check if the move gets the king out of check
        const pieceColor = this.getPieceColor(board[fromRow][fromCol]);
        const isStillInCheck = this.isKingInCheck(tempBoard, pieceColor);
        
        return !isStillInCheck;
    }
    
    isCheckmate(board, color) {
        if (!this.isKingInCheck(board, color)) return false;
        
        // Check if any move can get the king out of check
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const piece = board[row][col];
                if (piece && this.getPieceColor(piece) === color) {
                    const moves = this.getPossibleMovesForBoard(board, row, col);
                    for (const move of moves) {
                        if (this.isMoveValidInCheck(board, row, col, move.row, move.col)) {
                            return false; // Found a valid move
                        }
                    }
                }
            }
        }
        
        return true; // No valid moves found
    }

    undoMove() {
        if (this.gameHistory.length === 0) return;
        
        const lastMove = this.gameHistory.pop();
        this.board = lastMove.board;
        this.currentPlayer = this.currentPlayer === 'white' ? 'black' : 'white';
        
        // Restore captured pieces
        if (lastMove.capturedPiece) {
            const capturedColor = this.getPieceColor(lastMove.capturedPiece);
            const oppositeColor = capturedColor === 'white' ? 'black' : 'white';
            this.capturedPieces[oppositeColor].pop();
        }
        
        this.createChessboard();
        this.updateCheckState();
        this.updateGameStatus();
        this.updateCapturedPieces();
        this.gameOver = false;
    }
    
    checkGameEnd() {
        // Check for checkmate first
        if (this.checkState && this.checkState.isCheckmate) {
            const winner = this.currentPlayer === 'white' ? 'Black' : 'White';
            this.endGame(`${winner} wins!`, 'Checkmate!');
            return;
        }
        
        // Simple game end check - if king is captured
        let whiteKing = false, blackKing = false;
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const piece = this.board[row][col];
                if (piece && piece.toLowerCase() === 'k') {
                    if (this.getPieceColor(piece) === 'white') whiteKing = true;
                    else blackKing = true;
                }
            }
        }
        
        if (!whiteKing) {
            this.endGame('Black wins!', 'The white king has been captured.');
        } else if (!blackKing) {
            this.endGame('White wins!', 'The black king has been captured.');
        }
    }
    
    endGame(title, message) {
        this.gameOver = true;
        document.getElementById('game-result-title').textContent = title;
        document.getElementById('game-result-message').textContent = message;
        document.getElementById('game-over-modal').style.display = 'flex';
    }
    
    updateCheckState() {
        // Check if current player is in check
        const isInCheck = this.isKingInCheck(this.board, this.currentPlayer);
        const isCheckmate = isInCheck && this.isCheckmate(this.board, this.currentPlayer);
        
        this.checkState = {
            isInCheck,
            isCheckmate,
            color: this.currentPlayer
        };
    }
    
    updateGameStatus() {
        const status = document.getElementById('game-status');
        const whiteDetails = document.getElementById('white-details');
        const blackDetails = document.getElementById('black-details');
        
        whiteDetails.classList.toggle('active', this.currentPlayer === 'white');
        blackDetails.classList.toggle('active', this.currentPlayer === 'black');
        
        // 重置状态栏样式和类
        status.style.background = 'rgba(0, 0, 0, 0.6)';
        status.classList.remove('check-warning', 'checkmate-warning');
        
        let statusText = '';
        
        // Check for checkmate first
        if (this.checkState && this.checkState.isCheckmate) {
            const winner = this.currentPlayer === 'white' ? 'Black' : 'White';
            statusText = `Checkmate! ${winner} wins!`;
            status.style.background = 'rgba(220, 38, 38, 0.8)'; // Red background for checkmate
            status.classList.add('checkmate-warning');
        }
        // Check for check
        else if (this.checkState && this.checkState.isInCheck) {
            if (this.gameMode === 'vs-ai') {
                const isPlayerInCheck = this.currentPlayer === this.playerColor;
                statusText = isPlayerInCheck ? 'You are in CHECK!' : 'AI is in CHECK!';
            } else {
                statusText = `${this.currentPlayer.charAt(0).toUpperCase() + this.currentPlayer.slice(1)} is in CHECK!`;
            }
            status.style.background = 'rgba(239, 68, 68, 0.8)'; // Red background for check
            status.classList.add('check-warning');
        }
        // Normal turn
        else {
            if (this.gameMode === 'vs-ai') {
                const isPlayerTurn = this.currentPlayer === this.playerColor;
                statusText = isPlayerTurn ? 'Your turn' : 'AI\'s turn';
            } else {
                statusText = `${this.currentPlayer.charAt(0).toUpperCase() + this.currentPlayer.slice(1)} to move`;
            }
        }
        
        status.textContent = statusText;
    }
    
    newGame() {
        this.board = this.initializeBoard();
        this.currentPlayer = 'white';
        this.selectedSquare = null;
        this.gameHistory = [];
        this.capturedPieces = { white: [], black: [] };
        this.gameOver = false;
        this.checkState = null;
        
        // 确保玩家信息样式正确
        this.updatePlayerInfoStyles();
        
        this.createChessboard();
        this.updateCheckState();
        this.updateGameStatus();
        this.updateCapturedPieces();
        
        document.getElementById('game-over-modal').style.display = 'none';
        
        // 强制刷新布局
        setTimeout(() => {
            this.updatePlayerInfoStyles();
        }, 100);
        
        // Check if AI should move first (when player is black)
        if (this.gameMode === 'vs-ai' && this.currentPlayer !== this.playerColor) {
            setTimeout(() => this.makeAIMove(), 500);
        }
    }

    updatePlayerInfoStyles() {
        const chessContainer = document.querySelector('.chess-container');
        const whitePlayer = document.getElementById('white-player');
        const blackPlayer = document.getElementById('black-player');
        
        // 移除之前的类
        chessContainer.classList.remove('player-black', 'player-white');
        whitePlayer.classList.remove('player-self', 'player-opponent');
        blackPlayer.classList.remove('player-self', 'player-opponent');
        
        if (this.playerColor === 'black') {
            chessContainer.classList.add('player-black');
            blackPlayer.classList.add('player-self');
            whitePlayer.classList.add('player-opponent');
            
            // 直接设置CSS order，确保布局正确
            whitePlayer.style.order = '0'; // AI在上方
            blackPlayer.style.order = '2'; // 玩家在下方
            
            console.log('设置为黑棋玩家，AI(白棋)在上方，玩家(黑棋)在下方');
        } else {
            chessContainer.classList.add('player-white');
            whitePlayer.classList.add('player-self');
            blackPlayer.classList.add('player-opponent');
            
            // 直接设置CSS order，确保布局正确
            whitePlayer.style.order = '2'; // 玩家在下方
            blackPlayer.style.order = '0'; // AI在上方
            
            console.log('设置为白棋玩家，玩家(白棋)在下方，AI(黑棋)在上方');
        }
        
        // 调试信息
        console.log('Chess container classes:', chessContainer.className);
        console.log('Player color:', this.playerColor);
        console.log('White player order:', whitePlayer.style.order);
        console.log('Black player order:', blackPlayer.style.order);
    }
    
    setupEventListeners() {
        // Game controls
        document.getElementById('new-game-btn').addEventListener('click', () => this.newGame());
        document.getElementById('undo-btn').addEventListener('click', () => this.undoMove());
        document.getElementById('settings-btn').addEventListener('click', () => this.showSettings());
        
        // Modal controls
        document.getElementById('play-again-btn').addEventListener('click', () => this.newGame());
        document.getElementById('main-menu-btn').addEventListener('click', () => this.showSettings());
        
        // Fullscreen
        document.getElementById('fullscreen-button').addEventListener('click', this.toggleFullscreen);
    }
    
    showSettings() {
        document.getElementById('game-settings').style.display = 'block';
        document.getElementById('chessboard-container').style.display = 'none';
        document.getElementById('white-player').style.display = 'none';
        document.getElementById('black-player').style.display = 'none';
        document.getElementById('game-status').style.display = 'none';
        document.getElementById('game-controls').style.display = 'none';
        document.getElementById('game-over-modal').style.display = 'none';
    }
    
    hideSettings() {
        document.getElementById('game-settings').style.display = 'none';
        document.getElementById('chessboard-container').style.display = 'block';
        document.getElementById('white-player').style.display = 'flex';
        document.getElementById('black-player').style.display = 'flex';
        document.getElementById('game-status').style.display = 'block';
        document.getElementById('game-controls').style.display = 'flex';
        
        // 根据玩家颜色调整容器类名和玩家信息样式
        this.updatePlayerInfoStyles();
        
        // Initialize the chessboard when hiding settings
        this.createChessboard();
        this.updateCheckState();
        this.updateGameStatus();
    }
    
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log(`Error attempting to enable fullscreen: ${err.message}`);
            });
        } else {
            document.exitFullscreen();
        }
    }
}

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const game = new ChessGame();
    
    // Read initial settings from HTML
    function readInitialSettings() {
        const activeModeBtn = document.querySelector('[data-mode].active');
        const activeDifficultyBtn = document.querySelector('[data-difficulty].active');
        const activeColorBtn = document.querySelector('[data-color].active');
        
        if (activeModeBtn) {
            game.gameMode = activeModeBtn.dataset.mode;
        }
        if (activeDifficultyBtn) {
            game.aiDifficulty = activeDifficultyBtn.dataset.difficulty;
        }
        if (activeColorBtn) {
            if (activeColorBtn.dataset.color === 'random') {
                game.playerColor = Math.random() < 0.5 ? 'white' : 'black';
            } else {
                game.playerColor = activeColorBtn.dataset.color;
            }
        }
    }
    
    readInitialSettings();
    
    // Settings event listeners
    document.querySelectorAll('.setting-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const group = this.closest('.setting-group');
            group.querySelectorAll('.setting-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Update game settings
            if (this.dataset.mode) {
                game.gameMode = this.dataset.mode;
                const difficultyGroup = document.getElementById('difficulty-setting');
                difficultyGroup.style.display = this.dataset.mode === 'vs-ai' ? 'block' : 'none';
            }
            if (this.dataset.difficulty) {
                game.aiDifficulty = this.dataset.difficulty;
            }
            if (this.dataset.color) {
                if (this.dataset.color === 'random') {
                    game.playerColor = Math.random() < 0.5 ? 'white' : 'black';
                } else {
                    game.playerColor = this.dataset.color;
                }
            }
        });
    });
    
    // Start game button
    document.getElementById('start-game-btn').addEventListener('click', function() {
        // Update player names based on player color and game mode
        const isVsAI = game.gameMode === 'vs-ai';
        
        if (game.playerColor === 'white') {
            document.getElementById('white-name').textContent = 'You';
            document.getElementById('black-name').textContent = isVsAI ? 'AI' : 'Player 2';
        } else if (game.playerColor === 'black') {
            document.getElementById('black-name').textContent = 'You';
            document.getElementById('white-name').textContent = isVsAI ? 'AI' : 'Player 2';
        }
        
        game.hideSettings();
        game.newGame();
    });
    
    // Rotation check for mobile
    function checkOrientation() {
        const rotatePrompt = document.getElementById('rotate-prompt');
        const chessContainer = document.querySelector('.chess-container');
        
        if (window.innerWidth <= 1024 && window.innerHeight > window.innerWidth) {
            rotatePrompt.style.display = 'flex';
            chessContainer.style.display = 'none';
        } else {
            rotatePrompt.style.display = 'none';
            chessContainer.style.display = 'flex';
        }
    }
    
    window.addEventListener('resize', checkOrientation);
    window.addEventListener('orientationchange', checkOrientation);
    checkOrientation();
}); 