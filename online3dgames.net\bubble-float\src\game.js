// Bubble Float UI Controller - Additional enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        switch(e.key) {
            case ' ':
                e.preventDefault();
                document.getElementById('pause-btn').click();
                break;
            case 'r':
            case 'R':
                document.getElementById('reset-btn').click();
                break;
            case 'f':
            case 'F':
                document.getElementById('fullscreen-btn').click();
                break;
            case '1':
                document.getElementById('classic-mode').click();
                break;
            case '2':
                document.getElementById('colorful-mode').click();
                break;
            case '3':
                document.getElementById('neon-mode').click();
                break;
            case '4':
                document.getElementById('gentle-mode').click();
                break;
        }
    });
    
    // Add visual feedback for controls
    document.querySelectorAll('.control-btn, .mode-btn').forEach(btn => {
        btn.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(-2px) scale(0.95)';
        });
        
        btn.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-2px) scale(1)';
        });
        
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Handle fullscreen changes
    function handleFullscreenChange() {
        const btn = document.getElementById('fullscreen-btn');
        const isFullscreen = document.fullscreenElement ||
                           document.webkitFullscreenElement ||
                           document.mozFullScreenElement;

        if (isFullscreen) {
            btn.textContent = '🪟 Exit Fullscreen';
            // Ensure SVG resizes properly in fullscreen
            const svg = document.getElementById('bubble-svg');
            svg.setAttribute('width', '100vw');
            svg.setAttribute('height', '100vh');
        } else {
            btn.textContent = '🖥️ Fullscreen';
            // Reset SVG size
            const svg = document.getElementById('bubble-svg');
            svg.setAttribute('width', '100%');
            svg.setAttribute('height', '100%');
        }
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
});
