// Game Recommendations System
class GameRecommendations {
    constructor() {
        this.gameCategories = {
            blackjack: {
                name: 'Blackjack Zone',
                games: ['blackjack', 'blackjack-practice', 'free-bet-blackjack', 'pontoon']
            },
            'card-games': {
                name: 'Card Games',
                games: ['hearts', 'solitaire', 'spider-solitaire', 'texas-holdem', 'spades', 'chess']
            },
            puzzle: {
                name: 'Puzzle Games',
                games: ['sudoku', 'puzzle2048', 'tetris', 'memory']
            },
            arcade: {
                name: 'Classic Arcade',
                games: ['snake', 'breakout', 'reaction']
            },
            relaxation: {
                name: 'Relaxation Games',
                games: ['popit', 'spinner', 'breathing', 'drawing', 'particles', 'typing', 'bubbles', 'pet']
            }
        };

        this.gameData = {
            'blackjack': { name: 'Blackjack', icon: '🎰', rating: '4.9', category: 'blackjack', url: '/blackjack' },
            'blackjack-practice': { name: 'Blackjack Practice', icon: '🎯', rating: '4.7', category: 'blackjack', url: '/blackjack-practice' },
            'free-bet-blackjack': { name: 'Free Bet Blackjack', icon: '🆓', rating: '4.6', category: 'blackjack', url: '/freeBetBlackjack' },
            'pontoon': { name: 'Pontoon', icon: '🚤', rating: '4.5', category: 'blackjack', url: '/pontoon-game' },
            'hearts': { name: 'Hearts', icon: '♥️', rating: '4.8', category: 'card-games', url: '/hearts' },
            'solitaire': { name: 'Solitaire', icon: '🃏', rating: '4.6', category: 'card-games', url: '/solitaire' },
            'spider-solitaire': { name: 'Spider Solitaire', icon: '🕷️', rating: '4.5', category: 'card-games', url: '/spider-solitaire' },
            'texas-holdem': { name: 'Texas Hold\'em', icon: '🎲', rating: '4.5', category: 'card-games', url: '/texas-holdem-game' },
            'spades': { name: 'Spades', icon: '♠️', rating: '4.9', category: 'card-games', url: '/spades' },
            'chess': { name: 'Chess', icon: '♔', rating: '4.8', category: 'card-games', url: '/chess' },
            'sudoku': { name: 'Sudoku', icon: '🧩', rating: '4.8', category: 'puzzle', url: '/sudoku-game' },
            'puzzle2048': { name: '2048', icon: '🔢', rating: '4.7', category: 'puzzle', url: '/2048' },
            'tetris': { name: 'Tetris', icon: '🟦', rating: '4.9', category: 'puzzle', url: '/tetris-game' },
            'memory': { name: 'Memory Game', icon: '🧠', rating: '4.6', category: 'puzzle', url: '/memoryGame' },
            'snake': { name: 'Snake', icon: '🐍', rating: '4.7', category: 'arcade', url: '/snake-game' },
            'breakout': { name: 'Breakout', icon: '🧱', rating: '4.6', category: 'arcade', url: '/breakout-game' },
            'reaction': { name: 'Reaction Test', icon: '⚡', rating: '4.5', category: 'arcade', url: '/reaction-test' },
            'popit': { name: 'Pop-it', icon: '🫧', rating: '4.4', category: 'relaxation', url: '/pop-it-game' },
            'spinner': { name: 'Fidget Spinner', icon: '🌀', rating: '4.3', category: 'relaxation', url: '/fidget-spinner' },
            'breathing': { name: 'Breathing Ball', icon: '🌈', rating: '4.5', category: 'relaxation', url: '/breathing-ball' },
            'drawing': { name: 'Drawing Wall', icon: '🎨', rating: '4.4', category: 'relaxation', url: '/drawing-wall' },
            'particles': { name: 'Particle Trail', icon: '✨', rating: '4.3', category: 'relaxation', url: '/particle-trail' },
            'typing': { name: 'Typing Machine', icon: '⌨️', rating: '4.2', category: 'relaxation', url: '/typing-machine' },
            'bubbles': { name: 'Bubble Float', icon: '💭', rating: '4.4', category: 'relaxation', url: '/bubble-float' },
            'pet': { name: 'Virtual Pet', icon: '🐱', rating: '4.5', category: 'relaxation', url: '/virtual-pet' }
        };
    }

    // Get current game ID from URL
    getCurrentGameId() {
        const path = window.location.pathname;
        const segments = path.split('/').filter(segment => segment);
        
        if (segments.length === 0) return null;
        
        const gameId = segments[0];
        
        // Map URL paths to game IDs
        const urlMapping = {
            'blackjack': 'blackjack',
            'blackjack-practice': 'blackjack-practice',
            'freeBetBlackjack': 'free-bet-blackjack',
            'pontoon-game': 'pontoon',
            'hearts': 'hearts',
            'solitaire': 'solitaire',
            'spider-solitaire': 'spider-solitaire',
            'texas-holdem-game': 'texas-holdem',
            'spades': 'spades',
            'chess': 'chess',
            'sudoku-game': 'sudoku',
            '2048-game': 'puzzle2048',
            'tetris-game': 'tetris',
            'memoryGame': 'memory',
            'snake-game': 'snake',
            'breakout-game': 'breakout',
            'reaction-test': 'reaction',
            'pop-it-game': 'popit',
            'fidget-spinner': 'spinner',
            'breathing-ball': 'breathing',
            'drawing-wall': 'drawing',
            'particle-trail': 'particles',
            'typing-machine': 'typing',
            'bubble-float': 'bubbles',
            'virtual-pet': 'pet'
        };
        
        return urlMapping[gameId] || null;
    }

    // Get category for a game
    getGameCategory(gameId) {
        for (const [categoryId, category] of Object.entries(this.gameCategories)) {
            if (category.games.includes(gameId)) {
                return categoryId;
            }
        }
        return null;
    }

    // Get similar games in the same category (excluding current game)
    getSimilarGames(currentGameId, maxCount = 3) {
        const category = this.getGameCategory(currentGameId);
        if (!category) return [];

        const categoryGames = this.gameCategories[category].games;
        const similarGames = categoryGames
            .filter(gameId => gameId !== currentGameId && this.gameData[gameId])
            .map(gameId => this.gameData[gameId])
            .slice(0, maxCount);

        return similarGames;
    }

    // Get games from other categories (excluding current game's category)
    getOtherGames(currentGameId, maxCount = 12) {
        const currentCategory = this.getGameCategory(currentGameId);
        if (!currentCategory) return [];

        const otherGames = [];
        const allCategories = Object.keys(this.gameCategories);

        // Get all games from other categories
        for (const categoryId of allCategories) {
            if (categoryId !== currentCategory) {
                const categoryGames = this.gameCategories[categoryId].games;
                const availableGames = categoryGames
                    .filter(gameId => gameId !== currentGameId && this.gameData[gameId])
                    .map(gameId => this.gameData[gameId]);

                otherGames.push(...availableGames);
            }
        }

        // Shuffle all other games for variety
        const shuffledOtherGames = otherGames.sort(() => Math.random() - 0.5);

        return shuffledOtherGames.slice(0, maxCount);
    }

    // Generate recommendations HTML
    generateRecommendationsHTML(currentGameId) {
        const similarGames = this.getSimilarGames(currentGameId);
        const otherGames = this.getOtherGames(currentGameId);

        if (similarGames.length === 0 && otherGames.length === 0) {
            return '';
        }

        const category = this.getGameCategory(currentGameId);
        const categoryName = this.gameCategories[category]?.name || 'Similar Games';

        let html = '';

        // Similar games section
        if (similarGames.length > 0) {
            html += `
                <div class="game-recommendations similar-games">
                    <div class="recommendations-header">
                        <h3 class="recommendations-title">More ${categoryName}</h3>
                        <p class="recommendations-subtitle">Continue exploring similar games</p>
                    </div>
                    <div class="recommendations-grid">
            `;

            const similarRows = this.arrangeInHoneycombRows(similarGames);

            similarRows.forEach(row => {
                const offsetClass = row.isOffset ? ' offset-row' : '';
                html += `<div class="recommendations-row${offsetClass}">`;
                row.games.forEach(game => {
                    html += `
                        <a href="${game.url}" class="recommendation-card" data-category="${game.category}">
                            <div class="card-image">
                                <div class="game-icon">${game.icon}</div>
                                <h4 class="game-name">${game.name}</h4>
                                <div class="game-rating">⭐ ${game.rating}</div>
                            </div>
                        </a>
                    `;
                });
                html += '</div>';
            });

            html += `
                    </div>
                </div>
            `;
        }

        // Other games section
        if (otherGames.length > 0) {
            html += `
                <div class="game-recommendations other-games">
                    <div class="recommendations-header">
                        <h3 class="recommendations-title">Explore Other Games</h3>
                        <p class="recommendations-subtitle">Discover different types of entertainment</p>
                    </div>
                    <div class="recommendations-grid">
            `;

            const otherRows = this.arrangeInHoneycombRows(otherGames);

            otherRows.forEach(row => {
                const offsetClass = row.isOffset ? ' offset-row' : '';
                html += `<div class="recommendations-row${offsetClass}">`;
                row.games.forEach(game => {
                    html += `
                        <a href="${game.url}" class="recommendation-card" data-category="${game.category}">
                            <div class="card-image">
                                <div class="game-icon">${game.icon}</div>
                                <h4 class="game-name">${game.name}</h4>
                                <div class="game-rating">⭐ ${game.rating}</div>
                            </div>
                        </a>
                    `;
                });
                html += '</div>';
            });

            html += `
                    </div>
                </div>
            `;
        }

        return html;
    }

    // Arrange games in honeycomb pattern with proper offset
    arrangeInHoneycombRows(games) {
        const rows = [];

        if (games.length === 0) {
            return rows;
        }

        // True honeycomb pattern: alternating rows with offset
        // Even rows (0, 2, 4...): more items
        // Odd rows (1, 3, 5...): fewer items, offset to fit between
        let currentIndex = 0;

        while (currentIndex < games.length) {
            const isEvenRow = rows.length % 2 === 0;
            const remainingGames = games.length - currentIndex;
            let rowSize;

            if (remainingGames <= 2) {
                // Last few games, put them all in one row
                rowSize = remainingGames;
            } else if (remainingGames <= 4) {
                // For 3-4 remaining games
                rowSize = isEvenRow ? Math.ceil(remainingGames / 2) : Math.floor(remainingGames / 2);
            } else {
                // Standard honeycomb pattern
                if (isEvenRow) {
                    // Even rows: 4 items
                    rowSize = Math.min(4, remainingGames);
                } else {
                    // Odd rows: 3 items (offset between even row items)
                    rowSize = Math.min(3, remainingGames);
                }
            }

            const endIndex = Math.min(currentIndex + rowSize, games.length);
            const rowGames = games.slice(currentIndex, endIndex);

            // Add offset class for odd rows
            rows.push({
                games: rowGames,
                isOffset: !isEvenRow
            });

            currentIndex = endIndex;
        }

        return rows;
    }

    // Insert recommendations into page
    insertRecommendations(targetSelector = '.seo-content-section') {
        // Skip dynamic insertion since we now have static HTML for SEO
        // Static recommendations are already present in each game page HTML
        console.log('Using static game recommendations for SEO optimization');
        
        // Remove any existing dynamic recommendations to avoid duplicates
        const existingRecommendations = document.querySelectorAll('.game-recommendations[data-dynamic="true"]');
        existingRecommendations.forEach(element => element.remove());
    }

    // Initialize recommendations
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.insertRecommendations();
            });
        } else {
            this.insertRecommendations();
        }
    }
}

// Auto-initialize when script loads
const gameRecommendations = new GameRecommendations();
gameRecommendations.init();

// Export for manual use
window.GameRecommendations = GameRecommendations;
