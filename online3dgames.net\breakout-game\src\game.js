// Breakout Game UI Controller
let game;

document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('game-canvas');
    game = new BreakoutGame(canvas);
    
    // Initialize the game display
    game.draw();
    
    // Button event listeners
    document.getElementById('start-btn').addEventListener('click', startGame);
    document.getElementById('canvas-start-btn').addEventListener('click', startGame);
    document.getElementById('pause-btn').addEventListener('click', togglePause);
    document.getElementById('reset-btn').addEventListener('click', resetGame);
    document.getElementById('restart-btn').addEventListener('click', restartGame);
    document.getElementById('next-level-btn').addEventListener('click', nextLevel);
    document.getElementById('home-btn').addEventListener('click', goHome);
    
    // Close game over modal when clicking outside
    document.getElementById('game-over').addEventListener('click', function(e) {
        if (e.target === this) {
            closeGameOver();
        }
    });
});

function startGame() {
    if (game.gameRunning) return;

    // Hide start overlay
    document.getElementById('game-start').classList.add('hidden');

    game.start();
    updateButtonStates();
}

function togglePause() {
    if (!game.gameRunning) return;
    
    game.togglePause();
    updateButtonStates();
}

function resetGame() {
    game.reset();
    closeGameOver();
    showStartOverlay();
    updateButtonStates();
}

function restartGame() {
    game.reset();
    closeGameOver();
    showStartOverlay();
}

function showStartOverlay() {
    document.getElementById('game-start').classList.remove('hidden');
}

function nextLevel() {
    closeGameOver();
    startGame();
}

function goHome() {
    window.location.href = '/';
}

function closeGameOver() {
    document.getElementById('game-over').classList.add('hidden');
}

function updateButtonStates() {
    const startBtn = document.getElementById('start-btn');
    const pauseBtn = document.getElementById('pause-btn');
    const resetBtn = document.getElementById('reset-btn');
    
    if (game.gameRunning && !game.gamePaused) {
        startBtn.disabled = true;
        startBtn.textContent = 'Game Running...';
        pauseBtn.disabled = false;
        pauseBtn.textContent = 'Pause';
        resetBtn.disabled = false;
    } else if (game.gameRunning && game.gamePaused) {
        startBtn.disabled = true;
        startBtn.textContent = 'Game Running...';
        pauseBtn.disabled = false;
        pauseBtn.textContent = 'Continue';
        resetBtn.disabled = false;
    } else {
        startBtn.disabled = false;
        startBtn.textContent = 'Start Game';
        pauseBtn.disabled = true;
        pauseBtn.textContent = 'Pause';
        resetBtn.disabled = false;
    }
}

// Handle window resize
window.addEventListener('resize', function() {
    if (game) {
        game.draw();
    }
});

// Prevent default behavior for arrow keys and space
document.addEventListener('keydown', function(e) {
    if (['ArrowLeft', 'ArrowRight', ' '].includes(e.key)) {
        e.preventDefault();
    }
});

// Touch controls for mobile
let touchStartX = 0;
let touchCurrentX = 0;

document.addEventListener('touchstart', function(e) {
    touchStartX = e.touches[0].clientX;
    touchCurrentX = touchStartX;
});

document.addEventListener('touchmove', function(e) {
    e.preventDefault();
    touchCurrentX = e.touches[0].clientX;
    
    if (game && game.gameRunning && !game.gamePaused) {
        const canvas = document.getElementById('game-canvas');
        const rect = canvas.getBoundingClientRect();
        const canvasX = touchCurrentX - rect.left;
        
        // Scale touch position to canvas coordinates
        const scaleX = canvas.width / rect.width;
        game.mouseX = canvasX * scaleX;
    }
});

document.addEventListener('touchend', function(e) {
    touchCurrentX = 0;
});
