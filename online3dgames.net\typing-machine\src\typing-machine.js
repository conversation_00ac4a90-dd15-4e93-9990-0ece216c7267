// Typing Machine Game Logic
class TypingMachine {
    constructor() {
        this.gameStarted = false;
        
        // Audio settings
        this.audioContext = null;
        this.volume = 0.7;
        this.currentSoundType = 'mechanical';
        this.initAudio();
        
        // Typing stats
        this.charCount = 0;
        this.wordCount = 0;
        this.startTime = 0;
        this.typingTime = 0;
        
        // Current mode
        this.currentMode = 'free';
        
        // Prompts for different modes
        this.prompts = {
            zen: [
                "Focus on the present moment, feel the rhythm of each keypress...",
                "Let your thoughts flow like water, without striving for perfection...",
                "In this quiet space, only you and the words...",
                "Breathe, relax, let creativity flow naturally..."
            ],
            story: [
                "There was a mysterious forest, where...",
                "It was a rainy night, when suddenly a knock came at the door...",
                "She opened the old book, and found it written inside...",
                "The time machine started, and he found himself in..."
            ],
            code: [
                "// Write a simple function\nfunction hello() {\n    \n}",
                "// Create an array and iterate\nconst items = [];\n",
                "// Define a class\nclass MyClass {\n    constructor() {\n        \n    }\n}",
                "// Async function example\nasync function fetchData() {\n    \n}"
            ]
        };
        
        this.setupEventListeners();
        this.updateStats();
    }
    
    initAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Web Audio API not supported');
        }
    }
    
    playKeySound() {
        if (!this.audioContext) return;
        
        const now = this.audioContext.currentTime;
        
        switch (this.currentSoundType) {
            case 'mechanical':
                this.playMechanicalSound(now);
                break;
            case 'typewriter':
                this.playTypewriterSound(now);
                break;
            case 'soft':
                this.playSoftSound(now);
                break;
            case 'click':
                this.playClickSound(now);
                break;
        }
    }
    
    playMechanicalSound(now) {
        // Create mechanical keyboard sound
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        const filter = this.audioContext.createBiquadFilter();
        
        osc.connect(filter);
        filter.connect(gain);
        gain.connect(this.audioContext.destination);
        
        osc.frequency.setValueAtTime(800 + Math.random() * 400, now);
        osc.frequency.exponentialRampToValueAtTime(200, now + 0.05);
        
        filter.type = 'bandpass';
        filter.frequency.setValueAtTime(1000, now);
        
        gain.gain.setValueAtTime(this.volume * 0.3, now);
        gain.gain.exponentialRampToValueAtTime(0.001, now + 0.1);
        
        osc.start(now);
        osc.stop(now + 0.1);
    }
    
    playTypewriterSound(now) {
        // Create typewriter sound
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.connect(gain);
        gain.connect(this.audioContext.destination);
        
        osc.frequency.setValueAtTime(300 + Math.random() * 200, now);
        osc.frequency.exponentialRampToValueAtTime(100, now + 0.08);
        
        gain.gain.setValueAtTime(this.volume * 0.4, now);
        gain.gain.exponentialRampToValueAtTime(0.001, now + 0.12);
        
        osc.start(now);
        osc.stop(now + 0.12);
    }
    
    playSoftSound(now) {
        // Create soft key sound
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        const filter = this.audioContext.createBiquadFilter();
        
        osc.connect(filter);
        filter.connect(gain);
        gain.connect(this.audioContext.destination);
        
        osc.frequency.setValueAtTime(600 + Math.random() * 300, now);
        osc.frequency.exponentialRampToValueAtTime(300, now + 0.06);
        
        filter.type = 'lowpass';
        filter.frequency.setValueAtTime(800, now);
        
        gain.gain.setValueAtTime(this.volume * 0.2, now);
        gain.gain.exponentialRampToValueAtTime(0.001, now + 0.08);
        
        osc.start(now);
        osc.stop(now + 0.08);
    }
    
    playClickSound(now) {
        // Create crisp click sound
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.connect(gain);
        gain.connect(this.audioContext.destination);
        
        osc.frequency.setValueAtTime(1500 + Math.random() * 1000, now);
        osc.frequency.exponentialRampToValueAtTime(500, now + 0.03);
        
        gain.gain.setValueAtTime(this.volume * 0.25, now);
        gain.gain.exponentialRampToValueAtTime(0.001, now + 0.05);
        
        osc.start(now);
        osc.stop(now + 0.05);
    }
    
    setupEventListeners() {
        // Typing area
        const typingArea = document.getElementById('typing-area');
        typingArea.addEventListener('input', () => this.handleTyping());
        typingArea.addEventListener('keydown', () => this.playKeySound());
        
        // Settings
        document.getElementById('keyboard-sound').addEventListener('change', (e) => {
            this.currentSoundType = e.target.value;
        });
        
        document.getElementById('volume').addEventListener('input', (e) => {
            this.volume = parseFloat(e.target.value);
            document.getElementById('volume-display').textContent = Math.round(e.target.value * 100) + '%';
        });
        
        // Mode buttons
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setMode(e.target.id.replace('-mode', ''));
                this.updateModeButtons();
            });
        });
        
        // Action buttons
        document.getElementById('clear-btn').addEventListener('click', () => this.clearText());
        document.getElementById('save-btn').addEventListener('click', () => this.saveText());
        document.getElementById('copy-btn').addEventListener('click', () => this.copyText());
        document.getElementById('new-prompt-btn').addEventListener('click', () => this.newPrompt());
        
        // Start button
        document.getElementById('canvas-start-btn').addEventListener('click', () => this.start());
        document.getElementById('home-btn').addEventListener('click', () => this.goHome());
        
        // Close start modal
        document.getElementById('game-start').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.start();
            }
        });
    }
    
    handleTyping() {
        if (!this.gameStarted) return;
        
        const text = document.getElementById('typing-area').value;
        this.charCount = text.length;
        this.wordCount = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
        
        if (this.startTime === 0 && text.length > 0) {
            this.startTime = Date.now();
        }
        
        if (this.startTime > 0) {
            this.typingTime = (Date.now() - this.startTime) / 1000 / 60; // minutes
        }
        
        this.updateStats();
    }
    
    setMode(mode) {
        this.currentMode = mode;
        this.newPrompt();
    }
    
    updateModeButtons() {
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(`${this.currentMode}-mode`).classList.add('active');
    }
    
    newPrompt() {
        const promptText = document.getElementById('prompt-text');
        
        if (this.currentMode === 'free') {
            promptText.innerHTML = '<p>Free Mode: Write whatever you want, enjoy the rhythm of typing.</p>';
        } else if (this.prompts[this.currentMode]) {
            const prompts = this.prompts[this.currentMode];
            const randomPrompt = prompts[Math.floor(Math.random() * prompts.length)];
            promptText.innerHTML = `<p><strong>${this.getModeTitle()}Prompt:</strong></p><p>${randomPrompt}</p>`;
        }
    }
    
    getModeTitle() {
        const titles = {
            zen: 'Zen Writing',
            story: 'Story Writing',
            code: 'Code Practice'
        };
        return titles[this.currentMode] || 'Free Mode';
    }
    
    clearText() {
        gameConfirm('Are you sure you want to clear all text?', 'Clear Confirm').then(result => {
            if (result) {
                document.getElementById('typing-area').value = '';
                this.charCount = 0;
                this.wordCount = 0;
                this.startTime = 0;
                this.typingTime = 0;
                this.updateStats();
            }
        });
    }
    
    saveText() {
        const text = document.getElementById('typing-area').value;
        if (text.trim() === '') {
            gameWarning('No content to save!', 'Save Failed');
            return;
        }

        const blob = new Blob([text], { type: 'text/plain' });
        const link = document.createElement('a');
        link.download = `Typing Machine Text_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
        link.href = URL.createObjectURL(blob);
        link.click();
        gameSuccess('Text saved to local!', 'Save Success');
    }
    
    copyText() {
        const text = document.getElementById('typing-area').value;
        if (text.trim() === '') {
            gameWarning('No content to copy!', 'Copy Failed');
            return;
        }

        navigator.clipboard.writeText(text).then(() => {
            gameSuccess('Text copied to clipboard!', 'Copy Success');
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            gameSuccess('Text copied to clipboard!', 'Copy Success');
        });
    }
    
    updateStats() {
        document.getElementById('char-count').textContent = this.charCount;
        document.getElementById('word-count').textContent = this.wordCount;
        document.getElementById('typing-time').textContent = this.typingTime.toFixed(1) + ' minutes';
    }
    
    start() {
        document.getElementById('game-start').classList.add('hidden');
        this.gameStarted = true;
        this.newPrompt();
        document.getElementById('typing-area').focus();
    }
    
    goHome() {
        window.location.href = '/';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new TypingMachine();
});
